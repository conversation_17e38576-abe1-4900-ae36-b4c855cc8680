# MiniMax 声音复刻 API 使用说明

## 概述

本文档介绍如何使用 MiniMax 声音复刻功能的两个核心接口：音频文件上传和声音复刻。

## 前置条件

1. 在 MiniMax 平台注册账户并充值
2. 获取 API Key 和 Group ID
3. 在配置文件中正确配置 MiniMax API 信息

## 配置说明

在 `application-dev.yml` 中添加以下配置：

```yaml
# MiniMax API配置
minimax:
  api:
    base-url: https://api.minimaxi.com
    api-key: 你的minimax_api_key
    group-id: 你的minimax_group_id
    upload-path: /v1/files/upload
    clone-path: /v1/voice_clone
```

## API 接口说明

### 1. 音频文件上传接口

**接口地址：** `POST /xiaozhi/ttsVoice/upload-audio`

**功能：** 上传音频文件到 MiniMax 平台，获取 file_id

**请求参数：**
- `file` (MultipartFile, 必填): 音频文件，支持 .wav、.mp3、.m4a 格式
- `purpose` (String, 可选): 文件用途，默认为 "voice_clone"

**响应示例：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "fileId": "file-abc123def456",
    "filename": "voice_sample.wav",
    "createdAt": 1690876543210
  }
}
```

### 2. 声音复刻接口

**接口地址：** `POST /xiaozhi/ttsVoice/clone-voice`

**功能：** 使用上传的音频文件进行声音复刻

**请求参数：**
```json
{
  "fileId": "file-abc123def456",
  "voiceId": "custom-voice-001",
  "voiceName": "我的专属音色",
  "description": "基于录音样本复刻的音色"
}
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "voiceId": "custom-voice-001",
    "status": "success",
    "createdAt": 1690876543210
  }
}
```

## 使用流程

### 完整的声音复刻流程：

1. **准备音频文件**
   - 音频格式：.wav、.mp3、.m4a
   - 建议时长：10-30秒
   - 音质要求：清晰、无噪音

2. **上传音频文件**
   ```javascript
   // 前端调用示例
   import api from '@/apis/api';
   
   const file = document.getElementById('audioFile').files[0];
   api.timbre.uploadAudioFile(file, 'voice_clone', (response) => {
     if (response.code === 0) {
       const fileId = response.data.fileId;
       console.log('文件上传成功，file_id:', fileId);
       // 继续进行声音复刻
     }
   });
   ```

3. **进行声音复刻**
   ```javascript
   const cloneParams = {
     fileId: 'file-abc123def456',
     voiceId: 'my-custom-voice-001',
     voiceName: '我的专属音色',
     description: '基于录音样本复刻的音色'
   };
   
   api.timbre.cloneVoice(cloneParams, (response) => {
     if (response.code === 0) {
       console.log('声音复刻成功，voice_id:', response.data.voiceId);
       // 可以使用该 voice_id 进行语音合成
     }
   });
   ```

4. **使用复刻的音色**
   - 复刻成功后，可以在 TTS 语音合成中使用返回的 `voiceId`
   - 将 `voiceId` 配置到相应的 TTS 模型配置中

## 错误处理

### 常见错误码：

- `400`: 请求参数错误
- `401`: API Key 无效
- `403`: 权限不足或余额不足
- `413`: 文件过大
- `415`: 不支持的文件格式
- `500`: 服务器内部错误

### 错误处理示例：

```javascript
api.timbre.uploadAudioFile(file, 'voice_clone', (response) => {
  if (response.code !== 0) {
    switch (response.code) {
      case 400:
        console.error('请求参数错误:', response.msg);
        break;
      case 401:
        console.error('API Key 无效，请检查配置');
        break;
      case 403:
        console.error('权限不足或余额不足');
        break;
      default:
        console.error('上传失败:', response.msg);
    }
  }
});
```

## 注意事项

1. **文件大小限制**：单个音频文件不超过 100MB
2. **音频质量**：建议使用高质量的音频文件以获得更好的复刻效果
3. **API 限制**：请注意 MiniMax 平台的 API 调用频率限制
4. **费用说明**：声音复刻功能会消耗 MiniMax 平台的额度
5. **权限要求**：需要超级管理员权限才能调用这些接口

## 技术实现

### 后端实现

- **配置类**：`MiniMaxProperties` - 管理 MiniMax API 配置
- **服务类**：`MiniMaxApiService` - 封装 MiniMax API 调用
- **控制器**：`TimbreController` - 提供 REST API 接口
- **DTO 类**：`AudioUploadDTO`、`VoiceCloneDTO` - 请求参数封装

### 前端实现

- **API 调用**：`timbre.js` - 封装前端 API 调用方法
- **文件上传**：支持 FormData 格式的文件上传
- **错误处理**：统一的错误处理和重试机制

## 扩展功能

后续可以考虑添加以下功能：

1. **批量上传**：支持同时上传多个音频文件
2. **进度显示**：显示文件上传和复刻进度
3. **音色管理**：提供音色列表、删除、更新等管理功能
4. **音色预览**：支持复刻后的音色试听
5. **模板管理**：保存常用的复刻参数模板
