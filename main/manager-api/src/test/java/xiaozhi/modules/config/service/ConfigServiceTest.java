package xiaozhi.modules.config.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.alibaba.fastjson2.JSONObject;

/**
 * ConfigService 测试类
 * 用于测试设备信息解析的安全性
 */
@SpringBootTest
@ActiveProfiles("test")
public class ConfigServiceTest {

    /**
     * 测试JSON解析中null值的处理
     */
    @Test
    public void testJsonParsingWithNullValues() {
        // 模拟Redis中可能存在null值的JSON数据
        String jsonWithNulls = """
            {
                "ID": null,
                "deviceId": "test-device-001",
                "deviceName": "Test Device",
                "hardwareHash": "abc123",
                "cpuInfo": "Intel i7",
                "memoryInfo": "16GB",
                "diskInfo": "512GB SSD",
                "networkInfo": "WiFi",
                "gpuInfo": "NVIDIA GTX",
                "osName": "Windows",
                "osVersion": "11",
                "osArch": "x64",
                "hostname": "test-pc",
                "username": "testuser",
                "userHomeDir": "/home/<USER>",
                "workDir": "/workspace",
                "appVersion": "1.0.0",
                "appBuildNo": "100",
                "ipAddress": "*************",
                "macAddress": "00:11:22:33:44:55",
                "userId": null,
                "status": null,
                "isActive": null,
                "reportCount": null,
                "remark": "Test device",
                "isDefault": null,
                "mcpAccessAddress": "http://localhost:8080",
                "agentId": "agent-001"
            }
            """;

        try {
            JSONObject jsonObj = JSONObject.parseObject(jsonWithNulls);
            
            // 测试安全获取方法
            long id = getLongSafely(jsonObj, "ID");
            long userId = getLongSafely(jsonObj, "userId");
            int status = getIntegerSafely(jsonObj, "status");
            boolean isActive = getBooleanSafely(jsonObj, "isActive");
            long reportCount = getLongSafely(jsonObj, "reportCount");
            boolean isDefault = getBooleanSafely(jsonObj, "isDefault");
            
            System.out.println("JSON解析测试成功:");
            System.out.println("ID: " + id);
            System.out.println("UserId: " + userId);
            System.out.println("Status: " + status);
            System.out.println("IsActive: " + isActive);
            System.out.println("ReportCount: " + reportCount);
            System.out.println("IsDefault: " + isDefault);
            
            // 验证默认值
            assert id == 0L : "ID应该为默认值0";
            assert userId == 0L : "UserId应该为默认值0";
            assert status == 0 : "Status应该为默认值0";
            assert !isActive : "IsActive应该为默认值false";
            assert reportCount == 0L : "ReportCount应该为默认值0";
            assert !isDefault : "IsDefault应该为默认值false";
            
            System.out.println("所有断言通过，null值处理正确！");
            
        } catch (Exception e) {
            System.err.println("JSON解析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 安全地从JSONObject获取Long值，避免null指针异常
     */
    private long getLongSafely(JSONObject jsonObj, String key) {
        Long value = jsonObj.getLong(key);
        return value != null ? value : 0L;
    }

    /**
     * 安全地从JSONObject获取Integer值，避免null指针异常
     */
    private int getIntegerSafely(JSONObject jsonObj, String key) {
        Integer value = jsonObj.getInteger(key);
        return value != null ? value : 0;
    }

    /**
     * 安全地从JSONObject获取Boolean值，避免null指针异常
     */
    private boolean getBooleanSafely(JSONObject jsonObj, String key) {
        Boolean value = jsonObj.getBoolean(key);
        return value != null ? value : false;
    }
}
