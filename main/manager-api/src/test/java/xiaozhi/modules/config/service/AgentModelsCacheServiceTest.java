package xiaozhi.modules.config.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 智能体模型配置缓存管理服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AgentModelsCacheServiceTest {

    @Autowired
    private AgentModelsCacheService agentModelsCacheService;

    /**
     * 测试清除智能体相关缓存
     */
    @Test
    public void testClearAgentRelatedCaches() {
        String agentId = "test-agent-001";
        
        // 清除智能体相关缓存
        agentModelsCacheService.clearAgentRelatedCaches(agentId);
        
        System.out.println("智能体相关缓存清除测试完成，智能体ID: " + agentId);
    }

    /**
     * 测试清除用户相关缓存
     */
    @Test
    public void testClearUserRelatedCaches() {
        Long userId = 1001L;
        
        // 清除用户相关缓存
        agentModelsCacheService.clearUserRelatedCaches(userId);
        
        System.out.println("用户相关缓存清除测试完成，用户ID: " + userId);
    }

    /**
     * 测试清除设备相关缓存
     */
    @Test
    public void testClearDeviceRelatedCaches() {
        String deviceId = "test-device-001";
        String macAddress = "00:11:22:33:44:55";
        
        // 清除设备相关缓存
        agentModelsCacheService.clearDeviceRelatedCaches(deviceId, macAddress);
        
        System.out.println("设备相关缓存清除测试完成，设备ID: " + deviceId + ", MAC: " + macAddress);
    }

    /**
     * 测试清除音色相关缓存
     */
    @Test
    public void testClearTimbreRelatedCaches() {
        String timbreId = "test-timbre-001";
        
        // 清除音色相关缓存
        agentModelsCacheService.clearTimbreRelatedCaches(timbreId);
        
        System.out.println("音色相关缓存清除测试完成，音色ID: " + timbreId);
    }

    /**
     * 测试清除模型相关缓存
     */
    @Test
    public void testClearModelRelatedCaches() {
        String modelId = "test-model-001";
        
        // 清除模型相关缓存
        agentModelsCacheService.clearModelRelatedCaches(modelId);
        
        System.out.println("模型相关缓存清除测试完成，模型ID: " + modelId);
    }

    /**
     * 测试清除系统参数相关缓存
     */
    @Test
    public void testClearSysParamRelatedCaches() {
        String paramCode = "device_max_output_size";
        
        // 清除系统参数相关缓存
        agentModelsCacheService.clearSysParamRelatedCaches(paramCode);
        
        System.out.println("系统参数相关缓存清除测试完成，参数代码: " + paramCode);
    }

    /**
     * 测试根据智能体ID查找MAC地址
     */
    @Test
    public void testGetMacAddressesByAgentId() {
        String agentId = "test-agent-001";
        
        var macAddresses = agentModelsCacheService.getMacAddressesByAgentId(agentId);
        
        System.out.println("智能体关联的MAC地址列表，智能体ID: " + agentId + ", MAC地址: " + macAddresses);
    }

    /**
     * 测试根据用户ID查找MAC地址
     */
    @Test
    public void testGetMacAddressesByUserId() {
        Long userId = 1001L;
        
        var macAddresses = agentModelsCacheService.getMacAddressesByUserId(userId);
        
        System.out.println("用户关联的MAC地址列表，用户ID: " + userId + ", MAC地址: " + macAddresses);
    }

    /**
     * 测试根据音色ID查找智能体ID
     */
    @Test
    public void testGetAgentIdsByTimbreId() {
        String timbreId = "test-timbre-001";
        
        var agentIds = agentModelsCacheService.getAgentIdsByTimbreId(timbreId);
        
        System.out.println("使用该音色的智能体列表，音色ID: " + timbreId + ", 智能体ID: " + agentIds);
    }

    /**
     * 测试根据模型ID查找智能体ID
     */
    @Test
    public void testGetAgentIdsByModelId() {
        String modelId = "test-model-001";
        
        var agentIds = agentModelsCacheService.getAgentIdsByModelId(modelId);
        
        System.out.println("使用该模型的智能体列表，模型ID: " + modelId + ", 智能体ID: " + agentIds);
    }

    /**
     * 测试清除指定MAC地址的智能体模型配置缓存
     */
    @Test
    public void testClearAgentModelsCacheByMac() {
        String macAddress = "00:11:22:33:44:55";
        
        // 清除指定MAC地址的智能体模型配置缓存
        agentModelsCacheService.clearAgentModelsCacheByMac(macAddress);
        
        System.out.println("指定MAC地址的智能体模型配置缓存清除测试完成，MAC地址: " + macAddress);
    }
}
