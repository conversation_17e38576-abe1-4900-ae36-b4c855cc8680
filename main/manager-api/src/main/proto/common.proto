syntax = "proto3";

option java_package = "xiaozhi.grpc.common";
option java_outer_classname = "CommonProto";

package common;

option go_package = "github.com/flipped-aurora/gin-vue-admin/server/proto/generated/common";

// 通用响应结构
message BaseResponse {
  int32 code = 1;
  string message = 2;
  bool success = 3;
}

// 分页请求
message PageRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// 分页响应
message PageResponse {
  int32 page = 1;
  int32 page_size = 2;
  int64 total = 3;
}

// 通用ID请求
message IDRequest {
  uint32 id = 1;
}

// 时间戳
message Timestamp {
  int64 seconds = 1;
  int32 nanos = 2;
} 