knife4j:
  production: false
  enable: true
  basic:
    enable: false
    username: renren
    password: 2ZABCDEUgF
  setting:
    enableFooter: false
spring:
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ************************************************************************************************************************************************************************
      username: root
      password: juezhi@@321
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      host: r-uf6k7xu4lzk0mkymg6pd.redis.rds.aliyuncs.com         # Redis服务器地址
      port: 6379              # Redis服务器连接端口
      password: juezhi@@321             # Redis服务器连接密码（默认为空）
      database: 6            # Redis数据库索引（默认为0）
      timeout: 10000ms       # 连接超时时间（毫秒）
      lettuce:
        pool:
          max-active: 8      # 连接池最大连接数（使用负值表示没有限制）
          max-idle: 8        # 连接池中的最大空闲连接
          min-idle: 0        # 连接池中的最小空闲连接
        shutdown-timeout: 100ms  # 客户端优雅关闭的等待时间

# MCPCN外部API配置
mcpcn:
  api:
    base-url: https://www.rapido.chat
    get-user-info: /api/user/getUserInfo

# MiniMax API配置
minimax:
  api:
    base-url: https://api.minimax.chat
    api-key: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    group-id: 1939118475410678679
    upload-path: /v1/files/upload
    clone-path: /v1/voice_clone
