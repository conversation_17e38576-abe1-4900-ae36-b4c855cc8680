package xiaozhi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * MiniMax API配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "minimax.api")
public class MiniMaxProperties {
    
    /**
     * API基础URL
     */
    private String baseUrl = "https://api.minimax.chat";
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 组ID
     */
    private String groupId;
    
    /**
     * 文件上传接口路径
     */
    private String uploadPath = "/v1/files/upload";
    
    /**
     * 声音复刻接口路径
     */
    private String clonePath = "/v1/voice_clone";

    /**
     * T2A V2接口路径
     */
    private String t2aV2Path = "/v1/t2a_v2";

    /**
     * 获取完整的文件上传接口URL
     */
    public String getUploadUrl() {
        return baseUrl + uploadPath;
    }

    /**
     * 获取完整的声音复刻接口URL
     */
    public String getCloneUrl() {
        return baseUrl + clonePath;
    }

    /**
     * 获取完整的T2A V2接口URL
     */
    public String getT2aV2Url() {
        return baseUrl + t2aV2Path;
    }
}
