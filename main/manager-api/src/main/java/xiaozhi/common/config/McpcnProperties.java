package xiaozhi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * MCPCN外部API配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "mcpcn.api")
public class McpcnProperties {
    
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * 获取用户信息接口路径
     */
    private String getUserInfo;
    
    /**
     * 获取完整的用户信息接口URL
     */
    public String getUserInfoUrl() {
        return baseUrl + getUserInfo;
    }
}
