package xiaozhi.modules.agent.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.common.utils.JsonUtils;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.dto.AgentCreateDTO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.dto.AgentUpdateDTO;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentPluginMapping;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentChatHistoryService;
import xiaozhi.modules.agent.service.AgentMcpAccessPointService;
import xiaozhi.modules.agent.service.AgentPluginMappingService;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.agent.service.AgentTemplateService;
import xiaozhi.modules.agent.vo.AgentInfoVO;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.model.dto.ModelProviderDTO;
import xiaozhi.modules.model.service.ModelConfigService;
import xiaozhi.modules.model.service.ModelProviderService;
import xiaozhi.modules.security.user.SecurityUser;
import xiaozhi.modules.sys.enums.SuperAdminEnum;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.config.service.AgentModelsCacheService;
import xiaozhi.modules.device.entity.DeviceEntity;

@Log4j2
@Service
@AllArgsConstructor
public class AgentServiceImpl extends BaseServiceImpl<AgentDao, AgentEntity> implements AgentService {
    private final AgentDao agentDao;
    private final TimbreService timbreModelService;
    private final ModelConfigService modelConfigService;
    private final RedisUtils redisUtils;
    private final DeviceService deviceService;
    private final AgentPluginMappingService agentPluginMappingService;
    private final AgentChatHistoryService agentChatHistoryService;
    private final AgentTemplateService agentTemplateService;
    private final ModelProviderService modelProviderService;
    private final AgentMcpAccessPointService agentMcpAccessPointService;
    private final AgentModelsCacheService agentModelsCacheService;

    @Override
    public PageData<AgentEntity> adminAgentList(Map<String, Object> params) {
        IPage<AgentEntity> page = agentDao.selectPage(
                getPage(params, "agent_name", true),
                new QueryWrapper<>());
        return new PageData<>(page.getRecords(), page.getTotal());
    }

    @Override
    public AgentInfoVO getAgentById(String id) {
        AgentInfoVO agent = agentDao.selectAgentInfoById(id);

        if (agent == null) {
            throw new RenException("智能体不存在");
        }

        if (agent.getMemModelId() != null && agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            agent.setChatHistoryConf(Constant.ChatHistoryConfEnum.IGNORE.getCode());
            if (agent.getChatHistoryConf() == null) {
                agent.setChatHistoryConf(Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode());
            }
        }
        // 新增：赋值全局声纹打断开关
        AgentEntity entity = agentDao.selectById(id);
        if (entity != null) {
            agent.setVoiceprintInterruptEnabled(entity.getVoiceprintInterruptEnabled());
        }

        // 设置MCP接入点地址
        String mcpAccessAddress = agentMcpAccessPointService.getAgentMcpAccessAddress(id);
        agent.setMcpAccessAddress(mcpAccessAddress);

        // 无需额外查询插件列表，已通过SQL查询出来
        return agent;
    }

    @Override
    public boolean insert(AgentEntity entity) {
        // 如果ID为空，自动生成一个UUID作为ID
        if (entity.getId() == null || entity.getId().trim().isEmpty()) {
            entity.setId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 如果智能体编码为空，自动生成一个带前缀的编码
        if (entity.getAgentCode() == null || entity.getAgentCode().trim().isEmpty()) {
            entity.setAgentCode("AGT_" + System.currentTimeMillis());
        }

        // 如果排序字段为空，设置默认值0
        if (entity.getSort() == null) {
            entity.setSort(0);
        }

        return super.insert(entity);
    }

    @Override
    public void deleteAgentByUserId(Long userId) {
        // 先清除相关缓存
        agentModelsCacheService.clearUserRelatedCaches(userId);

        UpdateWrapper<AgentEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", userId);
        baseDao.delete(wrapper);
    }

    @Override
    public List<AgentDTO> getUserAgents(Long userId) {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        List<AgentEntity> agents = agentDao.selectList(wrapper);
        return agents.stream().map(agent -> {
            AgentDTO dto = new AgentDTO();
            dto.setId(agent.getId());
            dto.setAgentName(agent.getAgentName());
            dto.setSystemPrompt(agent.getSystemPrompt());

            // 获取 TTS 模型名称
            dto.setTtsModelName(modelConfigService.getModelNameById(agent.getTtsModelId()));

            // 获取 LLM 模型名称
            dto.setLlmModelName(modelConfigService.getModelNameById(agent.getLlmModelId()));

            // 获取 VLLM 模型名称
            dto.setVllmModelName(modelConfigService.getModelNameById(agent.getVllmModelId()));

            // 获取记忆模型名称
            dto.setMemModelId(agent.getMemModelId());

            // 获取 TTS 音色名称
            dto.setTtsVoiceName(timbreModelService.getTimbreNameById(agent.getTtsVoiceId()));

            // 获取智能体最近的最后连接时长
            dto.setLastConnectedAt(deviceService.getLatestLastConnectionTime(agent.getId()));

            // 获取设备数量
            dto.setDeviceCount(getDeviceCountByAgentId(agent.getId()));
            // 新增：赋值全局声纹打断开关
            dto.setVoiceprintInterruptEnabled(agent.getVoiceprintInterruptEnabled());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public AgentDTO getUserAgentsWithDevice(Long userId,String deviceId) {
        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("device_id", deviceId);
        AgentEntity agent = agentDao.selectOne(wrapper);
        AgentDTO dto = new AgentDTO();
        dto.setId(agent.getId());
        dto.setAgentName(agent.getAgentName());
        dto.setSystemPrompt(agent.getSystemPrompt());

        // 获取 TTS 模型名称
        dto.setTtsModelName(modelConfigService.getModelNameById(agent.getTtsModelId()));

        // 获取 LLM 模型名称
        dto.setLlmModelName(modelConfigService.getModelNameById(agent.getLlmModelId()));

        // 获取 VLLM 模型名称
        dto.setVllmModelName(modelConfigService.getModelNameById(agent.getVllmModelId()));

        // 获取记忆模型名称
        dto.setMemModelId(agent.getMemModelId());

        // 获取 TTS 音色名称
        dto.setTtsVoiceName(timbreModelService.getTimbreNameById(agent.getTtsVoiceId()));

        // 获取智能体最近的最后连接时长
        dto.setLastConnectedAt(deviceService.getLatestLastConnectionTime(agent.getId()));

        // 获取设备数量
        dto.setDeviceCount(getDeviceCountByAgentId(agent.getId()));
        // 新增：赋值全局声纹打断开关
        dto.setVoiceprintInterruptEnabled(agent.getVoiceprintInterruptEnabled());
        return dto;
    }

    @Override
    public Integer getDeviceCountByAgentId(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return 0;
        }

        // 先从Redis中获取
        Integer cachedCount = (Integer) redisUtils.get(RedisKeys.getAgentDeviceCountById(agentId));
        if (cachedCount != null) {
            return cachedCount;
        }

        // 如果Redis中没有，则从数据库查询
        Integer deviceCount = agentDao.getDeviceCountByAgentId(agentId);

        // 将结果存入Redis
        if (deviceCount != null) {
            redisUtils.set(RedisKeys.getAgentDeviceCountById(agentId), deviceCount, 60);
        }

        return deviceCount != null ? deviceCount : 0;
    }

    @Override
    public AgentEntity getDefaultAgentByMacAddress(String macAddress) {
        if (StringUtils.isEmpty(macAddress)) {
            return null;
        }
        return agentDao.getDefaultAgentByMacAddress(macAddress);
    }

    @Override
    public AgentEntity getDefaultAgentByDeviceId(String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            return null;
        }
        return agentDao.getDefaultAgentByDeviceId(deviceId);
    }

    @Override
    public boolean checkAgentPermission(String agentId, Long userId) {
        // 获取智能体信息
        AgentEntity agent = getAgentById(agentId);
        if (agent == null) {
            return false;
        }

        // 如果是超级管理员，直接返回true
        if (SecurityUser.getUser().getSuperAdmin() == SuperAdminEnum.YES.value()) {
            return true;
        }

        // 检查是否是智能体的所有者
        return userId.equals(agent.getUserId());
    }

    @Override
    public boolean checkAgentPermissionNoAuth(String agentId, Long userId) {
        // 获取智能体信息
        AgentEntity agent = super.selectById(agentId);
        if (agent == null) {
            return false;
        }

        // 检查是否是智能体的所有者
        return userId.equals(agent.getUserId());
    }

    // 根据id更新智能体信息
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentById(String agentId, AgentUpdateDTO dto) {
        // 先查询现有实体
        AgentEntity existingEntity = this.getAgentById(agentId);
        if (existingEntity == null) {
            throw new RuntimeException("智能体不存在");
        }

        // 只更新提供的非空字段
        if (dto.getAgentName() != null) {
            existingEntity.setAgentName(dto.getAgentName());
        }
        if (dto.getAgentCode() != null) {
            existingEntity.setAgentCode(dto.getAgentCode());
        }
        if (dto.getAsrModelId() != null) {
            existingEntity.setAsrModelId(dto.getAsrModelId());
        }
        if (dto.getVadModelId() != null) {
            existingEntity.setVadModelId(dto.getVadModelId());
        }
        if (dto.getLlmModelId() != null) {
            existingEntity.setLlmModelId(dto.getLlmModelId());
        }
        if (dto.getVllmModelId() != null) {
            existingEntity.setVllmModelId(dto.getVllmModelId());
        }
        if (dto.getTtsModelId() != null) {
            existingEntity.setTtsModelId(dto.getTtsModelId());
        }
        if (dto.getTtsVoiceId() != null) {
            existingEntity.setTtsVoiceId(dto.getTtsVoiceId());
        }
        if (dto.getMemModelId() != null) {
            existingEntity.setMemModelId(dto.getMemModelId());
        }
        if (dto.getIntentModelId() != null) {
            existingEntity.setIntentModelId(dto.getIntentModelId());
        }
        if (dto.getSystemPrompt() != null) {
            existingEntity.setSystemPrompt(dto.getSystemPrompt());
        }
        if (dto.getSummaryMemory() != null) {
            existingEntity.setSummaryMemory(dto.getSummaryMemory());
        }
        if (dto.getChatHistoryConf() != null) {
            existingEntity.setChatHistoryConf(dto.getChatHistoryConf());
        }
        if (dto.getLangCode() != null) {
            existingEntity.setLangCode(dto.getLangCode());
        }
        if (dto.getLanguage() != null) {
            existingEntity.setLanguage(dto.getLanguage());
        }
        if (dto.getSort() != null) {
            existingEntity.setSort(dto.getSort());
        }

        // 更新函数插件信息
        List<AgentUpdateDTO.FunctionInfo> functions = dto.getFunctions();
        if (functions != null) {
            // 1. 收集本次提交的 pluginId
            List<String> newPluginIds = functions.stream()
                    .map(AgentUpdateDTO.FunctionInfo::getPluginId)
                    .toList();

            // 2. 查询当前agent现有的所有映射
            List<AgentPluginMapping> existing = agentPluginMappingService.list(
                    new QueryWrapper<AgentPluginMapping>()
                            .eq("agent_id", agentId));
            Map<String, AgentPluginMapping> existMap = existing.stream()
                    .collect(Collectors.toMap(AgentPluginMapping::getPluginId, Function.identity()));

            // 3. 构造所有要 保存或更新 的实体
            List<AgentPluginMapping> allToPersist = functions.stream().map(info -> {
                AgentPluginMapping m = new AgentPluginMapping();
                m.setAgentId(agentId);
                m.setPluginId(info.getPluginId());
                m.setParamInfo(JsonUtils.toJsonString(info.getParamInfo()));
                AgentPluginMapping old = existMap.get(info.getPluginId());
                if (old != null) {
                    // 已存在，设置id表示更新
                    m.setId(old.getId());
                }
                return m;
            }).toList();

            // 4. 拆分：已有ID的走更新，无ID的走插入
            List<AgentPluginMapping> toUpdate = allToPersist.stream()
                    .filter(m -> m.getId() != null)
                    .toList();
            List<AgentPluginMapping> toInsert = allToPersist.stream()
                    .filter(m -> m.getId() == null)
                    .toList();

            if (!toUpdate.isEmpty()) {
                agentPluginMappingService.updateBatchById(toUpdate);
            }
            if (!toInsert.isEmpty()) {
                agentPluginMappingService.saveBatch(toInsert);
            }

            // 5. 删除本次不在提交列表里的插件映射
            List<Long> toDelete = existing.stream()
                    .filter(old -> !newPluginIds.contains(old.getPluginId()))
                    .map(AgentPluginMapping::getId)
                    .toList();
            if (!toDelete.isEmpty()) {
                agentPluginMappingService.removeBatchByIds(toDelete);
            }
        }

        // 设置更新者信息
        UserDetail user = SecurityUser.getUser();
        existingEntity.setUpdater(user.getId());
        existingEntity.setUpdatedAt(new Date());

        // 更新记忆策略
        if (existingEntity.getMemModelId() == null || existingEntity.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            // 删除所有记录
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, true);
            existingEntity.setSummaryMemory("");
        } else if (existingEntity.getChatHistoryConf() != null && existingEntity.getChatHistoryConf() == 1) {
            // 删除音频数据
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, false);
        }
        this.updateById(existingEntity);

        // 清除相关缓存（包括可能的历史用户缓存）
        agentModelsCacheService.clearAgentUpdateCaches(agentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentByIdNoAuth(String agentId, AgentUpdateDTO dto, Long userId) {
        // 先查询现有实体
        AgentEntity existingEntity = super.selectById(agentId);
        if (existingEntity == null) {
            throw new RuntimeException("智能体不存在");
        }

        // 只更新提供的非空字段
        if (dto.getAgentName() != null) {
            existingEntity.setAgentName(dto.getAgentName());
        }
        if (dto.getAgentCode() != null) {
            existingEntity.setAgentCode(dto.getAgentCode());
        }
        if (dto.getAsrModelId() != null) {
            existingEntity.setAsrModelId(dto.getAsrModelId());
        }
        if (dto.getVadModelId() != null) {
            existingEntity.setVadModelId(dto.getVadModelId());
        }
        if (dto.getLlmModelId() != null) {
            existingEntity.setLlmModelId(dto.getLlmModelId());
        }
        if (dto.getVllmModelId() != null) {
            existingEntity.setVllmModelId(dto.getVllmModelId());
        }
        if (dto.getTtsModelId() != null) {
            existingEntity.setTtsModelId(dto.getTtsModelId());
        }
        if (dto.getTtsVoiceId() != null) {
            existingEntity.setTtsVoiceId(dto.getTtsVoiceId());
        }
        if (dto.getMemModelId() != null) {
            existingEntity.setMemModelId(dto.getMemModelId());
        }
        if (dto.getIntentModelId() != null) {
            existingEntity.setIntentModelId(dto.getIntentModelId());
        }
        if (dto.getSystemPrompt() != null) {
            existingEntity.setSystemPrompt(dto.getSystemPrompt());
        }
        if (dto.getSummaryMemory() != null) {
            existingEntity.setSummaryMemory(dto.getSummaryMemory());
        }
        if (dto.getChatHistoryConf() != null) {
            existingEntity.setChatHistoryConf(dto.getChatHistoryConf());
        }
        if (dto.getLangCode() != null) {
            existingEntity.setLangCode(dto.getLangCode());
        }
        if (dto.getLanguage() != null) {
            existingEntity.setLanguage(dto.getLanguage());
        }
        if (dto.getSort() != null) {
            existingEntity.setSort(dto.getSort());
        }
        if (dto.getVoiceprintInterruptEnabled() != null) {
            existingEntity.setVoiceprintInterruptEnabled(dto.getVoiceprintInterruptEnabled());
        }

        // 处理插件信息
        if (dto.getFunctions() != null) {
            // 先删除现有的插件映射
            agentPluginMappingService.deleteByAgentId(agentId);

            // 添加新的插件映射
            List<AgentPluginMapping> toInsert = new ArrayList<>();
            for (AgentUpdateDTO.FunctionInfo functionInfo : dto.getFunctions()) {
                AgentPluginMapping mapping = new AgentPluginMapping();
                mapping.setAgentId(agentId);
                mapping.setPluginId(functionInfo.getPluginId());
                mapping.setParamInfo(JsonUtils.toJsonString(functionInfo.getParamInfo()));
                toInsert.add(mapping);
            }

            if (!toInsert.isEmpty()) {
                agentPluginMappingService.saveBatch(toInsert);
            }
        }

        // 设置更新者信息
        existingEntity.setUpdater(userId);
        existingEntity.setUpdatedAt(new Date());

        // 更新记忆策略
        if (existingEntity.getMemModelId() == null || existingEntity.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            // 删除所有记录
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, true);
            existingEntity.setSummaryMemory("");
        } else if (existingEntity.getChatHistoryConf() != null && existingEntity.getChatHistoryConf() == 1) {
            // 删除音频数据
            agentChatHistoryService.deleteByAgentId(existingEntity.getId(), true, false);
        }
        this.updateById(existingEntity);

        // 清除相关缓存（包括可能的历史用户缓存）
        agentModelsCacheService.clearAgentUpdateCaches(agentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAgent(AgentCreateDTO dto) {
        // 转换为实体
        AgentEntity entity = ConvertUtils.sourceToTarget(dto, AgentEntity.class);

        // 获取默认模板
        AgentTemplateEntity template = agentTemplateService.getDefaultTemplate();
        if (template != null) {
            // 设置模板中的默认值
            entity.setAsrModelId(template.getAsrModelId());
            entity.setVadModelId(template.getVadModelId());
            entity.setLlmModelId(template.getLlmModelId());
            entity.setVllmModelId(template.getVllmModelId());
            entity.setTtsModelId(template.getTtsModelId());
            entity.setTtsVoiceId(template.getTtsVoiceId());
            entity.setMemModelId(template.getMemModelId());
            entity.setIntentModelId(template.getIntentModelId());
            entity.setSystemPrompt(template.getSystemPrompt());
            entity.setSummaryMemory(template.getSummaryMemory());
            entity.setChatHistoryConf(template.getChatHistoryConf());
            entity.setLangCode(template.getLangCode());
            entity.setLanguage(template.getLanguage());
        }

        // 设置用户ID和创建者信息
        UserDetail user = SecurityUser.getUser();
        entity.setUserId(user.getId());
        entity.setCreator(user.getId());
        entity.setCreatedAt(new Date());

        // 保存智能体
        insert(entity);

        // 设置默认插件
        List<AgentPluginMapping> toInsert = new ArrayList<>();
        // 播放音乐、查天气、查新闻
        String[] pluginIds = new String[] { "SYSTEM_PLUGIN_MUSIC", "SYSTEM_PLUGIN_WEATHER",
                "SYSTEM_PLUGIN_NEWS_NEWSNOW" };
        for (String pluginId : pluginIds) {
            ModelProviderDTO provider = modelProviderService.getById(pluginId);
            if (provider == null) {
                continue;
            }
            AgentPluginMapping mapping = new AgentPluginMapping();
            mapping.setPluginId(pluginId);

            Map<String, Object> paramInfo = new HashMap<>();
            List<Map<String, Object>> fields = JsonUtils.parseObject(provider.getFields(), List.class);
            if (fields != null) {
                for (Map<String, Object> field : fields) {
                    paramInfo.put((String) field.get("key"), field.get("default"));
                }
            }
            mapping.setParamInfo(JsonUtils.toJsonString(paramInfo));
            mapping.setAgentId(entity.getId());
            toInsert.add(mapping);
        }
        // 保存默认插件
        agentPluginMappingService.saveBatch(toInsert);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String assignDefaultAgentToUser(Long userId, String deviceId) {
        // 检查用户是否已经有对应设备的智能体
        QueryWrapper<AgentEntity> userWrapper = new QueryWrapper<>();
        userWrapper.eq("device_id", deviceId);
        AgentEntity existingUserAgent = agentDao.selectOne(userWrapper);

        if (existingUserAgent != null) {
            // 用户已经有对应设备的智能体，判断用户是否换绑，是的话修改用户id，直接返回
            if (existingUserAgent.getUserId() != userId) {
                Long oldUserId = existingUserAgent.getUserId();
                String agentId = existingUserAgent.getId();

                log.info("智能体换绑，从用户 {} 换绑到用户 {}，智能体ID: {}", oldUserId, userId, agentId);

                // 在数据库更新之前，先收集原用户和智能体的设备信息
                List<String> oldUserMacAddresses = new ArrayList<>();
                List<String> agentMacAddresses = new ArrayList<>();

                try {
                    // 获取原用户的所有设备MAC地址
                    List<DeviceEntity> oldUserDevices = deviceService.getUserDevices(oldUserId, null);
                    if (oldUserDevices != null) {
                        for (DeviceEntity device : oldUserDevices) {
                            if (StringUtils.isNotBlank(device.getMacAddress())) {
                                oldUserMacAddresses.add(device.getMacAddress());
                            }
                        }
                    }

                    // 获取智能体关联的设备MAC地址
                    List<DeviceEntity> agentDevices = deviceService.getUserDevices(null, agentId);
                    if (agentDevices != null) {
                        for (DeviceEntity device : agentDevices) {
                            if (StringUtils.isNotBlank(device.getMacAddress())) {
                                agentMacAddresses.add(device.getMacAddress());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("收集换绑前的设备信息失败");
                }

                // 更新数据库
                existingUserAgent.setUserId(userId);
                agentDao.updateById(existingUserAgent);

                // 清除换绑相关的缓存，传递收集到的MAC地址信息
                agentModelsCacheService.clearAgentRebindCachesWithMacAddresses(
                    agentId, oldUserId, userId, oldUserMacAddresses, agentMacAddresses);
            }
            return existingUserAgent.getId();
        }

        // 查找id="default"的模板智能体
        AgentEntity defaultAgent = agentDao.selectById("default");
        if (defaultAgent == null) {
            throw new RenException("默认智能体模板不存在");
        }

        // 复制默认智能体数据，生成新的智能体
        AgentEntity newAgent = new AgentEntity();
        newAgent.setId(UUID.randomUUID().toString().replace("-", "")); // 生成新ID
        newAgent.setUserId(userId);
        newAgent.setAgentCode("AGT_" + System.currentTimeMillis()); // 生成随机agentCode
        newAgent.setAgentName(defaultAgent.getAgentName());
        newAgent.setAsrModelId(defaultAgent.getAsrModelId());
        newAgent.setVadModelId(defaultAgent.getVadModelId());
        newAgent.setLlmModelId(defaultAgent.getLlmModelId());
        newAgent.setVllmModelId(defaultAgent.getVllmModelId());
        newAgent.setTtsModelId(defaultAgent.getTtsModelId());
        newAgent.setTtsVoiceId(defaultAgent.getTtsVoiceId());
        newAgent.setMemModelId(defaultAgent.getMemModelId());
        newAgent.setIntentModelId(defaultAgent.getIntentModelId());
        newAgent.setSystemPrompt(defaultAgent.getSystemPrompt());
        newAgent.setSummaryMemory(defaultAgent.getSummaryMemory());
        newAgent.setChatHistoryConf(defaultAgent.getChatHistoryConf());
        newAgent.setLangCode(defaultAgent.getLangCode());
        newAgent.setLanguage(defaultAgent.getLanguage());
        newAgent.setSort(defaultAgent.getSort());
        newAgent.setVoiceprintInterruptEnabled(defaultAgent.getVoiceprintInterruptEnabled());
        newAgent.setDeviceId(deviceId); // 设置设备ID
        newAgent.setCreator(userId);
        newAgent.setCreatedAt(new Date());
        newAgent.setUpdater(userId);
        newAgent.setUpdatedAt(new Date());

        // 保存新智能体
        agentDao.insert(newAgent);

        // 复制默认智能体的插件配置
        QueryWrapper<AgentPluginMapping> pluginWrapper = new QueryWrapper<>();
        pluginWrapper.eq("agent_id", "default");
        List<AgentPluginMapping> defaultPlugins = agentPluginMappingService.list(pluginWrapper);

        if (!defaultPlugins.isEmpty()) {
            List<AgentPluginMapping> newPlugins = new ArrayList<>();
            for (AgentPluginMapping defaultPlugin : defaultPlugins) {
                AgentPluginMapping newPlugin = new AgentPluginMapping();
                newPlugin.setAgentId(newAgent.getId());
                newPlugin.setPluginId(defaultPlugin.getPluginId());
                newPlugin.setParamInfo(defaultPlugin.getParamInfo());
                newPlugins.add(newPlugin);
            }
            agentPluginMappingService.saveBatch(newPlugins);
        }

        return newAgent.getId();
    }

    @Override
    public AgentEntity getAgentByDeviceId(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return null;
        }

        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        return agentDao.selectOne(wrapper);
    }

    @Override
    public AgentEntity getAgentByUserIdAndDeviceId(Long userId, String deviceId) {
        if (userId == null || StringUtils.isBlank(deviceId)) {
            return null;
        }

        QueryWrapper<AgentEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("device_id", deviceId);
        return agentDao.selectOne(wrapper);
    }

    @Override
    public void updateAgentSummaryMemoryOnly(String agentId, String summaryMemory) {
        if (StringUtils.isBlank(agentId)) {
            throw new RenException("智能体ID不能为空");
        }

        // 1. 更新数据库中的记忆摘要
        AgentEntity agent = new AgentEntity();
        agent.setId(agentId);
        agent.setSummaryMemory(summaryMemory);
        agent.setUpdatedAt(new Date());
        agentDao.updateById(agent);

        log.info("更新智能体记忆摘要，智能体ID: {}, 记忆长度: {}", agentId,
                summaryMemory != null ? summaryMemory.length() : 0);

        // 2. 只更新缓存中的summaryMemory字段，不清除所有缓存
        agentModelsCacheService.updateAgentSummaryMemoryInCache(agentId, summaryMemory);
    }
}
