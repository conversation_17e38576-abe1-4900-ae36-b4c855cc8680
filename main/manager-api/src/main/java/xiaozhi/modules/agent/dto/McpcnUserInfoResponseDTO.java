package xiaozhi.modules.agent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * MCPCN用户信息响应DTO
 */
@Data
public class McpcnUserInfoResponseDTO {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 用户数据
     */
    private UserData data;
    
    @Data
    public static class UserData {
        /**
         * 用户信息
         */
        private UserInfo userInfo;
    }
    
    @Data
    public static class UserInfo {
        /**
         * 用户ID
         */
        @JsonProperty("ID")
        private Long id;
        
        /**
         * 创建时间
         */
        @JsonProperty("CreatedAt")
        private String createdAt;
        
        /**
         * 更新时间
         */
        @JsonProperty("UpdatedAt")
        private String updatedAt;
        
        /**
         * UUID
         */
        private String uuid;
        
        /**
         * 用户名
         */
        private String userName;
        
        /**
         * 昵称
         */
        private String nickName;
        
        /**
         * AuthingId
         */
        private String authingId;
        
        /**
         * 侧边栏模式
         */
        private String sideMode;
        
        /**
         * 头像
         */
        private String headerImg;
        
        /**
         * 基础颜色
         */
        private String baseColor;
        
        /**
         * 激活颜色
         */
        private String activeColor;
        
        /**
         * 权限ID
         */
        private Integer authorityId;
        
        /**
         * 手机号
         */
        private String phone;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 是否启用
         */
        private Integer enable;
        
        /**
         * 积分
         */
        private Integer points;
        
        /**
         * 免费积分
         */
        private Integer freePoints;
        
        /**
         * VIP等级
         */
        @JsonProperty("vip_level")
        private Integer vipLevel;
        
        /**
         * 原始设置
         */
        private Object originSetting;
        
        /**
         * AuthingToken
         */
        private String authingToken;
        
        /**
         * 微信OpenID
         */
        private String mcpcnWechatOpenid;
        
        /**
         * 微信UnionID
         */
        private String mcpcnWechatUnionid;
        
        /**
         * 是否有密码
         */
        private Boolean havePassword;
        
        /**
         * API密钥
         */
        private String apiKey;
        
        /**
         * 是否连接客服
         */
        private Boolean connectedKf;
    }
}
