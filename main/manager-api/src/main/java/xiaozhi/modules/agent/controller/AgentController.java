package xiaozhi.modules.agent.controller;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.utils.ResultUtils;
import xiaozhi.modules.agent.dto.AgentChatHistoryDTO;
import xiaozhi.modules.agent.dto.AgentChatSessionDTO;
import xiaozhi.modules.agent.dto.AgentCreateDTO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.dto.AgentMemoryDTO;
import xiaozhi.modules.agent.dto.AgentUpdateDTO;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentChatAudioService;
import xiaozhi.modules.agent.service.AgentChatHistoryService;
import xiaozhi.modules.agent.service.AgentPluginMappingService;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.agent.service.AgentTemplateService;
import xiaozhi.modules.agent.service.McpcnApiService;
import xiaozhi.modules.agent.dto.McpcnUserInfoResponseDTO;
import xiaozhi.modules.agent.vo.AgentChatHistoryUserVO;
import xiaozhi.modules.agent.vo.AgentInfoVO;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.security.user.SecurityUser;

@Tag(name = "智能体管理")
@AllArgsConstructor
@RestController
@RequestMapping("/agent")
public class AgentController {
    private final AgentService agentService;
    private final AgentTemplateService agentTemplateService;
    private final DeviceService deviceService;
    private final AgentChatHistoryService agentChatHistoryService;
    private final AgentChatAudioService agentChatAudioService;
    private final AgentPluginMappingService agentPluginMappingService;
    private final RedisUtils redisUtils;
    private final McpcnApiService mcpcnApiService;

    @GetMapping("/list")
    @Operation(summary = "获取用户智能体列表")
    @RequiresPermissions("sys:role:normal")
    public Result<List<AgentDTO>> getUserAgents() {
        UserDetail user = SecurityUser.getUser();
        List<AgentDTO> agents = agentService.getUserAgents(user.getId());
        return new Result<List<AgentDTO>>().ok(agents);
    }

    @GetMapping("/list/no-auth")
    @Operation(summary = "获取用户智能体列表（无需鉴权，通过x-token获取用户信息）")
    public Result<AgentDTO> getUserAgentsNoAuth(
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token,
            @Parameter(description = "device-id请求头", required = true)
            @RequestHeader("device-id") String deviceId) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<AgentDTO>().error(userInfoResponse.getCode(), userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 查询该用户的智能体列表（仅包含device_id不为空的）
        AgentDTO agents = agentService.getUserAgentsWithDevice(userId,deviceId);

        return new Result<AgentDTO>().ok(agents);
    }

    @GetMapping("/{id}/no-auth")
    @Operation(summary = "获取智能体详情（无需鉴权，通过x-token获取用户信息）")
    public Result<AgentInfoVO> getAgentByIdNoAuth(
            @PathVariable("id") String id,
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<AgentInfoVO>().error(userInfoResponse.getCode(), userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 检查权限
        /*if (!agentService.checkAgentPermissionNoAuth(id, userId)) {
            return new Result<AgentInfoVO>().error(7, "没有权限访问该智能体");
        }*/

        // 获取智能体详情
        AgentInfoVO agent = agentService.getAgentById(id);
        return ResultUtils.success(agent);
    }

    @PutMapping("/{id}/no-auth")
    @Operation(summary = "更新智能体（无需鉴权，通过x-token获取用户信息）")
    public Result<Void> updateNoAuth(
            @PathVariable String id,
            @RequestBody @Valid AgentUpdateDTO dto,
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<Void>().error(userInfoResponse.getCode(), userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 检查权限
        /*if (!agentService.checkAgentPermissionNoAuth(id, userId)) {
            return new Result<Void>().error(7, "没有权限修改该智能体");
        }*/

        // 更新智能体
        agentService.updateAgentByIdNoAuth(id, dto, userId);
        return new Result<>();
    }

    @GetMapping("/all")
    @Operation(summary = "智能体列表（管理员）")
    @RequiresPermissions("sys:role:superAdmin")
    @Parameters({
            @Parameter(name = Constant.PAGE, description = "当前页码，从1开始", required = true),
            @Parameter(name = Constant.LIMIT, description = "每页显示记录数", required = true),
    })
    public Result<PageData<AgentEntity>> adminAgentList(
            @Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        PageData<AgentEntity> page = agentService.adminAgentList(params);
        return new Result<PageData<AgentEntity>>().ok(page);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取智能体详情")
    @RequiresPermissions("sys:role:normal")
    public Result<AgentInfoVO> getAgentById(@PathVariable("id") String id) {
        AgentInfoVO agent = agentService.getAgentById(id);
        return ResultUtils.success(agent);
    }

    @PostMapping
    @Operation(summary = "创建智能体")
    @RequiresPermissions("sys:role:normal")
    public Result<String> save(@RequestBody @Valid AgentCreateDTO dto) {
        String agentId = agentService.createAgent(dto);
        return new Result<String>().ok(agentId);
    }

    @PutMapping("/saveMemory/{macAddress}")
    @Operation(summary = "根据设备id更新智能体")
    public Result<Void> updateByDeviceId(@PathVariable String macAddress, @RequestBody @Valid AgentMemoryDTO dto) {
        DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
        if (device == null) {
            return new Result<>();
        }
        agentService.updateAgentSummaryMemoryOnly(device.getAgentId(), dto.getSummaryMemory());
        return new Result<>();
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新智能体")
    @RequiresPermissions("sys:role:normal")
    public Result<Void> update(@PathVariable String id, @RequestBody @Valid AgentUpdateDTO dto) {
        agentService.updateAgentById(id, dto);
        return new Result<>();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除智能体")
    @RequiresPermissions("sys:role:normal")
    public Result<Void> delete(@PathVariable String id) {
        // 先删除关联的设备
        deviceService.deleteByAgentId(id);
        // 删除关联的聊天记录
        agentChatHistoryService.deleteByAgentId(id, true, true);
        // 删除关联的插件
        agentPluginMappingService.deleteByAgentId(id);
        // 再删除智能体
        agentService.deleteById(id);
        return new Result<>();
    }

    @GetMapping("/template/external")
    @Operation(summary = "智能体模板列表（外部调用）")
    public Result<List<AgentTemplateEntity>> templateListExternal(
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<List<AgentTemplateEntity>>().error(7, userInfoResponse.getMsg());
        }

        // 获取模板列表
        List<AgentTemplateEntity> list = agentTemplateService
                .list(new QueryWrapper<AgentTemplateEntity>().orderByAsc("sort"));
        return new Result<List<AgentTemplateEntity>>().ok(list);
    }

    @GetMapping("/template")
    @Operation(summary = "智能体模板模板列表")
    @RequiresPermissions("sys:role:normal")
    public Result<List<AgentTemplateEntity>> templateList() {
        List<AgentTemplateEntity> list = agentTemplateService
                .list(new QueryWrapper<AgentTemplateEntity>().orderByAsc("sort"));
        return new Result<List<AgentTemplateEntity>>().ok(list);
    }

    @GetMapping("/{id}/sessions")
    @Operation(summary = "获取智能体会话列表")
    @RequiresPermissions("sys:role:normal")
    @Parameters({
            @Parameter(name = Constant.PAGE, description = "当前页码，从1开始", required = true),
            @Parameter(name = Constant.LIMIT, description = "每页显示记录数", required = true),
    })
    public Result<PageData<AgentChatSessionDTO>> getAgentSessions(
            @PathVariable("id") String id,
            @Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        params.put("agentId", id);
        PageData<AgentChatSessionDTO> page = agentChatHistoryService.getSessionListByAgentId(params);
        return new Result<PageData<AgentChatSessionDTO>>().ok(page);
    }

    @GetMapping("/{id}/chat-history/{sessionId}")
    @Operation(summary = "获取智能体聊天记录")
    @RequiresPermissions("sys:role:normal")
    public Result<List<AgentChatHistoryDTO>> getAgentChatHistory(
            @PathVariable("id") String id,
            @PathVariable("sessionId") String sessionId) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();

        // 检查权限
        if (!agentService.checkAgentPermission(id, user.getId())) {
            return new Result<List<AgentChatHistoryDTO>>().error("没有权限查看该智能体的聊天记录");
        }

        // 查询聊天记录
        List<AgentChatHistoryDTO> result = agentChatHistoryService.getChatHistoryBySessionId(id, sessionId);
        return new Result<List<AgentChatHistoryDTO>>().ok(result);
    }
    @GetMapping("/{id}/chat-history/user")
    @Operation(summary = "获取智能体聊天记录（用户）")
    @RequiresPermissions("sys:role:normal")
    public Result<List<AgentChatHistoryUserVO>> getRecentlyFiftyByAgentId(
            @PathVariable("id") String id) {
        // 获取当前用户
        UserDetail user = SecurityUser.getUser();

        // 检查权限
        if (!agentService.checkAgentPermission(id, user.getId())) {
            return new Result<List<AgentChatHistoryUserVO>>().error("没有权限查看该智能体的聊天记录");
        }

        // 查询聊天记录
        List<AgentChatHistoryUserVO> data = agentChatHistoryService.getRecentlyFiftyByAgentId(id);
        return new Result<List<AgentChatHistoryUserVO>>().ok(data);
    }

    @GetMapping("/{id}/sessions/external")
    @Operation(summary = "获取智能体会话列表（外部调用）")
    @Parameters({
            @Parameter(name = Constant.PAGE, description = "当前页码，从1开始", required = true),
            @Parameter(name = Constant.LIMIT, description = "每页显示记录数", required = true),
    })
    public Result<PageData<AgentChatSessionDTO>> getAgentSessionsExternal(
            @PathVariable("id") String id,
            @Parameter(hidden = true) @RequestParam Map<String, Object> params,
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<PageData<AgentChatSessionDTO>>().error(7, userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 检查权限
        if (!agentService.checkAgentPermissionNoAuth(id, userId)) {
            return new Result<PageData<AgentChatSessionDTO>>().error(7, "没有权限访问该智能体");
        }

        params.put("agentId", id);
        PageData<AgentChatSessionDTO> page = agentChatHistoryService.getSessionListByAgentId(params);
        return new Result<PageData<AgentChatSessionDTO>>().ok(page);
    }

    @GetMapping("/{id}/chat-history/{sessionId}/external")
    @Operation(summary = "获取智能体聊天记录（外部调用）")
    public Result<List<AgentChatHistoryDTO>> getAgentChatHistoryExternal(
            @PathVariable("id") String id,
            @PathVariable("sessionId") String sessionId,
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<List<AgentChatHistoryDTO>>().error(7, userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 检查权限
        if (!agentService.checkAgentPermissionNoAuth(id, userId)) {
            return new Result<List<AgentChatHistoryDTO>>().error(7, "没有权限查看该智能体的聊天记录");
        }

        // 查询聊天记录
        List<AgentChatHistoryDTO> result = agentChatHistoryService.getChatHistoryBySessionId(id, sessionId);
        return new Result<List<AgentChatHistoryDTO>>().ok(result);
    }

    @GetMapping("/{id}/chat-history/user/external")
    @Operation(summary = "获取智能体聊天记录（用户）（外部调用）")
    public Result<List<AgentChatHistoryUserVO>> getRecentlyFiftyByAgentIdExternal(
            @PathVariable("id") String id,
            @Parameter(description = "x-token请求头", required = true)
            @RequestHeader("x-token") String token) {

        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<List<AgentChatHistoryUserVO>>().error(7, userInfoResponse.getMsg());
        }

        // 获取用户ID
        Long userId = userInfoResponse.getData().getUserInfo().getId();

        // 检查权限
        if (!agentService.checkAgentPermissionNoAuth(id, userId)) {
            return new Result<List<AgentChatHistoryUserVO>>().error(7, "没有权限查看该智能体的聊天记录");
        }

        // 查询聊天记录
        List<AgentChatHistoryUserVO> data = agentChatHistoryService.getRecentlyFiftyByAgentId(id);
        return new Result<List<AgentChatHistoryUserVO>>().ok(data);
    }

    @GetMapping("/{id}/chat-history/audio")
    @Operation(summary = "获取音频内容")
    @RequiresPermissions("sys:role:normal")
    public Result<String> getContentByAudioId(
            @PathVariable("id") String id) {
        // 查询聊天记录
        String data = agentChatHistoryService.getContentByAudioId(id);
        return new Result<String>().ok(data);
    }

    @PostMapping("/audio/{audioId}")
    @Operation(summary = "获取音频下载ID")
    @RequiresPermissions("sys:role:normal")
    public Result<String> getAudioId(@PathVariable("audioId") String audioId) {
        byte[] audioData = agentChatAudioService.getAudio(audioId);
        if (audioData == null) {
            return new Result<String>().error("音频不存在");
        }
        String uuid = UUID.randomUUID().toString();
        redisUtils.set(RedisKeys.getAgentAudioIdKey(uuid), audioId);
        return new Result<String>().ok(uuid);
    }

    @GetMapping("/play/{uuid}")
    @Operation(summary = "播放音频")
    public ResponseEntity<byte[]> playAudio(@PathVariable("uuid") String uuid) {

        String audioId = (String) redisUtils.get(RedisKeys.getAgentAudioIdKey(uuid));
        if (StringUtils.isBlank(audioId)) {
            return ResponseEntity.notFound().build();
        }

        byte[] audioData = agentChatAudioService.getAudio(audioId);
        if (audioData == null) {
            return ResponseEntity.notFound().build();
        }
        redisUtils.delete(RedisKeys.getAgentAudioIdKey(uuid));
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"play.wav\"")
                .body(audioData);
    }

    @PostMapping("/assign-default/{userId}/{deviceId}")
    @Operation(summary = "为用户分配默认智能体")
    public Result<AgentInfoVO> assignDefaultAgentToUser(
            @PathVariable("userId") Long userId,
            @PathVariable("deviceId") String deviceId) {
        String agentId = agentService.assignDefaultAgentToUser(userId, deviceId);
        AgentInfoVO agentInfo = agentService.getAgentById(agentId);
        return new Result<AgentInfoVO>().ok(agentInfo);
    }

}