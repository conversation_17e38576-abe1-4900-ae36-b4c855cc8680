package xiaozhi.modules.agent.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.config.McpcnProperties;
import xiaozhi.modules.agent.dto.McpcnUserInfoResponseDTO;
import xiaozhi.modules.agent.service.McpcnApiService;

/**
 * MCPCN外部API服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class McpcnApiServiceImpl implements McpcnApiService {
    
    private final RestTemplate restTemplate;
    private final McpcnProperties mcpcnProperties;
    
    @Override
    public McpcnUserInfoResponseDTO getUserInfo(String token) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-token", token);
            
            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // 发送GET请求
            String url = mcpcnProperties.getUserInfoUrl();
            log.info("调用MCPCN获取用户信息接口，URL: {}", url);
            
            ResponseEntity<McpcnUserInfoResponseDTO> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                McpcnUserInfoResponseDTO.class
            );
            
            McpcnUserInfoResponseDTO result = response.getBody();
            log.info("MCPCN接口响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), result);
            
            return result;
            
        } catch (Exception e) {
            log.error("调用MCPCN获取用户信息接口异常", e);
            // 返回错误响应
            McpcnUserInfoResponseDTO errorResponse = new McpcnUserInfoResponseDTO();
            errorResponse.setCode(7);
            errorResponse.setMsg("调用外部接口异常: " + e.getMessage());
            return errorResponse;
        }
    }
}
