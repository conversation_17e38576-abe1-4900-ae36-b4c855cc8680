package xiaozhi.modules.config.service;

import java.util.List;

/**
 * 智能体模型配置缓存管理服务
 * 负责管理 getAgentModels 方法相关的所有缓存清除操作
 */
public interface AgentModelsCacheService {

    /**
     * 清除指定MAC地址的智能体模型配置缓存
     * 
     * @param macAddress MAC地址
     */
    void clearAgentModelsCacheByMac(String macAddress);

    /**
     * 清除指定智能体相关的所有缓存
     * 
     * @param agentId 智能体ID
     */
    void clearAgentRelatedCaches(String agentId);

    /**
     * 清除指定用户相关的所有缓存
     * 
     * @param userId 用户ID
     */
    void clearUserRelatedCaches(Long userId);

    /**
     * 清除指定设备相关的所有缓存
     * 
     * @param deviceId 设备ID
     * @param macAddress MAC地址
     */
    void clearDeviceRelatedCaches(String deviceId, String macAddress);

    /**
     * 清除指定音色相关的所有缓存
     * 
     * @param timbreId 音色ID
     */
    void clearTimbreRelatedCaches(String timbreId);

    /**
     * 清除指定模型相关的所有缓存
     * 
     * @param modelId 模型ID
     */
    void clearModelRelatedCaches(String modelId);

    /**
     * 清除指定系统参数相关的所有缓存
     * 
     * @param paramCode 参数代码
     */
    void clearSysParamRelatedCaches(String paramCode);

    /**
     * 清除智能体插件相关的缓存
     * 
     * @param agentId 智能体ID
     */
    void clearAgentPluginCaches(String agentId);

    /**
     * 清除智能体声纹相关的缓存
     * 
     * @param agentId 智能体ID
     */
    void clearAgentVoiceprintCaches(String agentId);

    /**
     * 根据智能体ID查找相关的MAC地址列表
     * 
     * @param agentId 智能体ID
     * @return MAC地址列表
     */
    List<String> getMacAddressesByAgentId(String agentId);

    /**
     * 根据用户ID查找相关的MAC地址列表
     * 
     * @param userId 用户ID
     * @return MAC地址列表
     */
    List<String> getMacAddressesByUserId(Long userId);

    /**
     * 根据音色ID查找使用该音色的智能体ID列表
     * 
     * @param timbreId 音色ID
     * @return 智能体ID列表
     */
    List<String> getAgentIdsByTimbreId(String timbreId);

    /**
     * 根据模型ID查找使用该模型的智能体ID列表
     * 
     * @param modelId 模型ID
     * @return 智能体ID列表
     */
    List<String> getAgentIdsByModelId(String modelId);

    /**
     * 清除智能体换绑相关的缓存
     *
     * @param agentId 智能体ID
     * @param oldUserId 原用户ID
     * @param newUserId 新用户ID
     */
    void clearAgentRebindCaches(String agentId, Long oldUserId, Long newUserId);

    /**
     * 清除智能体换绑相关的缓存（带MAC地址信息）
     *
     * @param agentId 智能体ID
     * @param oldUserId 原用户ID
     * @param newUserId 新用户ID
     * @param oldUserMacAddresses 原用户的MAC地址列表
     * @param agentMacAddresses 智能体关联的MAC地址列表
     */
    void clearAgentRebindCachesWithMacAddresses(String agentId, Long oldUserId, Long newUserId,
                                               List<String> oldUserMacAddresses, List<String> agentMacAddresses);

    /**
     * 清除智能体更新相关的缓存（考虑可能的用户换绑）
     * 这个方法会检查智能体的历史用户关联，确保清除所有相关缓存
     *
     * @param agentId 智能体ID
     */
    void clearAgentUpdateCaches(String agentId);

    /**
     * 清除所有 agent:models:* 缓存（慎用）
     */
    void clearAllAgentModelsCache();

    /**
     * 智能体WebSocket连接关闭时清除相关缓存
     * 获取使用该智能体的所有电脑设备，清除它们的缓存
     *
     * @param agentId 智能体ID
     */
    void clearCachesOnAgentWebSocketClosed(String agentId);

    /**
     * 根据设备ID清除agent:models缓存
     * 这是一个通用方法，可以直接根据设备ID清除相关的配置缓存
     *
     * @param deviceId 设备ID
     * @return 清除的缓存数量
     */
    int clearAgentModelsCacheByDeviceId(String deviceId);

    /**
     * 调试方法：直接测试清除特定用户的电脑设备缓存
     * 可以通过接口调用来调试缓存清除逻辑
     *
     * @param userId 用户ID
     */
    void debugClearUserComputerDeviceModelsCache(Long userId);

    /**
     * 只更新缓存中的summaryMemory字段，不清除所有缓存
     * 用于聊天后更新记忆的场景
     *
     * @param agentId 智能体ID
     * @param summaryMemory 记忆摘要
     */
    void updateAgentSummaryMemoryInCache(String agentId, String summaryMemory);
}
