package xiaozhi.modules.config.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.config.service.AgentModelsCacheService;
import xiaozhi.modules.device.dao.DeviceDao;
import xiaozhi.modules.device.entity.DeviceEntity;

/**
 * 智能体模型配置缓存管理服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentModelsCacheServiceImpl implements AgentModelsCacheService {

    private final RedisUtils redisUtils;
    private final StringRedisTemplate stringRedisTemplate;
    private final AgentDao agentDao;
    private final DeviceDao deviceDao;

    @Override
    public void clearAgentModelsCacheByMac(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return;
        }

        try {
            // 清除所有以 agent:models:{macAddress}: 开头的缓存
            String pattern = "agent:models:" + macAddress + ":*";
            Set<String> keys = stringRedisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                stringRedisTemplate.delete(keys);
                log.info("清除智能体模型配置缓存，MAC地址: {}, 清除数量: {}", macAddress, keys.size());
            }
        } catch (Exception e) {
            log.error("清除智能体模型配置缓存失败，MAC地址: {}", macAddress, e);
        }
    }

    @Override
    public void clearAgentRelatedCaches(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            // 1. 清除智能体信息缓存
            redisUtils.delete(RedisKeys.getAgentInfoKey(agentId));

            // 2. 清除智能体插件缓存
            redisUtils.delete(RedisKeys.getAgentPluginMappingsKey(agentId));

            // 3. 清除智能体声纹缓存
            redisUtils.delete(RedisKeys.getAgentVoiceprintsKey(agentId));

            // 4. 清除智能体设备数量缓存
            redisUtils.delete(RedisKeys.getAgentDeviceCountById(agentId));

            // 5. 清除智能体最后连接时间缓存
            //redisUtils.delete(RedisKeys.getAgentDeviceLastConnectedAtById(agentId));

            // 6. 获取智能体信息，清除相关用户的缓存
            AgentEntity agent = agentDao.selectById(agentId);
            if (agent != null && agent.getUserId() != null) {
                // 清除用户智能体列表缓存
                redisUtils.delete(RedisKeys.getUserAgentsKey(agent.getUserId()));

                // 清除用户设备列表缓存
                redisUtils.delete("device:user_devices:" + agent.getUserId());

                // 清除硬件设备的用户设备智能体关联缓存（数据库中的设备）
                List<DeviceEntity> userDevices = deviceDao.selectByUserId(agent.getUserId());
                for (DeviceEntity device : userDevices) {
                    // 硬件设备使用数据库ID
                    String hardwareDeviceKey = RedisKeys.getAgentByUserDeviceKey(agent.getUserId(), device.getId().toString());
                    redisUtils.delete(hardwareDeviceKey);
                    log.info("清除硬件设备智能体关联缓存: {}", hardwareDeviceKey);
                }

                // 清除该用户所有电脑设备的agent:models缓存（智能体修改时需要清除）
                clearUserComputerDeviceModelsCache(agent.getUserId());
            }

            // 7. 查找相关的MAC地址并清除对应的 agent:models 缓存
            List<String> macAddresses = getMacAddressesByAgentId(agentId);
            for (String macAddress : macAddresses) {
                clearAgentModelsCacheByMac(macAddress);
            }

            // 8. 额外清除包含该智能体的历史缓存（处理换绑场景）
            clearHistoricalUserCachesForAgent(agentId);

            log.info("清除智能体相关缓存，智能体ID: {}, 用户ID: {}, 影响MAC地址数量: {}",
                    agentId, agent != null ? agent.getUserId() : null, macAddresses.size());
        } catch (Exception e) {
            log.error("清除智能体相关缓存失败，智能体ID: {}", agentId, e);
        }
    }

    @Override
    public void clearUserRelatedCaches(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            // 1. 清除用户智能体列表缓存
            redisUtils.delete(RedisKeys.getUserAgentsKey(userId));

            // 2. 清除用户设备列表缓存
            redisUtils.delete("device:user_devices:" + userId);

            // 3. 清除用户设备智能体关联缓存（需要查询所有设备）
            List<DeviceEntity> userDevices = deviceDao.selectByUserId(userId);
            for (DeviceEntity device : userDevices) {
                redisUtils.delete(RedisKeys.getAgentByUserDeviceKey(userId, device.getId()));
            }

            // 4. 清除用户的所有电脑设备缓存（gRPC查询结果）
            clearUserComputerDeviceCaches(userId);

            // 5. 查找相关的MAC地址并清除对应的 agent:models 缓存
            List<String> macAddresses = getMacAddressesByUserId(userId);
            for (String macAddress : macAddresses) {
                clearAgentModelsCacheByMac(macAddress);
            }

            log.info("清除用户相关缓存，用户ID: {}, 影响MAC地址数量: {}", userId, macAddresses.size());
        } catch (Exception e) {
            log.error("清除用户相关缓存失败，用户ID: {}", userId, e);
        }
    }

    @Override
    public void clearDeviceRelatedCaches(String deviceId, String macAddress) {
        if (StringUtils.isBlank(deviceId) && StringUtils.isBlank(macAddress)) {
            return;
        }

        try {
            // 1. 清除设备信息缓存
            if (StringUtils.isNotBlank(macAddress)) {
                redisUtils.delete(RedisKeys.getDeviceByMacKey(macAddress));
                redisUtils.delete(RedisKeys.getComputerDeviceKey(macAddress));
                clearAgentModelsCacheByMac(macAddress);
            }

            // 2. 如果有设备ID，清除相关的用户设备智能体关联缓存
            if (StringUtils.isNotBlank(deviceId)) {
                // 查找设备信息
                DeviceEntity device = deviceDao.selectById(deviceId);
                if (device != null) {
                    if (device.getUserId() != null) {
                        redisUtils.delete(RedisKeys.getAgentByUserDeviceKey(device.getUserId(), deviceId));
                        // 清除用户设备列表缓存
                        redisUtils.delete("device:user_devices:" + device.getUserId());
                    }
                    // 如果MAC地址为空，从设备信息中获取
                    if (StringUtils.isBlank(macAddress) && StringUtils.isNotBlank(device.getMacAddress())) {
                        clearAgentModelsCacheByMac(device.getMacAddress());
                    }
                }
            }

            log.info("清除设备相关缓存，设备ID: {}, MAC地址: {}", deviceId, macAddress);
        } catch (Exception e) {
            log.error("清除设备相关缓存失败，设备ID: {}, MAC地址: {}", deviceId, macAddress, e);
        }
    }

    @Override
    public void clearTimbreRelatedCaches(String timbreId) {
        if (StringUtils.isBlank(timbreId)) {
            return;
        }

        try {
            // 1. 清除音色详情缓存
            redisUtils.delete(RedisKeys.getTimbreDetailsKey(timbreId));

            // 2. 查找使用该音色的智能体并清除相关缓存
            List<String> agentIds = getAgentIdsByTimbreId(timbreId);
            for (String agentId : agentIds) {
                List<String> macAddresses = getMacAddressesByAgentId(agentId);
                for (String macAddress : macAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
            }

            log.info("清除音色相关缓存，音色ID: {}, 影响智能体数量: {}", timbreId, agentIds.size());
        } catch (Exception e) {
            log.error("清除音色相关缓存失败，音色ID: {}", timbreId, e);
        }
    }

    @Override
    public void clearModelRelatedCaches(String modelId) {
        if (StringUtils.isBlank(modelId)) {
            return;
        }

        try {
            // 1. 清除模型配置缓存
            redisUtils.delete(RedisKeys.getModelConfigById(modelId));
            redisUtils.delete(RedisKeys.getModelNameById(modelId));

            // 2. 查找使用该模型的智能体并清除相关缓存
            List<String> agentIds = getAgentIdsByModelId(modelId);
            for (String agentId : agentIds) {
                List<String> macAddresses = getMacAddressesByAgentId(agentId);
                for (String macAddress : macAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
            }

            log.info("清除模型相关缓存，模型ID: {}, 影响智能体数量: {}", modelId, agentIds.size());
        } catch (Exception e) {
            log.error("清除模型相关缓存失败，模型ID: {}", modelId, e);
        }
    }

    @Override
    public void clearSysParamRelatedCaches(String paramCode) {
        if (StringUtils.isBlank(paramCode)) {
            return;
        }

        try {
            // 1. 清除系统参数缓存
            redisUtils.delete(RedisKeys.getSysParamKey(paramCode));

            // 2. 如果是影响 getAgentModels 的关键参数，清除所有相关缓存
            if ("device_max_output_size".equals(paramCode) || "server.voice_print".equals(paramCode)) {
                clearAllAgentModelsCache();
                log.info("清除系统参数相关缓存，参数代码: {} (关键参数，清除所有 agent:models 缓存)", paramCode);
            } else {
                log.info("清除系统参数相关缓存，参数代码: {}", paramCode);
            }
        } catch (Exception e) {
            log.error("清除系统参数相关缓存失败，参数代码: {}", paramCode, e);
        }
    }

    @Override
    public void clearAgentPluginCaches(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            // 清除智能体插件缓存
            redisUtils.delete(RedisKeys.getAgentPluginMappingsKey(agentId));

            // 清除相关的 agent:models 缓存
            List<String> macAddresses = getMacAddressesByAgentId(agentId);
            for (String macAddress : macAddresses) {
                clearAgentModelsCacheByMac(macAddress);
            }

            log.info("清除智能体插件缓存，智能体ID: {}, 影响MAC地址数量: {}", agentId, macAddresses.size());
        } catch (Exception e) {
            log.error("清除智能体插件缓存失败，智能体ID: {}", agentId, e);
        }
    }

    @Override
    public void clearAgentVoiceprintCaches(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            // 清除智能体声纹缓存
            redisUtils.delete(RedisKeys.getAgentVoiceprintsKey(agentId));

            // 清除相关的 agent:models 缓存
            List<String> macAddresses = getMacAddressesByAgentId(agentId);
            for (String macAddress : macAddresses) {
                clearAgentModelsCacheByMac(macAddress);
            }

            log.info("清除智能体声纹缓存，智能体ID: {}, 影响MAC地址数量: {}", agentId, macAddresses.size());
        } catch (Exception e) {
            log.error("清除智能体声纹缓存失败，智能体ID: {}", agentId, e);
        }
    }

    @Override
    public List<String> getMacAddressesByAgentId(String agentId) {
        List<String> macAddresses = new ArrayList<>();
        if (StringUtils.isBlank(agentId)) {
            return macAddresses;
        }

        try {
            // 查询使用该智能体的所有设备
            List<DeviceEntity> devices = deviceDao.selectByAgentId(agentId);
            for (DeviceEntity device : devices) {
                if (StringUtils.isNotBlank(device.getMacAddress())) {
                    macAddresses.add(device.getMacAddress());
                }
            }
        } catch (Exception e) {
            log.error("根据智能体ID查找MAC地址失败，智能体ID: {}", agentId, e);
        }

        return macAddresses;
    }

    @Override
    public List<String> getMacAddressesByUserId(Long userId) {
        List<String> macAddresses = new ArrayList<>();
        if (userId == null) {
            return macAddresses;
        }

        try {
            // 查询用户的所有设备
            List<DeviceEntity> devices = deviceDao.selectByUserId(userId);
            for (DeviceEntity device : devices) {
                if (StringUtils.isNotBlank(device.getMacAddress())) {
                    macAddresses.add(device.getMacAddress());
                }
            }
        } catch (Exception e) {
            log.error("根据用户ID查找MAC地址失败，用户ID: {}", userId, e);
        }

        return macAddresses;
    }

    @Override
    public List<String> getAgentIdsByTimbreId(String timbreId) {
        List<String> agentIds = new ArrayList<>();
        if (StringUtils.isBlank(timbreId)) {
            return agentIds;
        }

        try {
            // 查询使用该音色的所有智能体
            List<AgentEntity> agents = agentDao.selectByTimbreId(timbreId);
            for (AgentEntity agent : agents) {
                agentIds.add(agent.getId());
            }
        } catch (Exception e) {
            log.error("根据音色ID查找智能体ID失败，音色ID: {}", timbreId, e);
        }

        return agentIds;
    }

    @Override
    public List<String> getAgentIdsByModelId(String modelId) {
        List<String> agentIds = new ArrayList<>();
        if (StringUtils.isBlank(modelId)) {
            return agentIds;
        }

        try {
            // 查询使用该模型的所有智能体（包括各种模型类型）
            List<AgentEntity> agents = agentDao.selectByModelId(modelId);
            for (AgentEntity agent : agents) {
                agentIds.add(agent.getId());
            }
        } catch (Exception e) {
            log.error("根据模型ID查找智能体ID失败，模型ID: {}", modelId, e);
        }

        return agentIds;
    }

    @Override
    public void clearAgentRebindCaches(String agentId, Long oldUserId, Long newUserId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            log.info("开始清除智能体换绑相关缓存，智能体ID: {}, 原用户ID: {}, 新用户ID: {}",
                    agentId, oldUserId, newUserId);

            // 1. 先获取智能体相关的MAC地址（在智能体更新后获取）
            List<String> agentMacAddresses = getMacAddressesByAgentId(agentId);

            // 2. 清除智能体相关的基础缓存（这会清除新用户的缓存）
            clearAgentRelatedCaches(agentId);

            // 3. 清除原用户相关的缓存
            if (oldUserId != null) {
                log.info("清除原用户相关缓存，原用户ID: {}", oldUserId);

                // 清除原用户的智能体列表缓存
                redisUtils.delete(RedisKeys.getUserAgentsKey(oldUserId));

                // 清除原用户的设备列表缓存
                redisUtils.delete("device:user_devices:" + oldUserId);

                // 清除原用户相关的MAC地址的agent:models缓存
                List<String> oldUserMacAddresses = getMacAddressesByUserId(oldUserId);
                log.info("原用户关联的MAC地址数量: {}", oldUserMacAddresses.size());
                for (String macAddress : oldUserMacAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
            }

            // 4. 清除新用户相关的缓存
            if (newUserId != null) {
                log.info("清除新用户相关缓存，新用户ID: {}", newUserId);

                // 清除新用户的智能体列表缓存
                redisUtils.delete(RedisKeys.getUserAgentsKey(newUserId));

                // 清除新用户的设备列表缓存
                redisUtils.delete("device:user_devices:" + newUserId);

                // 清除新用户相关的MAC地址的agent:models缓存
                List<String> newUserMacAddresses = getMacAddressesByUserId(newUserId);
                log.info("新用户关联的MAC地址数量: {}", newUserMacAddresses.size());
                for (String macAddress : newUserMacAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
            }

            // 5. 额外清除智能体相关的MAC地址缓存
            log.info("清除智能体关联的MAC地址缓存，数量: {}", agentMacAddresses.size());
            for (String macAddress : agentMacAddresses) {
                clearAgentModelsCacheByMac(macAddress);
            }

            // 6. 扫描并清除所有包含该智能体的历史缓存
            clearHistoricalUserCachesForAgent(agentId);

            log.info("清除智能体换绑相关缓存完成，智能体ID: {}, 原用户ID: {}, 新用户ID: {}, 智能体MAC地址数量: {}",
                    agentId, oldUserId, newUserId, agentMacAddresses.size());
        } catch (Exception e) {
            log.error("清除智能体换绑相关缓存失败，智能体ID: {}, 原用户ID: {}, 新用户ID: {}",
                    agentId, oldUserId, newUserId, e);
        }
    }

    @Override
    public void clearAgentRebindCachesWithMacAddresses(String agentId, Long oldUserId, Long newUserId,
                                                       List<String> oldUserMacAddresses, List<String> agentMacAddresses) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            log.info("开始清除智能体换绑相关缓存（带MAC地址），智能体ID: {}, 原用户ID: {}, 新用户ID: {}, 原用户MAC数量: {}, 智能体MAC数量: {}",
                    agentId, oldUserId, newUserId,
                    oldUserMacAddresses != null ? oldUserMacAddresses.size() : 0,
                    agentMacAddresses != null ? agentMacAddresses.size() : 0);

            // 1. 清除智能体相关的基础缓存
            clearAgentRelatedCaches(agentId);

            // 2. 清除原用户相关的缓存
            if (oldUserId != null) {
                log.info("清除原用户相关缓存，原用户ID: {}", oldUserId);

                // 清除原用户的智能体列表缓存
                redisUtils.delete(RedisKeys.getUserAgentsKey(oldUserId));

                // 清除原用户的设备列表缓存
                redisUtils.delete("device:user_devices:" + oldUserId);

                // 清除原用户相关的MAC地址的agent:models缓存
                if (oldUserMacAddresses != null && !oldUserMacAddresses.isEmpty()) {
                    for (String macAddress : oldUserMacAddresses) {
                        clearAgentModelsCacheByMac(macAddress);
                    }
                    log.info("清除原用户MAC地址缓存完成，数量: {}", oldUserMacAddresses.size());
                }
            }

            // 3. 清除新用户相关的缓存
            if (newUserId != null) {
                log.info("清除新用户相关缓存，新用户ID: {}", newUserId);

                // 清除新用户的智能体列表缓存
                redisUtils.delete(RedisKeys.getUserAgentsKey(newUserId));

                // 清除新用户的设备列表缓存
                redisUtils.delete("device:user_devices:" + newUserId);

                // 清除新用户相关的MAC地址的agent:models缓存
                List<String> newUserMacAddresses = getMacAddressesByUserId(newUserId);
                for (String macAddress : newUserMacAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
                log.info("清除新用户MAC地址缓存完成，数量: {}", newUserMacAddresses.size());
            }

            // 4. 清除智能体关联的MAC地址缓存
            if (agentMacAddresses != null && !agentMacAddresses.isEmpty()) {
                for (String macAddress : agentMacAddresses) {
                    clearAgentModelsCacheByMac(macAddress);
                }
                log.info("清除智能体MAC地址缓存完成，数量: {}", agentMacAddresses.size());
            }

            // 5. 扫描并清除所有包含该智能体的历史缓存
            clearHistoricalUserCachesForAgent(agentId);

            log.info("清除智能体换绑相关缓存完成，智能体ID: {}, 原用户ID: {}, 新用户ID: {}",
                    agentId, oldUserId, newUserId);
        } catch (Exception e) {
            log.error("清除智能体换绑相关缓存失败，智能体ID: {}, 原用户ID: {}, 新用户ID: {}",
                    agentId, oldUserId, newUserId, e);
        }
    }

    @Override
    public void clearAgentUpdateCaches(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            // 1. 清除智能体相关的基础缓存
            clearAgentRelatedCaches(agentId);

            // 2. 额外清除可能的历史用户缓存
            // 通过查找所有 agent:models:* 缓存，找出可能包含该智能体的缓存
            clearHistoricalUserCachesForAgent(agentId);

            log.info("清除智能体更新相关缓存完成，智能体ID: {}", agentId);
        } catch (Exception e) {
            log.error("清除智能体更新相关缓存失败，智能体ID: {}", agentId, e);
        }
    }

    /**
     * 清除智能体的历史用户缓存
     * 通过扫描所有 agent:models 缓存来找出可能的历史关联
     */
    private void clearHistoricalUserCachesForAgent(String agentId) {
        try {
            // 查找所有 agent:models:* 缓存
            String pattern = "agent:models:*";
            Set<String> allKeys = stringRedisTemplate.keys(pattern);

            if (allKeys == null || allKeys.isEmpty()) {
                return;
            }

            log.info("扫描历史缓存，智能体ID: {}, 总缓存数量: {}", agentId, allKeys.size());

            // 检查每个缓存，看是否包含该智能体
            int clearedCount = 0;
            for (String key : allKeys) {
                try {
                    String cacheValue = stringRedisTemplate.opsForValue().get(key);
                    if (StringUtils.isNotBlank(cacheValue)) {
                        // 检查缓存是否包含该智能体ID（可能在不同字段中）
                        boolean containsAgent = cacheValue.contains("\"agentId\":\"" + agentId + "\"") ||
                                              cacheValue.contains("\"id\":\"" + agentId + "\"") ||
                                              cacheValue.contains("\"agent_id\":\"" + agentId + "\"");

                        if (containsAgent) {
                            // 这个缓存包含该智能体，删除它
                            stringRedisTemplate.delete(key);
                            clearedCount++;
                            log.info("清除包含智能体的历史缓存，key: {}, 智能体ID: {}", key, agentId);
                        }
                    }
                } catch (Exception e) {
                    log.warn("检查缓存失败，key: {}, 错误: {}", key, e.getMessage());
                }
            }

            if (clearedCount > 0) {
                log.info("清除智能体历史缓存完成，智能体ID: {}, 清除数量: {}", agentId, clearedCount);
            }
        } catch (Exception e) {
            log.error("清除智能体历史用户缓存失败，智能体ID: {}", agentId, e);
        }
    }

    @Override
    public void clearAllAgentModelsCache() {
        try {
            // 清除所有 agent:models:* 缓存
            String pattern = "agent:models:*";
            Set<String> keys = stringRedisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                stringRedisTemplate.delete(keys);
                log.warn("清除所有智能体模型配置缓存，清除数量: {}", keys.size());
            }
        } catch (Exception e) {
            log.error("清除所有智能体模型配置缓存失败", e);
        }
    }

    /**
     * 清除用户的所有电脑设备缓存（gRPC查询结果）
     * 通过模糊匹配 device:device:* 找到该用户的所有设备缓存
     */
    private void clearUserComputerDeviceCaches(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            // 查找所有 device:device:* 的key
            String pattern = "device:device:*";
            Set<String> allDeviceKeys = stringRedisTemplate.keys(pattern);

            if (allDeviceKeys != null && !allDeviceKeys.isEmpty()) {
                List<String> userDeviceKeys = new ArrayList<>();

                // 遍历所有设备缓存，找到属于该用户的设备
                List<String> userDeviceIds = new ArrayList<>();
                for (String deviceKey : allDeviceKeys) {
                    try {
                        String jsonStr = stringRedisTemplate.opsForValue().get(deviceKey);
                        if (StringUtils.isNotBlank(jsonStr)) {
                            // 简单检查JSON中是否包含该用户ID
                            if (jsonStr.contains("\"userId\":" + userId)) {
                                userDeviceKeys.add(deviceKey);

                                // 提取设备ID，用于清除agent:models缓存
                                String deviceId = deviceKey.replace("device:device:", "");
                                userDeviceIds.add(deviceId);
                            }
                        }
                    } catch (Exception e) {
                        // 单个设备缓存检查失败，继续处理其他设备
                        log.warn("检查设备缓存失败，key: {}", deviceKey, e);
                    }
                }

                // 批量删除该用户的设备缓存
                if (!userDeviceKeys.isEmpty()) {
                    stringRedisTemplate.delete(userDeviceKeys);
                    log.info("清除用户电脑设备缓存，用户ID: {}, 清除数量: {}", userId, userDeviceKeys.size());
                }
            }
        } catch (Exception e) {
            log.error("清除用户电脑设备缓存失败，用户ID: {}", userId, e);
        }
    }

    /**
     * 智能体WebSocket连接关闭时清除相关缓存
     * 获取使用该智能体的所有电脑设备，清除它们的缓存
     */
    public void clearCachesOnAgentWebSocketClosed(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            log.info("智能体WebSocket连接关闭，开始清除相关缓存，智能体ID: {}", agentId);

            // 1. 获取智能体信息
            AgentEntity agent = agentDao.selectById(agentId);
            if (agent == null) {
                log.warn("智能体不存在，无法清除缓存，智能体ID: {}", agentId);
                return;
            }

            // 2. 如果智能体有关联用户，获取该用户的所有电脑设备
            if (agent.getUserId() != null) {
                List<String> clearedDeviceIds = clearUserComputerDevicesOnWebSocketClosed(agent.getUserId());
                log.info("智能体WebSocket连接关闭，清除用户电脑设备缓存完成，智能体ID: {}, 用户ID: {}, 清除设备数量: {}",
                        agentId, agent.getUserId(), clearedDeviceIds.size());
            }

            // 3. 清除智能体相关的其他缓存
            clearAgentRelatedCaches(agentId);

        } catch (Exception e) {
            log.error("智能体WebSocket连接关闭时清除缓存失败，智能体ID: {}", agentId, e);
        }
    }

    /**
     * 专门用于WebSocket连接关闭时清除用户电脑设备缓存
     * 返回清除的设备ID列表，用于日志记录
     */
    private List<String> clearUserComputerDevicesOnWebSocketClosed(Long userId) {
        List<String> clearedDeviceIds = new ArrayList<>();

        if (userId == null) {
            return clearedDeviceIds;
        }

        try {
            log.info("开始清除用户电脑设备缓存，用户ID: {}", userId);

            // 查找所有 device:device:* 的key
            String pattern = "device:device:*";
            Set<String> allDeviceKeys = stringRedisTemplate.keys(pattern);
            log.info("找到所有设备缓存key数量: {}", allDeviceKeys != null ? allDeviceKeys.size() : 0);

            if (allDeviceKeys != null && !allDeviceKeys.isEmpty()) {
                List<String> userDeviceKeys = new ArrayList<>();
                int agentModelsDeleteCount = 0;

                // 遍历所有设备缓存，找到属于该用户的设备
                for (String deviceKey : allDeviceKeys) {
                    try {
                        String jsonStr = stringRedisTemplate.opsForValue().get(deviceKey);
                        if (StringUtils.isNotBlank(jsonStr)) {
                            // 检查JSON中是否包含该用户ID
                            if (jsonStr.contains("\"userId\":" + userId)) {
                                userDeviceKeys.add(deviceKey);

                                // 提取设备ID
                                String deviceId = deviceKey.replace("device:device:", "");
                                clearedDeviceIds.add(deviceId);
                                log.info("找到用户设备，设备ID: {}, 用户ID: {}", deviceId, userId);

                                // 清除对应的agent:models缓存
                                String agentModelsPattern = "agent:models:" + deviceId + ":*";
                                Set<String> agentModelsKeys = stringRedisTemplate.keys(agentModelsPattern);
                                log.info("查找agent:models缓存，模式: {}, 找到数量: {}",
                                        agentModelsPattern, agentModelsKeys != null ? agentModelsKeys.size() : 0);

                                if (agentModelsKeys != null && !agentModelsKeys.isEmpty()) {
                                    for (String key : agentModelsKeys) {
                                        log.info("删除agent:models缓存key: {}", key);
                                    }
                                    stringRedisTemplate.delete(agentModelsKeys);
                                    agentModelsDeleteCount += agentModelsKeys.size();
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("检查设备缓存失败，key: {}", deviceKey, e);
                    }
                }

                // 批量删除该用户的设备缓存
                if (!userDeviceKeys.isEmpty()) {
                    stringRedisTemplate.delete(userDeviceKeys);
                    log.info("删除用户设备缓存，用户ID: {}, 设备缓存数量: {}, agent:models缓存数量: {}",
                            userId, userDeviceKeys.size(), agentModelsDeleteCount);
                }
            }
        } catch (Exception e) {
            log.error("WebSocket连接关闭时清除用户电脑设备缓存失败，用户ID: {}", userId, e);
        }

        return clearedDeviceIds;
    }

    /**
     * 清除用户所有电脑设备的agent:models缓存（智能体修改时调用）
     * 只清除agent:models缓存，不清除设备信息缓存
     */
    private void clearUserComputerDeviceModelsCache(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            log.info("智能体修改，开始清除用户电脑设备的agent:models缓存，用户ID: {}", userId);

            // 直接查找所有 agent:models:* 的key
            String pattern = "agent:models:*";
            Set<String> allAgentModelsKeys = stringRedisTemplate.keys(pattern);
            log.info("查找agent:models缓存，模式: {}, 找到总数量: {}", pattern, allAgentModelsKeys != null ? allAgentModelsKeys.size() : 0);

            if (allAgentModelsKeys != null && !allAgentModelsKeys.isEmpty()) {
                List<String> keysToDelete = new ArrayList<>();
                List<String> userDeviceIds = new ArrayList<>();

                // 遍历所有agent:models缓存，找到包含该用户ID的缓存
                for (String agentModelsKey : allAgentModelsKeys) {
                    try {
                        String jsonStr = stringRedisTemplate.opsForValue().get(agentModelsKey);

                        if (StringUtils.isNotBlank(jsonStr)) {
                            // 检查JSON中是否包含该用户ID（在computer_device对象中）
                            String userIdPattern = "\"userId\":" + userId;
                            boolean containsUserId = jsonStr.contains(userIdPattern);

                            if (containsUserId) {
                                keysToDelete.add(agentModelsKey);
                                log.info("找到包含用户ID {}的agent:models缓存: {}", userId, agentModelsKey);

                                // 从key中提取设备ID，用于清除用户设备智能体关联缓存
                                // key格式: agent:models:{deviceId}:{hashCode}
                                String[] keyParts = agentModelsKey.split(":");
                                if (keyParts.length >= 3) {
                                    String deviceId = keyParts[2];
                                    if (!userDeviceIds.contains(deviceId)) {
                                        userDeviceIds.add(deviceId);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("检查agent:models缓存失败，key: {}", agentModelsKey, e);
                    }
                }

                // 批量删除找到的缓存
                if (!keysToDelete.isEmpty()) {
                    stringRedisTemplate.delete(keysToDelete);
                    log.info("智能体修改，清除用户电脑设备agent:models缓存完成，用户ID: {}, 清除缓存数量: {}",
                            userId, keysToDelete.size());

                    // 打印删除的具体key
                    for (String key : keysToDelete) {
                        log.info("智能体修改，删除agent:models缓存key: {}", key);
                    }

                    // 清除电脑设备的用户设备智能体关联缓存
                    for (String deviceId : userDeviceIds) {
                        String computerDeviceKey = RedisKeys.getAgentByUserDeviceKey(userId, deviceId);
                        redisUtils.delete(computerDeviceKey);
                        log.info("智能体修改，删除电脑设备智能体关联缓存key: {}", computerDeviceKey);
                    }
                } else {
                    log.info("智能体修改，未找到包含用户ID {}的agent:models缓存", userId);
                }
            } else {
                log.info("智能体修改，未找到任何agent:models缓存");
            }
        } catch (Exception e) {
            log.error("智能体修改时清除用户电脑设备agent:models缓存失败，用户ID: {}", userId, e);
        }
    }

    /**
     * 根据设备ID清除agent:models缓存
     * 这是一个通用方法，可以直接根据设备ID清除相关的配置缓存
     *
     * @param deviceId 设备ID
     * @return 清除的缓存数量
     */
    public int clearAgentModelsCacheByDeviceId(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return 0;
        }

        try {
            String pattern = "agent:models:" + deviceId + ":*";
            Set<String> keys = stringRedisTemplate.keys(pattern);

            if (keys != null && !keys.isEmpty()) {
                log.info("清除设备配置缓存，设备ID: {}, 模式: {}, 找到缓存数量: {}", deviceId, pattern, keys.size());
                for (String key : keys) {
                    log.info("删除缓存key: {}", key);
                }
                stringRedisTemplate.delete(keys);
                return keys.size();
            } else {
                log.info("未找到设备配置缓存，设备ID: {}, 模式: {}", deviceId, pattern);
                return 0;
            }
        } catch (Exception e) {
            log.error("清除设备配置缓存失败，设备ID: {}", deviceId, e);
            return 0;
        }
    }

    /**
     * 调试方法：直接测试清除特定用户的电脑设备缓存
     * 可以通过接口调用来调试缓存清除逻辑
     */
    public void debugClearUserComputerDeviceModelsCache(Long userId) {
        log.info("=== 开始调试清除用户电脑设备缓存，用户ID: {} ===", userId);

        // 1. 先检查是否有agent:models缓存
        String agentModelsPattern = "agent:models:*";
        Set<String> allAgentModelsKeys = stringRedisTemplate.keys(agentModelsPattern);
        log.info("所有agent:models缓存数量: {}", allAgentModelsKeys != null ? allAgentModelsKeys.size() : 0);
        if (allAgentModelsKeys != null) {
            for (String key : allAgentModelsKeys) {
                log.info("agent:models缓存key: {}", key);
            }
        }

        // 2. 调用实际的清除方法
        clearUserComputerDeviceModelsCache(userId);

        log.info("=== 调试清除用户电脑设备缓存完成 ===");
    }

    @Override
    public void updateAgentSummaryMemoryInCache(String agentId, String summaryMemory) {
        if (StringUtils.isBlank(agentId)) {
            return;
        }

        try {
            log.info("开始更新缓存中的summaryMemory，智能体ID: {}", agentId);

            // 查找所有包含该智能体的agent:models缓存
            String pattern = "agent:models:*";
            Set<String> allAgentModelsKeys = stringRedisTemplate.keys(pattern);

            if (allAgentModelsKeys != null && !allAgentModelsKeys.isEmpty()) {
                int updatedCount = 0;

                for (String cacheKey : allAgentModelsKeys) {
                    try {
                        String jsonStr = stringRedisTemplate.opsForValue().get(cacheKey);
                        if (StringUtils.isNotBlank(jsonStr)) {
                            // 检查是否包含该智能体的配置
                            if (jsonStr.contains("\"agentId\":\"" + agentId + "\"")) {
                                // 使用JSONObject解析和更新
                                com.alibaba.fastjson2.JSONObject jsonObj = com.alibaba.fastjson2.JSONObject.parseObject(jsonStr);

                                // 更新summaryMemory字段
                                jsonObj.put("summaryMemory", summaryMemory);

                                // 将更新后的JSON写回缓存
                                String updatedJsonStr = jsonObj.toJSONString();
                                stringRedisTemplate.opsForValue().set(cacheKey, updatedJsonStr);

                                updatedCount++;
                                log.info("更新缓存中的summaryMemory，key: {}", cacheKey);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("更新单个缓存失败，key: {}", cacheKey, e);
                    }
                }

                log.info("更新缓存中的summaryMemory完成，智能体ID: {}, 更新缓存数量: {}", agentId, updatedCount);
            } else {
                log.info("未找到任何agent:models缓存，智能体ID: {}", agentId);
            }
        } catch (Exception e) {
            log.error("更新缓存中的summaryMemory失败，智能体ID: {}", agentId, e);
        }
    }
}
