package xiaozhi.modules.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 电脑设备信息VO
 * 用于包装从gRPC获取的设备信息
 */
@Data
@Schema(description = "电脑设备信息")
public class ComputerDeviceInfo {
    
    @Schema(description = "设备ID")
    private long id;
    
    @Schema(description = "设备唯一标识码")
    private String deviceId;
    
    @Schema(description = "设备自定义名称")
    private String deviceName;
    
    @Schema(description = "硬件特征哈希值")
    private String hardwareHash;
    
    @Schema(description = "CPU信息")
    private String cpuInfo;
    
    @Schema(description = "内存信息")
    private String memoryInfo;
    
    @Schema(description = "磁盘信息")
    private String diskInfo;
    
    @Schema(description = "网络信息")
    private String networkInfo;
    
    @Schema(description = "显卡信息")
    private String gpuInfo;
    
    @Schema(description = "操作系统名称")
    private String osName;
    
    @Schema(description = "操作系统版本")
    private String osVersion;
    
    @Schema(description = "系统架构")
    private String osArch;
    
    @Schema(description = "主机名")
    private String hostname;
    
    @Schema(description = "当前用户名")
    private String username;
    
    @Schema(description = "用户主目录")
    private String userHomeDir;
    
    @Schema(description = "工作目录")
    private String workDir;
    
    @Schema(description = "应用版本")
    private String appVersion;
    
    @Schema(description = "应用构建号")
    private String appBuildNo;
    
    @Schema(description = "IP地址")
    private String ipAddress;
    
    @Schema(description = "MAC地址")
    private String macAddress;
    
    @Schema(description = "关联用户ID")
    private long userId;
    
    @Schema(description = "状态")
    private int status;
    
    @Schema(description = "是否活跃")
    private boolean active;

    @Schema(description = "上报次数")
    private long reportCount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否默认")
    private boolean defaultDevice;

    @Schema(description = "MCP访问地址")
    private String mcpAccessAddress;

    @Schema(description = "关联的智能体ID")
    private String agentId;

    @Schema(description = "在线状态信息")
    private OnlineStatus onlineStatus;
    
    /**
     * 设备在线状态信息
     */
    @Data
    @Schema(description = "设备在线状态信息")
    public static class OnlineStatus {
        
        @Schema(description = "是否在线")
        private boolean online;
        
        @Schema(description = "会话类型")
        private String sessionType;
        
        @Schema(description = "登录IP地址")
        private String ipAddress;
        
        @Schema(description = "用户代理")
        private String userAgent;
        
        @Schema(description = "设备名称")
        private String deviceName;
        
        @Schema(description = "操作系统信息")
        private String osInfo;
        
        @Schema(description = "应用版本")
        private String appVersion;
    }
}
