package xiaozhi.modules.timbre.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * MiniMax T2A V2 API响应DTO
 *
 */
@Data
public class MiniMaxT2AV2Response {

    /**
     * 音频数据
     */
    private AudioData data;

    /**
     * 额外信息
     */
    @JsonProperty("extra_info")
    private ExtraInfo extraInfo;

    /**
     * 追踪ID
     */
    @JsonProperty("trace_id")
    private String traceId;

    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private BaseResp baseResp;

    /**
     * 音频数据内部类
     */
    @Data
    public static class AudioData {
        /**
         * 音频数据（十六进制编码）
         */
        private String audio;

        /**
         * 状态码
         */
        private Integer status;
    }

    /**
     * 额外信息内部类
     */
    @Data
    public static class ExtraInfo {
        /**
         * 音频长度
         */
        @JsonProperty("audio_length")
        private Integer audioLength;

        /**
         * 音频采样率
         */
        @JsonProperty("audio_sample_rate")
        private Integer audioSampleRate;

        /**
         * 音频大小（字节）
         */
        @JsonProperty("audio_size")
        private Integer audioSize;

        /**
         * 音频比特率
         */
        @JsonProperty("audio_bitrate")
        private Integer audioBitrate;

        /**
         * 字数统计
         */
        @JsonProperty("word_count")
        private Integer wordCount;

        /**
         * 不可见字符比例
         */
        @JsonProperty("invisible_character_ratio")
        private Double invisibleCharacterRatio;

        /**
         * 音频格式
         */
        @JsonProperty("audio_format")
        private String audioFormat;

        /**
         * 使用字符数
         */
        @JsonProperty("usage_characters")
        private Integer usageCharacters;
    }

    /**
     * 基础响应信息内部类
     */
    @Data
    public static class BaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;

        @JsonProperty("status_msg")
        private String statusMsg;
    }

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() == 0;
    }

    /**
     * 获取音频数据（十六进制编码）
     */
    public String getAudioData() {
        return data != null ? data.getAudio() : null;
    }

    /**
     * 获取音频状态
     */
    public Integer getAudioStatus() {
        return data != null ? data.getStatus() : null;
    }

    /**
     * 获取音频格式
     */
    public String getAudioFormat() {
        return extraInfo != null ? extraInfo.getAudioFormat() : null;
    }

    /**
     * 获取音频长度
     */
    public Integer getAudioLength() {
        return extraInfo != null ? extraInfo.getAudioLength() : null;
    }

    /**
     * 获取音频大小（字节）
     */
    public Integer getAudioSize() {
        return extraInfo != null ? extraInfo.getAudioSize() : null;
    }

    /**
     * 获取音频采样率
     */
    public Integer getAudioSampleRate() {
        return extraInfo != null ? extraInfo.getAudioSampleRate() : null;
    }

    /**
     * 获取音频比特率
     */
    public Integer getAudioBitrate() {
        return extraInfo != null ? extraInfo.getAudioBitrate() : null;
    }

    /**
     * 获取字数统计
     */
    public Integer getWordCount() {
        return extraInfo != null ? extraInfo.getWordCount() : null;
    }

    /**
     * 获取使用字符数
     */
    public Integer getUsageCharacters() {
        return extraInfo != null ? extraInfo.getUsageCharacters() : null;
    }
}
