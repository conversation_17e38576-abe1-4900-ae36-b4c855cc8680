package xiaozhi.modules.timbre.service;

import org.springframework.web.multipart.MultipartFile;
import xiaozhi.modules.timbre.dto.MiniMaxCloneResponse;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Request;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Response;
import xiaozhi.modules.timbre.dto.MiniMaxUploadResponse;
import xiaozhi.modules.timbre.dto.VoiceCloneDTO;

/**
 * MiniMax API服务接口
 */
public interface MiniMaxApiService {

    /**
     * 上传音频文件到MiniMax
     *
     * @param file 音频文件
     * @param purpose 文件用途
     * @return 上传响应
     */
    MiniMaxUploadResponse uploadAudioFile(MultipartFile file, String purpose);

    /**
     * 进行声音复刻
     *
     * @param dto 声音复刻参数
     * @return 复刻响应
     */
    MiniMaxCloneResponse cloneVoice(VoiceCloneDTO dto);

    /**
     * 生成符合规则的voiceId
     *
     * @param prefix 前缀（可选），如果为空则使用默认前缀
     * @return 生成的voiceId
     */
    String generateVoiceId(String prefix);

    /**
     * 验证voiceId是否符合规则
     *
     * @param voiceId 要验证的voiceId
     * @throws xiaozhi.common.exception.RenException 如果验证失败
     */
    void validateVoiceId(String voiceId);

    /**
     * 调用T2A V2 API进行语音合成
     *
     * @param request T2A V2请求参数
     * @return T2A V2响应
     */
    MiniMaxT2AV2Response textToAudioV2(MiniMaxT2AV2Request request);

}
