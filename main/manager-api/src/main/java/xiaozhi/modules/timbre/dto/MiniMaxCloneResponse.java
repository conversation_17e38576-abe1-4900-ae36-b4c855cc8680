package xiaozhi.modules.timbre.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * MiniMax声音复刻响应
 */
@Data
public class MiniMaxCloneResponse {

    /**
     * 输入是否敏感
     */
    @JsonProperty("input_sensitive")
    private Boolean inputSensitive;

    /**
     * 输入敏感类型
     */
    @JsonProperty("input_sensitive_type")
    private Integer inputSensitiveType;

    /**
     * 演示音频
     */
    @JsonProperty("demo_audio")
    private String demoAudio;

    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private BaseResp baseResp;

    /**
     * 基础响应信息内部类
     */
    @Data
    public static class BaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;

        @JsonProperty("status_msg")
        private String statusMsg;
    }

    /**
     * 获取音色ID（兼容性方法）
     * 注意：新的API响应中可能不直接包含voice_id，需要根据实际API文档调整
     */
    @JsonProperty
    private String voiceId;

    @JsonProperty
    private Long fileId;

    /**
     * 获取状态（兼容性方法）
     */
    public String getStatus() {
        if (baseResp != null && baseResp.getStatusMsg() != null) {
            return baseResp.getStatusCode() == 0 ? "success" : "failed";
        }
        return null;
    }

    /**
     * 获取创建时间（兼容性方法）
     * 注意：新的API响应中可能不包含created_at，需要根据实际API文档调整
     */
    public Long getCreatedAt() {
        // 根据实际API响应调整此方法
        // 如果新API在其他字段中返回时间戳，请相应修改
        return null;
    }

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() == 0;
    }
}