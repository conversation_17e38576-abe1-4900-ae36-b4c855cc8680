package xiaozhi.modules.timbre.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import xiaozhi.common.config.MiniMaxProperties;
import xiaozhi.common.exception.RenException;
import xiaozhi.modules.timbre.dao.TimbreDao;
import xiaozhi.modules.timbre.dto.MiniMaxCloneResponse;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Request;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Response;
import xiaozhi.modules.timbre.dto.MiniMaxUploadResponse;
import xiaozhi.modules.timbre.dto.VoiceCloneDTO;
import xiaozhi.modules.timbre.entity.TimbreEntity;
import xiaozhi.modules.timbre.service.MiniMaxApiService;

import java.io.IOException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * MiniMax API服务实现
 *
 */
@Slf4j
@AllArgsConstructor
@Service
public class MiniMaxApiServiceImpl implements MiniMaxApiService {

    private final RestTemplate restTemplate;
    private final MiniMaxProperties miniMaxProperties;
    private final TimbreDao timbreDao;

    // voiceId验证正则表达式
    private static final Pattern VOICE_ID_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]$");
    private static final SecureRandom RANDOM = new SecureRandom();

    @Override
    public MiniMaxUploadResponse uploadAudioFile(MultipartFile file, String purpose) {
        try {
            log.info("开始上传音频文件到MiniMax，文件名: {}, 大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(miniMaxProperties.getApiKey());
            headers.add("authority", "api.minimax.chat");

            // 构建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            
            // 添加文件
            ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file", fileResource);
            body.add("purpose", purpose);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求 - 将GroupId作为查询参数
            String url = miniMaxProperties.getUploadUrl() + "?GroupId=" + miniMaxProperties.getGroupId();
            log.info("调用MiniMax文件上传接口，URL: {}", url);
            
            ResponseEntity<MiniMaxUploadResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    MiniMaxUploadResponse.class
            );

            MiniMaxUploadResponse result = response.getBody();
            log.info("MiniMax文件上传响应: {}", result);

            if (result == null) {
                throw new RenException("文件上传失败，未获取到响应");
            }

            // 检查响应状态
            if (!result.isSuccess()) {
                String errorMsg = result.getBaseResp() != null ? result.getBaseResp().getStatusMsg() : "未知错误";
                throw new RenException("文件上传失败: " + errorMsg);
            }

            // 检查文件信息
            if (result.getFile() == null || result.getFileId() == null) {
                throw new RenException("文件上传失败，未获取到file_id");
            }

            log.info("MiniMax文件上传成功，file_id: {}, filename: {}",
                    result.getFileId(), result.getFilename());

            return result;

        } catch (IOException e) {
            log.error("读取文件内容失败", e);
            throw new RenException("读取文件内容失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("上传音频文件到MiniMax失败", e);
            throw new RenException("上传音频文件失败: " + e.getMessage());
        }
    }

    @Override
    public MiniMaxCloneResponse cloneVoice(VoiceCloneDTO dto) {
        try {
            log.info("开始进行声音复刻，fileId: {}, voiceId: {}", dto.getFileId(), dto.getVoiceId());

            // 如果用户没有提供voiceId，则生成一个
            String voiceId = dto.getVoiceId();
            if (StringUtils.isEmpty(voiceId)) {
                voiceId = generateVoiceId(null);
                dto.setVoiceId(voiceId);
                log.info("用户未提供voiceId，自动生成: {}", voiceId);
            } else {
                // 验证用户提供的voiceId是否符合规则
                validateVoiceId(voiceId);
                log.info("使用用户提供的voiceId: {}", voiceId);
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(miniMaxProperties.getApiKey());
            headers.add("authority", "api.minimax.chat");

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("file_id", dto.getFileId());
            requestBody.put("voice_id", voiceId);

            if (dto.getVoiceName() != null) {
                requestBody.put("voice_name", dto.getVoiceName());
            }
            if (dto.getDescription() != null) {
                requestBody.put("description", dto.getDescription());
            }

            log.info("MiniMax声音复刻请求体: {}", requestBody);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求 - 将GroupId作为查询参数
            String url = miniMaxProperties.getCloneUrl() + "?GroupId=" + miniMaxProperties.getGroupId();
            log.info("调用MiniMax声音复刻接口，URL: {}", url);

            ResponseEntity<MiniMaxCloneResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    MiniMaxCloneResponse.class
            );

            MiniMaxCloneResponse result = response.getBody();
            log.info("MiniMax声音复刻响应: {}", result);

            if (result == null) {
                throw new RenException("声音复刻失败，未获取到响应");
            }

            // 检查响应状态
            if (!result.isSuccess()) {
                String errorMsg = result.getBaseResp() != null ? result.getBaseResp().getStatusMsg() : "未知错误";
                throw new RenException("声音复刻失败: " + errorMsg);
            }

            log.info("MiniMax声音复刻成功，输入敏感性: {}, 演示音频: {}",
                    result.getInputSensitive(), result.getDemoAudio());

            result.setVoiceId(voiceId);
            result.setFileId(dto.getFileId());
            return result;

        } catch (Exception e) {
            log.error("MiniMax声音复刻失败", e);
            throw new RenException("声音复刻失败: " + e.getMessage());
        }
    }

    /**
     * 生成符合规则的voiceId
     *
     * @param prefix 前缀（可选），如果为空则使用默认前缀
     * @return 生成的voiceId
     */
    public String generateVoiceId(String prefix) {
        if (StringUtils.isEmpty(prefix)) {
            prefix = "aido_voice";
        }

        String voiceId;
        int attempts = 0;
        int maxAttempts = 100;

        do {
            // 生成随机后缀
            String suffix = generateRandomSuffix();
            voiceId = prefix + suffix;

            // 确保长度在合理范围内
            if (voiceId.length() > 256) {
                voiceId = voiceId.substring(0, 256);
                // 确保末位字符不是 - 或 _
                while (voiceId.endsWith("-") || voiceId.endsWith("_")) {
                    voiceId = voiceId.substring(0, voiceId.length() - 1);
                }
            }

            attempts++;
            if (attempts >= maxAttempts) {
                throw new RenException("生成voiceId失败，尝试次数过多");
            }

        } while (isVoiceIdExists(voiceId));

        return voiceId;
    }

    /**
     * 验证voiceId是否符合规则
     *
     * @param voiceId 要验证的voiceId
     * @throws RenException 如果验证失败
     */
    public void validateVoiceId(String voiceId) {
        if (StringUtils.isEmpty(voiceId)) {
            throw new RenException("voiceId不能为空");
        }

        // 检查长度 - 放宽长度限制，允许更短的ID
        if (voiceId.length() < 1 || voiceId.length() > 256) {
            throw new RenException("voiceId长度必须在1-256个字符之间");
        }

        // 放宽字符要求，允许数字开头和更多字符类型
        // 只检查是否包含非法字符（空格、特殊符号等）
        if (!voiceId.matches("^[a-zA-Z0-9_-]+$")) {
            throw new RenException("voiceId只能包含数字、字母、-、_ 字符");
        }

        log.info("voiceId验证通过: {}", voiceId);
    }

    /**
     * 检查voiceId是否存在于数据库中
     *
     * @param voiceId 要检查的voiceId
     * @return true如果存在，false如果不存在
     */
    private boolean isVoiceIdExists(String voiceId) {
        QueryWrapper<TimbreEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", voiceId);
        return timbreDao.selectCount(queryWrapper) > 0;
    }

    /**
     * 生成随机后缀
     *
     * @return 随机后缀字符串
     */
    private String generateRandomSuffix() {
        // 生成3位随机数字
        int randomNum = RANDOM.nextInt(1000);
        return String.format("%03d", randomNum);
    }

    @Override
    public MiniMaxT2AV2Response textToAudioV2(MiniMaxT2AV2Request request) {
        try {
            log.info("开始调用MiniMax T2A V2 API，文本: {}, 音色ID: {}",
                    request.getText(),
                    request.getVoiceSetting() != null ? request.getVoiceSetting().getVoiceId() : "未指定");

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(miniMaxProperties.getApiKey());

            // 构建请求体
            HttpEntity<MiniMaxT2AV2Request> requestEntity = new HttpEntity<>(request, headers);

            // 发送请求 - 将GroupId作为查询参数
            String url = miniMaxProperties.getT2aV2Url() + "?GroupId=" + miniMaxProperties.getGroupId();
            log.info("调用MiniMax T2A V2接口，URL: {}", url);

            ResponseEntity<MiniMaxT2AV2Response> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    MiniMaxT2AV2Response.class
            );

            MiniMaxT2AV2Response result = response.getBody();
            log.info("MiniMax T2A V2响应状态: {}", result != null ? result.isSuccess() : "null");

            if (result == null) {
                throw new RenException("T2A V2调用失败，未获取到响应");
            }

            // 检查响应状态
            if (!result.isSuccess()) {
                String errorMsg = result.getBaseResp() != null ? result.getBaseResp().getStatusMsg() : "未知错误";
                throw new RenException("T2A V2调用失败: " + errorMsg);
            }


            return result;

        } catch (Exception e) {
            log.error("MiniMax T2A V2调用失败", e);
            throw new RenException("T2A V2调用失败: " + e.getMessage());
        }
    }
}
