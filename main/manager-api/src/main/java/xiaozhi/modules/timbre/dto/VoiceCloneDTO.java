package xiaozhi.modules.timbre.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 声音复刻DTO
 *
 */
@Data
@Schema(description = "声音复刻参数")
public class VoiceCloneDTO {

    @Schema(description = "文件ID", required = true)
    private Long fileId;

    @Schema(description = "自定义音色ID")
    private String voiceId;

    @Schema(description = "音色名称")
    private String voiceName;

    @Schema(description = "音色描述")
    private String description;

    @Schema(description = "userId")
    private Long userId;
}
