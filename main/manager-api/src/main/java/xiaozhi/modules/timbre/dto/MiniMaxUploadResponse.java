package xiaozhi.modules.timbre.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * MiniMax文件上传响应
 */
@Data
public class MiniMaxUploadResponse {

    /**
     * 文件信息
     */
    private FileInfo file;

    /**
     * 基础响应信息
     */
    @JsonProperty("base_resp")
    private BaseResp baseResp;

    /**
     * 文件信息内部类
     */
    @Data
    public static class FileInfo {
        @JsonProperty("file_id")
        private Long fileId;

        private Long bytes;

        @JsonProperty("created_at")
        private Long createdAt;

        private String filename;

        private String purpose;
    }

    /**
     * 基础响应信息内部类
     */
    @Data
    public static class BaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;

        @JsonProperty("status_msg")
        private String statusMsg;
    }

    /**
     * 获取文件ID（兼容性方法）
     */
    public Long getFileId() {
        return file != null ? file.getFileId() : null;
    }

    /**
     * 获取文件名（兼容性方法）
     */
    public String getFilename() {
        return file != null ? file.getFilename() : null;
    }

    /**
     * 获取创建时间（兼容性方法）
     */
    public Long getCreatedAt() {
        return file != null ? file.getCreatedAt() : null;
    }

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return baseResp != null && baseResp.getStatusCode() != null && baseResp.getStatusCode() == 0;
    }
}