package xiaozhi.modules.timbre.controller;

import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.modules.agent.dto.McpcnUserInfoResponseDTO;
import xiaozhi.modules.agent.service.McpcnApiService;
import xiaozhi.modules.timbre.dto.*;
import xiaozhi.modules.timbre.entity.TimbreEntity;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.timbre.vo.TimbreDetailsVO;

/**
 * 音色控制层
 *
 * <AUTHOR>
 * @since 2025-3-21
 */
@AllArgsConstructor
@RestController
@RequestMapping("/ttsVoice")
@Tag(name = "音色管理")
public class TimbreController {
    private final TimbreService timbreService;

    @Autowired
    private McpcnApiService mcpcnApiService;

    @GetMapping
    @Operation(summary = "分页查找")
    @RequiresPermissions("sys:role:superAdmin")
    @Parameters({
            @Parameter(name = "ttsModelId", description = "对应 TTS 模型主键", required = true),
            @Parameter(name = "name", description = "音色名称"),
            @Parameter(name = Constant.PAGE, description = "当前页码，从1开始", required = true),
            @Parameter(name = Constant.LIMIT, description = "每页显示记录数", required = true),
    })
    public Result<PageData<TimbreDetailsVO>> page(
            @Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        TimbrePageDTO dto = new TimbrePageDTO();
        dto.setTtsModelId((String) params.get("ttsModelId"));
        dto.setName((String) params.get("name"));
        dto.setLimit((String) params.get(Constant.LIMIT));
        dto.setPage((String) params.get(Constant.PAGE));

        ValidatorUtils.validateEntity(dto);
        PageData<TimbreDetailsVO> page = timbreService.page(dto);
        return new Result<PageData<TimbreDetailsVO>>().ok(page);
    }

    @PostMapping
    @Operation(summary = "音色保存")
    @RequiresPermissions("sys:role:superAdmin")
    public Result<Void> save(@RequestBody TimbreDataDTO dto) {
        ValidatorUtils.validateEntity(dto);
        timbreService.save(dto);
        return new Result<>();
    }

    @PutMapping("/{id}")
    @Operation(summary = "音色修改")
    @RequiresPermissions("sys:role:superAdmin")
    public Result<Void> update(
            @PathVariable String id,
            @RequestBody TimbreDataDTO dto) {
        ValidatorUtils.validateEntity(dto);
        timbreService.update(id, dto);
        return new Result<>();
    }

    @PostMapping("/delete")
    @Operation(summary = "音色删除")
    @RequiresPermissions("sys:role:superAdmin")
    public Result<Void> delete(@RequestBody String[] ids) {
        timbreService.delete(ids);
        return new Result<>();
    }


    @PostMapping("/upload-clone-voice")
    @Operation(summary = "上传音频复刻音色")
    public Result<TimbreEntity> uploadAudio(@RequestParam("file") MultipartFile file,
                                                    @RequestParam(value = "purpose", defaultValue = "voice_clone") String purpose,
                                                    @RequestParam(value = "name", defaultValue = "复刻音频") String name,HttpServletRequest request){

        String token = request.getHeader("x-token");
        // 调用外部接口获取用户信息
        McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

        // 如果外部接口调用失败，返回相应的错误码
        if (userInfoResponse.getCode() != 0) {
            return new Result<TimbreEntity>().error(7, userInfoResponse.getMsg());
        }

        if (file.isEmpty()) {
            return new Result<TimbreEntity>().error("上传文件不能为空");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return new Result<TimbreEntity>().error("文件名不能为空");
        }
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        if (!extension.equals(".wav") && !extension.equals(".mp3") && !extension.equals(".m4a")) {
            return new Result<TimbreEntity>().error("只允许上传.wav、.mp3、.m4a格式的音频文件");
        }

        MiniMaxUploadResponse response = timbreService.uploadAudioFile(file, purpose);

        VoiceCloneDTO voiceCloneDTO = new VoiceCloneDTO();
        voiceCloneDTO.setFileId(response.getFileId());
        voiceCloneDTO.setVoiceName(name);
        voiceCloneDTO.setUserId(userInfoResponse.getData().getUserInfo().getId());
        TimbreEntity responseClone = timbreService.cloneVoice(voiceCloneDTO);
        return new Result<TimbreEntity>().ok(responseClone);
    }

}