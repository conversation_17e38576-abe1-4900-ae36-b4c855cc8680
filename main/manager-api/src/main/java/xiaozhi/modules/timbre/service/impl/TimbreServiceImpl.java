package xiaozhi.modules.timbre.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.model.dto.VoiceDTO;
import xiaozhi.modules.timbre.dao.TimbreDao;
import xiaozhi.modules.timbre.dto.MiniMaxCloneResponse;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Request;
import xiaozhi.modules.timbre.dto.MiniMaxT2AV2Response;
import xiaozhi.modules.timbre.dto.MiniMaxUploadResponse;
import xiaozhi.modules.timbre.dto.TimbreDataDTO;
import xiaozhi.modules.timbre.dto.TimbrePageDTO;
import xiaozhi.modules.timbre.dto.VoiceCloneDTO;
import xiaozhi.modules.timbre.entity.TimbreEntity;
import xiaozhi.modules.timbre.service.MiniMaxApiService;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.timbre.vo.TimbreDetailsVO;
import xiaozhi.modules.config.service.AgentModelsCacheService;

/**
 * 音色的业务层的实现
 * 
 * <AUTHOR>
 * @since 2025-3-21
 */
@Slf4j
@AllArgsConstructor
@Service
public class TimbreServiceImpl extends BaseServiceImpl<TimbreDao, TimbreEntity> implements TimbreService {

    private final TimbreDao timbreDao;
    private final RedisUtils redisUtils;
    private final AgentModelsCacheService agentModelsCacheService;
    private final MiniMaxApiService miniMaxApiService;

    @Override
    public PageData<TimbreDetailsVO> page(TimbrePageDTO dto) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(Constant.PAGE, dto.getPage());
        params.put(Constant.LIMIT, dto.getLimit());
        IPage<TimbreEntity> page = baseDao.selectPage(
                getPage(params, null, true),
                // 定义查询条件
                new QueryWrapper<TimbreEntity>()
                        // 必须按照ttsID查找
                        .eq("tts_model_id", dto.getTtsModelId())
                        // 如果有音色名字，按照音色名模糊查找
                        .like(StringUtils.isNotBlank(dto.getName()), "name", dto.getName()));

        return getPageData(page, TimbreDetailsVO.class);
    }

    @Override
    public TimbreDetailsVO get(String timbreId) {
        if (StringUtils.isBlank(timbreId)) {
            return null;
        }

        // 先从Redis获取缓存
        String key = RedisKeys.getTimbreDetailsKey(timbreId);
        TimbreDetailsVO cachedDetails = (TimbreDetailsVO) redisUtils.get(key);
        if (cachedDetails != null) {
            return cachedDetails;
        }

        // 如果缓存中没有，则从数据库获取
        TimbreEntity entity = baseDao.selectById(timbreId);
        if (entity == null) {
            return null;
        }

        // 转换为VO对象
        TimbreDetailsVO details = ConvertUtils.sourceToTarget(entity, TimbreDetailsVO.class);

        // 存入Redis缓存
        if (details != null) {
            redisUtils.set(key, details);
        }

        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TimbreDataDTO dto) {
        isTtsModelId(dto.getTtsModelId());
        TimbreEntity timbreEntity = ConvertUtils.sourceToTarget(dto, TimbreEntity.class);
        baseDao.insert(timbreEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String timbreId, TimbreDataDTO dto) {
        isTtsModelId(dto.getTtsModelId());
        TimbreEntity timbreEntity = ConvertUtils.sourceToTarget(dto, TimbreEntity.class);
        timbreEntity.setId(timbreId);
        baseDao.updateById(timbreEntity);
        // 删除缓存
        redisUtils.delete(RedisKeys.getTimbreDetailsKey(timbreId));

        // 清除相关的智能体模型配置缓存
        agentModelsCacheService.clearTimbreRelatedCaches(timbreId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String[] ids) {
        baseDao.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public List<VoiceDTO> getVoiceNames(String ttsModelId, String voiceName,Long UserId) {
        QueryWrapper<TimbreEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tts_model_id", StringUtils.isBlank(ttsModelId) ? "" : ttsModelId);
        if (StringUtils.isNotBlank(voiceName)) {
            queryWrapper.like("name", voiceName);
        }

        // 根据userId参数构建creator查询条件
        if (UserId != null) {
            // 当传了userId时，查询creator为空或者creator是自己的数据
            queryWrapper.and(wrapper -> wrapper.isNull("creator").or().eq("creator", UserId));
        } else {
            // 当没传userId时，只查询creator为空的数据
            queryWrapper.isNull("creator");
        }

        List<TimbreEntity> timbreEntities = timbreDao.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(timbreEntities)) {
            return null;
        }

        return ConvertUtils.sourceToTarget(timbreEntities, VoiceDTO.class);
    }

    /**
     * 处理是不是tts模型的id
     */
    private void isTtsModelId(String ttsModelId) {
        // 等模型配置那边写好调用方法判断
    }

    @Override
    public String getTimbreNameById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        String cachedName = (String) redisUtils.get(RedisKeys.getTimbreNameById(id));

        if (StringUtils.isNotBlank(cachedName)) {
            return cachedName;
        }

        TimbreEntity entity = timbreDao.selectById(id);
        if (entity != null) {
            String name = entity.getName();
            if (StringUtils.isNotBlank(name)) {
                redisUtils.set(RedisKeys.getTimbreNameById(id), name);
            }
            return name;
        }

        return null;
    }

    @Override
    public MiniMaxUploadResponse uploadAudioFile(MultipartFile file, String purpose) {
        return miniMaxApiService.uploadAudioFile(file, purpose);
    }

    /**
     * 根据前缀生成下一个TimbreEntity的ID
     *
     * @param prefix ID前缀，例如 "TTS_MinimaxTTS"
     * @return 下一个可用的ID，例如 "TTS_MinimaxTTS0007"
     */
    public String generateNextTimbreId(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            throw new IllegalArgumentException("前缀不能为空");
        }

        // 查询数据库中以该前缀开头的最新一条记录
        QueryWrapper<TimbreEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("id", prefix)
                   .select("id");
        queryWrapper.orderByDesc("create_date");
        queryWrapper.last("LIMIT 1");

        TimbreEntity entity = timbreDao.selectOne(queryWrapper);

        int maxNumber = 0; // 从0开始，这样第一个ID就是0001

        if (entity != null && StringUtils.isNotBlank(entity.getId())) {
            String id = entity.getId();

            // 确保ID确实以指定前缀开头
            if (id.startsWith(prefix)) {
                String numberPart = id.substring(prefix.length());

                try {
                    int currentNumber = Integer.parseInt(numberPart);
                    maxNumber = Math.max(maxNumber, currentNumber);
                } catch (NumberFormatException e) {
                    log.warn("无法解析ID中的数字部分: {}, 跳过该ID", id);
                }
            }
        }

        int nextNumber = maxNumber + 1;

        // 根据规则格式化数字部分
        String formattedNumber;
        if (nextNumber <= 9999) {
            // 1-9999: 补零到4位
            formattedNumber = String.format("%04d", nextNumber);
        } else {
            // 10000以上: 不补零
            formattedNumber = String.valueOf(nextNumber);
        }

        String generatedId = prefix + formattedNumber;
        return generatedId;
    }



    @Override
    public TimbreEntity cloneVoice(VoiceCloneDTO dto) {
        Long userId = dto.getUserId();
        String voiceName = dto.getVoiceName();
        TimbreEntity timbreEntity = new TimbreEntity();
        String id = generateNextTimbreId("TTS_MinimaxTTS");
        dto.setVoiceId(id);
        MiniMaxCloneResponse miniMaxCloneResponse = miniMaxApiService.cloneVoice(dto);
        //同步语音合成（T2A）
        if (miniMaxCloneResponse.isSuccess()) {
            try {
                // 调用T2A V2 API进行语音合成测试
                MiniMaxT2AV2Request t2aRequest = createT2AV2Request(miniMaxCloneResponse.getVoiceId());
                MiniMaxT2AV2Response t2aResponse = miniMaxApiService.textToAudioV2(t2aRequest);

                if (t2aResponse.isSuccess()) {
                    //模版id
                    String templateId = "TTS_MinimaxTTS0006";
                    TimbreEntity entity = this.selectById(templateId);
                    timbreEntity = ConvertUtils.sourceToTarget(entity, TimbreEntity.class);
                    timbreEntity.setTtsVoice(miniMaxCloneResponse.getVoiceId());
                    timbreEntity.setCreator(userId);

                    //个人的音色排序
                    LambdaQueryWrapper<TimbreEntity> timbreEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    timbreEntityLambdaQueryWrapper.eq(TimbreEntity::getCreator, userId);
                    timbreEntityLambdaQueryWrapper.orderByDesc(TimbreEntity::getSort);
                    timbreEntityLambdaQueryWrapper.last("LIMIT 1");
                    TimbreEntity userTimbre = baseDao.selectOne(timbreEntityLambdaQueryWrapper);
                    if (userTimbre != null) {
                        timbreEntity.setSort(userTimbre.getSort() + 1);
                    }else {
                        timbreEntity.setSort(1);
                    }

                    timbreEntity.setId(id);
                    timbreEntity.setName(voiceName);
                    // 保存新的音色实体
                    baseDao.insert(timbreEntity);

                } else {
                    log.warn("T2A V2调用失败，音色ID: {}", miniMaxCloneResponse.getVoiceId());
                }
            } catch (Exception e) {
                log.error("调用T2A V2 API失败，音色ID: {}", miniMaxCloneResponse.getVoiceId(), e);
                // 不影响主流程，只记录错误
            }
        }
        return timbreEntity;
    }

    /**
     * 创建T2A V2请求对象
     *
     * @param voiceId 音色ID
     * @return T2A V2请求对象
     */
    private MiniMaxT2AV2Request createT2AV2Request(String voiceId) {
        MiniMaxT2AV2Request request = new MiniMaxT2AV2Request();
        request.setModel("speech-02-hd");
        request.setText("你好，这是一个语音合成测试。");
        request.setStream(false);

        // 设置语音参数
        MiniMaxT2AV2Request.VoiceSetting voiceSetting = new MiniMaxT2AV2Request.VoiceSetting();
        voiceSetting.setVoiceId(voiceId);
        voiceSetting.setSpeed(1.0);
        voiceSetting.setVol(1.0);
        voiceSetting.setPitch(0);
        voiceSetting.setEmotion("happy");
        request.setVoiceSetting(voiceSetting);

        // 设置音频参数
        MiniMaxT2AV2Request.AudioSetting audioSetting = new MiniMaxT2AV2Request.AudioSetting();
        audioSetting.setSampleRate(32000);
        audioSetting.setBitrate(128000);
        audioSetting.setFormat("mp3");
        audioSetting.setChannel(1);
        request.setAudioSetting(audioSetting);

        return request;
    }
}