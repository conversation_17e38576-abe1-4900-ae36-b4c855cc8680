package xiaozhi.modules.timbre.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * MiniMax T2A V2 API请求DTO
 *
 */
@Data
public class MiniMaxT2AV2Request {

    /**
     * 模型名称
     */
    private String model;

    /**
     * 要合成的文本
     */
    private String text;

    /**
     * 是否流式输出
     */
    private Boolean stream = false;

    /**
     * 语音设置
     */
    @JsonProperty("voice_setting")
    private VoiceSetting voiceSetting;

    /**
     * 发音字典
     */
    @JsonProperty("pronunciation_dict")
    private Map<String, Object> pronunciationDict;

    /**
     * 音频设置
     */
    @JsonProperty("audio_setting")
    private AudioSetting audioSetting;

    /**
     * 语音设置内部类
     */
    @Data
    public static class VoiceSetting {
        /**
         * 音色ID
         */
        @JsonProperty("voice_id")
        private String voiceId;

        /**
         * 语速 (0.5-2.0)
         */
        private Double speed = 1.0;

        /**
         * 音量 (0.0-1.0)
         */
        private Double vol = 1.0;

        /**
         * 音调 (-12到12)
         */
        private Integer pitch = 0;

        /**
         * 情感
         */
        private String emotion = "happy";
    }

    /**
     * 音频设置内部类
     */
    @Data
    public static class AudioSetting {
        /**
         * 采样率
         */
        @JsonProperty("sample_rate")
        private Integer sampleRate = 32000;

        /**
         * 比特率
         */
        private Integer bitrate = 128000;

        /**
         * 音频格式
         */
        private String format = "mp3";

        /**
         * 声道数
         */
        private Integer channel = 1;
    }
}
