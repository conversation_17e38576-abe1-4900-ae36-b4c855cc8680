package xiaozhi.modules.device.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.device.entity.DeviceEntity;

@Mapper
public interface DeviceDao extends BaseMapper<DeviceEntity> {
    /**
     * 获取此智能体全部设备的最后连接时间
     *
     * @param agentId 智能体id
     * @return
     */
    Date getAllLastConnectedAtByAgentId(String agentId);

    /**
     * 根据用户ID查询设备列表
     *
     * @param userId 用户ID
     * @return 设备列表
     */
    List<DeviceEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据智能体ID查询设备列表
     *
     * @param agentId 智能体ID
     * @return 设备列表
     */
    List<DeviceEntity> selectByAgentId(@Param("agentId") String agentId);

}