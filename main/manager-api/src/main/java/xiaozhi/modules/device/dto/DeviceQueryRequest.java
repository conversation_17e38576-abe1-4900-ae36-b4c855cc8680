package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备查询请求DTO
 */
@Data
@Schema(description = "设备查询请求")
public class DeviceQueryRequest {
    
    @Schema(description = "MAC地址")
    private String macAddress;
    
    @Schema(description = "设备类型")
    private String deviceType;
    
    public DeviceQueryRequest() {
    }
    
    public DeviceQueryRequest(String macAddress) {
        this.macAddress = macAddress;
        this.deviceType = "computer";
    }
}
