package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备查询响应DTO
 */
@Data
@Schema(description = "设备查询响应")
public class DeviceQueryResponse {
    
    @Schema(description = "是否找到设备")
    private boolean found;
    
    @Schema(description = "设备ID")
    private String deviceId;
    
    @Schema(description = "设备名称")
    private String deviceName;
    
    @Schema(description = "设备类型")
    private String deviceType;
    
    @Schema(description = "MAC地址")
    private String macAddress;
    
    @Schema(description = "操作系统")
    private String operatingSystem;
    
    @Schema(description = "设备状态")
    private String status;
    
    public DeviceQueryResponse() {
    }
    
    public DeviceQueryResponse(boolean found) {
        this.found = found;
    }
}
