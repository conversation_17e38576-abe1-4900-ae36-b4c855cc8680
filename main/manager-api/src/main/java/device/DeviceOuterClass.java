// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: device/device.proto

// Protobuf Java Version: 3.25.3
package device;

public final class DeviceOuterClass {
  private DeviceOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DeviceOrBuilder extends
      // @@protoc_insertion_point(interface_extends:device.Device)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <pre>
     * 设备唯一标识码
     * </pre>
     *
     * <code>string device_id = 2;</code>
     * @return The deviceId.
     */
    java.lang.String getDeviceId();
    /**
     * <pre>
     * 设备唯一标识码
     * </pre>
     *
     * <code>string device_id = 2;</code>
     * @return The bytes for deviceId.
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();

    /**
     * <pre>
     * 设备自定义名称
     * </pre>
     *
     * <code>string device_name = 3;</code>
     * @return The deviceName.
     */
    java.lang.String getDeviceName();
    /**
     * <pre>
     * 设备自定义名称
     * </pre>
     *
     * <code>string device_name = 3;</code>
     * @return The bytes for deviceName.
     */
    com.google.protobuf.ByteString
        getDeviceNameBytes();

    /**
     * <pre>
     * 硬件特征哈希值
     * </pre>
     *
     * <code>string hardware_hash = 4;</code>
     * @return The hardwareHash.
     */
    java.lang.String getHardwareHash();
    /**
     * <pre>
     * 硬件特征哈希值
     * </pre>
     *
     * <code>string hardware_hash = 4;</code>
     * @return The bytes for hardwareHash.
     */
    com.google.protobuf.ByteString
        getHardwareHashBytes();

    /**
     * <pre>
     * 硬件信息
     * </pre>
     *
     * <code>string cpu_info = 5;</code>
     * @return The cpuInfo.
     */
    java.lang.String getCpuInfo();
    /**
     * <pre>
     * 硬件信息
     * </pre>
     *
     * <code>string cpu_info = 5;</code>
     * @return The bytes for cpuInfo.
     */
    com.google.protobuf.ByteString
        getCpuInfoBytes();

    /**
     * <pre>
     * 内存信息
     * </pre>
     *
     * <code>string memory_info = 6;</code>
     * @return The memoryInfo.
     */
    java.lang.String getMemoryInfo();
    /**
     * <pre>
     * 内存信息
     * </pre>
     *
     * <code>string memory_info = 6;</code>
     * @return The bytes for memoryInfo.
     */
    com.google.protobuf.ByteString
        getMemoryInfoBytes();

    /**
     * <pre>
     * 磁盘信息
     * </pre>
     *
     * <code>string disk_info = 7;</code>
     * @return The diskInfo.
     */
    java.lang.String getDiskInfo();
    /**
     * <pre>
     * 磁盘信息
     * </pre>
     *
     * <code>string disk_info = 7;</code>
     * @return The bytes for diskInfo.
     */
    com.google.protobuf.ByteString
        getDiskInfoBytes();

    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string network_info = 8;</code>
     * @return The networkInfo.
     */
    java.lang.String getNetworkInfo();
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string network_info = 8;</code>
     * @return The bytes for networkInfo.
     */
    com.google.protobuf.ByteString
        getNetworkInfoBytes();

    /**
     * <pre>
     * 显卡信息
     * </pre>
     *
     * <code>string gpu_info = 9;</code>
     * @return The gpuInfo.
     */
    java.lang.String getGpuInfo();
    /**
     * <pre>
     * 显卡信息
     * </pre>
     *
     * <code>string gpu_info = 9;</code>
     * @return The bytes for gpuInfo.
     */
    com.google.protobuf.ByteString
        getGpuInfoBytes();

    /**
     * <pre>
     * 系统环境信息
     * </pre>
     *
     * <code>string os_name = 10;</code>
     * @return The osName.
     */
    java.lang.String getOsName();
    /**
     * <pre>
     * 系统环境信息
     * </pre>
     *
     * <code>string os_name = 10;</code>
     * @return The bytes for osName.
     */
    com.google.protobuf.ByteString
        getOsNameBytes();

    /**
     * <pre>
     * 操作系统版本
     * </pre>
     *
     * <code>string os_version = 11;</code>
     * @return The osVersion.
     */
    java.lang.String getOsVersion();
    /**
     * <pre>
     * 操作系统版本
     * </pre>
     *
     * <code>string os_version = 11;</code>
     * @return The bytes for osVersion.
     */
    com.google.protobuf.ByteString
        getOsVersionBytes();

    /**
     * <pre>
     * 系统架构
     * </pre>
     *
     * <code>string os_arch = 12;</code>
     * @return The osArch.
     */
    java.lang.String getOsArch();
    /**
     * <pre>
     * 系统架构
     * </pre>
     *
     * <code>string os_arch = 12;</code>
     * @return The bytes for osArch.
     */
    com.google.protobuf.ByteString
        getOsArchBytes();

    /**
     * <pre>
     * 主机名
     * </pre>
     *
     * <code>string hostname = 13;</code>
     * @return The hostname.
     */
    java.lang.String getHostname();
    /**
     * <pre>
     * 主机名
     * </pre>
     *
     * <code>string hostname = 13;</code>
     * @return The bytes for hostname.
     */
    com.google.protobuf.ByteString
        getHostnameBytes();

    /**
     * <pre>
     * 当前用户名
     * </pre>
     *
     * <code>string username = 14;</code>
     * @return The username.
     */
    java.lang.String getUsername();
    /**
     * <pre>
     * 当前用户名
     * </pre>
     *
     * <code>string username = 14;</code>
     * @return The bytes for username.
     */
    com.google.protobuf.ByteString
        getUsernameBytes();

    /**
     * <pre>
     * 用户主目录
     * </pre>
     *
     * <code>string user_home_dir = 15;</code>
     * @return The userHomeDir.
     */
    java.lang.String getUserHomeDir();
    /**
     * <pre>
     * 用户主目录
     * </pre>
     *
     * <code>string user_home_dir = 15;</code>
     * @return The bytes for userHomeDir.
     */
    com.google.protobuf.ByteString
        getUserHomeDirBytes();

    /**
     * <pre>
     * 工作目录
     * </pre>
     *
     * <code>string work_dir = 16;</code>
     * @return The workDir.
     */
    java.lang.String getWorkDir();
    /**
     * <pre>
     * 工作目录
     * </pre>
     *
     * <code>string work_dir = 16;</code>
     * @return The bytes for workDir.
     */
    com.google.protobuf.ByteString
        getWorkDirBytes();

    /**
     * <pre>
     * 应用信息
     * </pre>
     *
     * <code>string app_version = 17;</code>
     * @return The appVersion.
     */
    java.lang.String getAppVersion();
    /**
     * <pre>
     * 应用信息
     * </pre>
     *
     * <code>string app_version = 17;</code>
     * @return The bytes for appVersion.
     */
    com.google.protobuf.ByteString
        getAppVersionBytes();

    /**
     * <pre>
     * 应用构建号
     * </pre>
     *
     * <code>string app_build_no = 18;</code>
     * @return The appBuildNo.
     */
    java.lang.String getAppBuildNo();
    /**
     * <pre>
     * 应用构建号
     * </pre>
     *
     * <code>string app_build_no = 18;</code>
     * @return The bytes for appBuildNo.
     */
    com.google.protobuf.ByteString
        getAppBuildNoBytes();

    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string ip_address = 19;</code>
     * @return The ipAddress.
     */
    java.lang.String getIpAddress();
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string ip_address = 19;</code>
     * @return The bytes for ipAddress.
     */
    com.google.protobuf.ByteString
        getIpAddressBytes();

    /**
     * <pre>
     * MAC地址
     * </pre>
     *
     * <code>string mac_address = 20;</code>
     * @return The macAddress.
     */
    java.lang.String getMacAddress();
    /**
     * <pre>
     * MAC地址
     * </pre>
     *
     * <code>string mac_address = 20;</code>
     * @return The bytes for macAddress.
     */
    com.google.protobuf.ByteString
        getMacAddressBytes();

    /**
     * <pre>
     * 用户关联
     * </pre>
     *
     * <code>uint32 user_id = 21;</code>
     * @return The userId.
     */
    int getUserId();

    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     * @return Whether the firstSeenAt field is set.
     */
    boolean hasFirstSeenAt();
    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     * @return The firstSeenAt.
     */
    common.Common.Timestamp getFirstSeenAt();
    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     */
    common.Common.TimestampOrBuilder getFirstSeenAtOrBuilder();

    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     * @return Whether the lastSeenAt field is set.
     */
    boolean hasLastSeenAt();
    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     * @return The lastSeenAt.
     */
    common.Common.Timestamp getLastSeenAt();
    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     */
    common.Common.TimestampOrBuilder getLastSeenAtOrBuilder();

    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     * @return Whether the lastReportAt field is set.
     */
    boolean hasLastReportAt();
    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     * @return The lastReportAt.
     */
    common.Common.Timestamp getLastReportAt();
    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     */
    common.Common.TimestampOrBuilder getLastReportAtOrBuilder();

    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     * @return Whether the createdAt field is set.
     */
    boolean hasCreatedAt();
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     * @return The createdAt.
     */
    common.Common.Timestamp getCreatedAt();
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     */
    common.Common.TimestampOrBuilder getCreatedAtOrBuilder();

    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     * @return Whether the updatedAt field is set.
     */
    boolean hasUpdatedAt();
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     * @return The updatedAt.
     */
    common.Common.Timestamp getUpdatedAt();
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     */
    common.Common.TimestampOrBuilder getUpdatedAtOrBuilder();

    /**
     * <pre>
     * 状态信息
     * </pre>
     *
     * <code>int32 status = 27;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <pre>
     * 是否活跃
     * </pre>
     *
     * <code>bool is_active = 28;</code>
     * @return The isActive.
     */
    boolean getIsActive();

    /**
     * <pre>
     * 上报次数
     * </pre>
     *
     * <code>int64 report_count = 29;</code>
     * @return The reportCount.
     */
    long getReportCount();

    /**
     * <pre>
     * 备注
     * </pre>
     *
     * <code>string remark = 30;</code>
     * @return The remark.
     */
    java.lang.String getRemark();
    /**
     * <pre>
     * 备注
     * </pre>
     *
     * <code>string remark = 30;</code>
     * @return The bytes for remark.
     */
    com.google.protobuf.ByteString
        getRemarkBytes();

    /**
     * <pre>
     * 是否默认
     * </pre>
     *
     * <code>bool is_default = 31;</code>
     * @return The isDefault.
     */
    boolean getIsDefault();
  }
  /**
   * <pre>
   * 设备信息
   * </pre>
   *
   * Protobuf type {@code device.Device}
   */
  public static final class Device extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:device.Device)
      DeviceOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Device.newBuilder() to construct.
    private Device(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Device() {
      deviceId_ = "";
      deviceName_ = "";
      hardwareHash_ = "";
      cpuInfo_ = "";
      memoryInfo_ = "";
      diskInfo_ = "";
      networkInfo_ = "";
      gpuInfo_ = "";
      osName_ = "";
      osVersion_ = "";
      osArch_ = "";
      hostname_ = "";
      username_ = "";
      userHomeDir_ = "";
      workDir_ = "";
      appVersion_ = "";
      appBuildNo_ = "";
      ipAddress_ = "";
      macAddress_ = "";
      remark_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Device();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return device.DeviceOuterClass.internal_static_device_Device_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return device.DeviceOuterClass.internal_static_device_Device_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              device.DeviceOuterClass.Device.class, device.DeviceOuterClass.Device.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deviceId_ = "";
    /**
     * <pre>
     * 设备唯一标识码
     * </pre>
     *
     * <code>string device_id = 2;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备唯一标识码
     * </pre>
     *
     * <code>string device_id = 2;</code>
     * @return The bytes for deviceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICE_NAME_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deviceName_ = "";
    /**
     * <pre>
     * 设备自定义名称
     * </pre>
     *
     * <code>string device_name = 3;</code>
     * @return The deviceName.
     */
    @java.lang.Override
    public java.lang.String getDeviceName() {
      java.lang.Object ref = deviceName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备自定义名称
     * </pre>
     *
     * <code>string device_name = 3;</code>
     * @return The bytes for deviceName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceNameBytes() {
      java.lang.Object ref = deviceName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HARDWARE_HASH_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hardwareHash_ = "";
    /**
     * <pre>
     * 硬件特征哈希值
     * </pre>
     *
     * <code>string hardware_hash = 4;</code>
     * @return The hardwareHash.
     */
    @java.lang.Override
    public java.lang.String getHardwareHash() {
      java.lang.Object ref = hardwareHash_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hardwareHash_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 硬件特征哈希值
     * </pre>
     *
     * <code>string hardware_hash = 4;</code>
     * @return The bytes for hardwareHash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHardwareHashBytes() {
      java.lang.Object ref = hardwareHash_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hardwareHash_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CPU_INFO_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cpuInfo_ = "";
    /**
     * <pre>
     * 硬件信息
     * </pre>
     *
     * <code>string cpu_info = 5;</code>
     * @return The cpuInfo.
     */
    @java.lang.Override
    public java.lang.String getCpuInfo() {
      java.lang.Object ref = cpuInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        cpuInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 硬件信息
     * </pre>
     *
     * <code>string cpu_info = 5;</code>
     * @return The bytes for cpuInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCpuInfoBytes() {
      java.lang.Object ref = cpuInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cpuInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MEMORY_INFO_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object memoryInfo_ = "";
    /**
     * <pre>
     * 内存信息
     * </pre>
     *
     * <code>string memory_info = 6;</code>
     * @return The memoryInfo.
     */
    @java.lang.Override
    public java.lang.String getMemoryInfo() {
      java.lang.Object ref = memoryInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        memoryInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 内存信息
     * </pre>
     *
     * <code>string memory_info = 6;</code>
     * @return The bytes for memoryInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMemoryInfoBytes() {
      java.lang.Object ref = memoryInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        memoryInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DISK_INFO_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object diskInfo_ = "";
    /**
     * <pre>
     * 磁盘信息
     * </pre>
     *
     * <code>string disk_info = 7;</code>
     * @return The diskInfo.
     */
    @java.lang.Override
    public java.lang.String getDiskInfo() {
      java.lang.Object ref = diskInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        diskInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 磁盘信息
     * </pre>
     *
     * <code>string disk_info = 7;</code>
     * @return The bytes for diskInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDiskInfoBytes() {
      java.lang.Object ref = diskInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        diskInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NETWORK_INFO_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object networkInfo_ = "";
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string network_info = 8;</code>
     * @return The networkInfo.
     */
    @java.lang.Override
    public java.lang.String getNetworkInfo() {
      java.lang.Object ref = networkInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        networkInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string network_info = 8;</code>
     * @return The bytes for networkInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNetworkInfoBytes() {
      java.lang.Object ref = networkInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        networkInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GPU_INFO_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object gpuInfo_ = "";
    /**
     * <pre>
     * 显卡信息
     * </pre>
     *
     * <code>string gpu_info = 9;</code>
     * @return The gpuInfo.
     */
    @java.lang.Override
    public java.lang.String getGpuInfo() {
      java.lang.Object ref = gpuInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gpuInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 显卡信息
     * </pre>
     *
     * <code>string gpu_info = 9;</code>
     * @return The bytes for gpuInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGpuInfoBytes() {
      java.lang.Object ref = gpuInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gpuInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OS_NAME_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object osName_ = "";
    /**
     * <pre>
     * 系统环境信息
     * </pre>
     *
     * <code>string os_name = 10;</code>
     * @return The osName.
     */
    @java.lang.Override
    public java.lang.String getOsName() {
      java.lang.Object ref = osName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 系统环境信息
     * </pre>
     *
     * <code>string os_name = 10;</code>
     * @return The bytes for osName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOsNameBytes() {
      java.lang.Object ref = osName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OS_VERSION_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object osVersion_ = "";
    /**
     * <pre>
     * 操作系统版本
     * </pre>
     *
     * <code>string os_version = 11;</code>
     * @return The osVersion.
     */
    @java.lang.Override
    public java.lang.String getOsVersion() {
      java.lang.Object ref = osVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 操作系统版本
     * </pre>
     *
     * <code>string os_version = 11;</code>
     * @return The bytes for osVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOsVersionBytes() {
      java.lang.Object ref = osVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OS_ARCH_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object osArch_ = "";
    /**
     * <pre>
     * 系统架构
     * </pre>
     *
     * <code>string os_arch = 12;</code>
     * @return The osArch.
     */
    @java.lang.Override
    public java.lang.String getOsArch() {
      java.lang.Object ref = osArch_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osArch_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 系统架构
     * </pre>
     *
     * <code>string os_arch = 12;</code>
     * @return The bytes for osArch.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOsArchBytes() {
      java.lang.Object ref = osArch_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osArch_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HOSTNAME_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object hostname_ = "";
    /**
     * <pre>
     * 主机名
     * </pre>
     *
     * <code>string hostname = 13;</code>
     * @return The hostname.
     */
    @java.lang.Override
    public java.lang.String getHostname() {
      java.lang.Object ref = hostname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        hostname_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 主机名
     * </pre>
     *
     * <code>string hostname = 13;</code>
     * @return The bytes for hostname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getHostnameBytes() {
      java.lang.Object ref = hostname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hostname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERNAME_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile java.lang.Object username_ = "";
    /**
     * <pre>
     * 当前用户名
     * </pre>
     *
     * <code>string username = 14;</code>
     * @return The username.
     */
    @java.lang.Override
    public java.lang.String getUsername() {
      java.lang.Object ref = username_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        username_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 当前用户名
     * </pre>
     *
     * <code>string username = 14;</code>
     * @return The bytes for username.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUsernameBytes() {
      java.lang.Object ref = username_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        username_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USER_HOME_DIR_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object userHomeDir_ = "";
    /**
     * <pre>
     * 用户主目录
     * </pre>
     *
     * <code>string user_home_dir = 15;</code>
     * @return The userHomeDir.
     */
    @java.lang.Override
    public java.lang.String getUserHomeDir() {
      java.lang.Object ref = userHomeDir_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userHomeDir_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 用户主目录
     * </pre>
     *
     * <code>string user_home_dir = 15;</code>
     * @return The bytes for userHomeDir.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserHomeDirBytes() {
      java.lang.Object ref = userHomeDir_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userHomeDir_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int WORK_DIR_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private volatile java.lang.Object workDir_ = "";
    /**
     * <pre>
     * 工作目录
     * </pre>
     *
     * <code>string work_dir = 16;</code>
     * @return The workDir.
     */
    @java.lang.Override
    public java.lang.String getWorkDir() {
      java.lang.Object ref = workDir_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        workDir_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 工作目录
     * </pre>
     *
     * <code>string work_dir = 16;</code>
     * @return The bytes for workDir.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getWorkDirBytes() {
      java.lang.Object ref = workDir_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        workDir_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_VERSION_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appVersion_ = "";
    /**
     * <pre>
     * 应用信息
     * </pre>
     *
     * <code>string app_version = 17;</code>
     * @return The appVersion.
     */
    @java.lang.Override
    public java.lang.String getAppVersion() {
      java.lang.Object ref = appVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 应用信息
     * </pre>
     *
     * <code>string app_version = 17;</code>
     * @return The bytes for appVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppVersionBytes() {
      java.lang.Object ref = appVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_BUILD_NO_FIELD_NUMBER = 18;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appBuildNo_ = "";
    /**
     * <pre>
     * 应用构建号
     * </pre>
     *
     * <code>string app_build_no = 18;</code>
     * @return The appBuildNo.
     */
    @java.lang.Override
    public java.lang.String getAppBuildNo() {
      java.lang.Object ref = appBuildNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appBuildNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 应用构建号
     * </pre>
     *
     * <code>string app_build_no = 18;</code>
     * @return The bytes for appBuildNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppBuildNoBytes() {
      java.lang.Object ref = appBuildNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appBuildNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ADDRESS_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ipAddress_ = "";
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string ip_address = 19;</code>
     * @return The ipAddress.
     */
    @java.lang.Override
    public java.lang.String getIpAddress() {
      java.lang.Object ref = ipAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ipAddress_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 网络信息
     * </pre>
     *
     * <code>string ip_address = 19;</code>
     * @return The bytes for ipAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIpAddressBytes() {
      java.lang.Object ref = ipAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MAC_ADDRESS_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object macAddress_ = "";
    /**
     * <pre>
     * MAC地址
     * </pre>
     *
     * <code>string mac_address = 20;</code>
     * @return The macAddress.
     */
    @java.lang.Override
    public java.lang.String getMacAddress() {
      java.lang.Object ref = macAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        macAddress_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * MAC地址
     * </pre>
     *
     * <code>string mac_address = 20;</code>
     * @return The bytes for macAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMacAddressBytes() {
      java.lang.Object ref = macAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        macAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USER_ID_FIELD_NUMBER = 21;
    private int userId_ = 0;
    /**
     * <pre>
     * 用户关联
     * </pre>
     *
     * <code>uint32 user_id = 21;</code>
     * @return The userId.
     */
    @java.lang.Override
    public int getUserId() {
      return userId_;
    }

    public static final int FIRST_SEEN_AT_FIELD_NUMBER = 22;
    private common.Common.Timestamp firstSeenAt_;
    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     * @return Whether the firstSeenAt field is set.
     */
    @java.lang.Override
    public boolean hasFirstSeenAt() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     * @return The firstSeenAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getFirstSeenAt() {
      return firstSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : firstSeenAt_;
    }
    /**
     * <pre>
     * 时间信息
     * </pre>
     *
     * <code>.common.Timestamp first_seen_at = 22;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getFirstSeenAtOrBuilder() {
      return firstSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : firstSeenAt_;
    }

    public static final int LAST_SEEN_AT_FIELD_NUMBER = 23;
    private common.Common.Timestamp lastSeenAt_;
    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     * @return Whether the lastSeenAt field is set.
     */
    @java.lang.Override
    public boolean hasLastSeenAt() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     * @return The lastSeenAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getLastSeenAt() {
      return lastSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastSeenAt_;
    }
    /**
     * <pre>
     * 最后见到时间
     * </pre>
     *
     * <code>.common.Timestamp last_seen_at = 23;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getLastSeenAtOrBuilder() {
      return lastSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastSeenAt_;
    }

    public static final int LAST_REPORT_AT_FIELD_NUMBER = 24;
    private common.Common.Timestamp lastReportAt_;
    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     * @return Whether the lastReportAt field is set.
     */
    @java.lang.Override
    public boolean hasLastReportAt() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     * @return The lastReportAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getLastReportAt() {
      return lastReportAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastReportAt_;
    }
    /**
     * <pre>
     * 最后上报时间
     * </pre>
     *
     * <code>.common.Timestamp last_report_at = 24;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getLastReportAtOrBuilder() {
      return lastReportAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastReportAt_;
    }

    public static final int CREATED_AT_FIELD_NUMBER = 25;
    private common.Common.Timestamp createdAt_;
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     * @return Whether the createdAt field is set.
     */
    @java.lang.Override
    public boolean hasCreatedAt() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     * @return The createdAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getCreatedAt() {
      return createdAt_ == null ? common.Common.Timestamp.getDefaultInstance() : createdAt_;
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>.common.Timestamp created_at = 25;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getCreatedAtOrBuilder() {
      return createdAt_ == null ? common.Common.Timestamp.getDefaultInstance() : createdAt_;
    }

    public static final int UPDATED_AT_FIELD_NUMBER = 26;
    private common.Common.Timestamp updatedAt_;
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     * @return Whether the updatedAt field is set.
     */
    @java.lang.Override
    public boolean hasUpdatedAt() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     * @return The updatedAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getUpdatedAt() {
      return updatedAt_ == null ? common.Common.Timestamp.getDefaultInstance() : updatedAt_;
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>.common.Timestamp updated_at = 26;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getUpdatedAtOrBuilder() {
      return updatedAt_ == null ? common.Common.Timestamp.getDefaultInstance() : updatedAt_;
    }

    public static final int STATUS_FIELD_NUMBER = 27;
    private int status_ = 0;
    /**
     * <pre>
     * 状态信息
     * </pre>
     *
     * <code>int32 status = 27;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int IS_ACTIVE_FIELD_NUMBER = 28;
    private boolean isActive_ = false;
    /**
     * <pre>
     * 是否活跃
     * </pre>
     *
     * <code>bool is_active = 28;</code>
     * @return The isActive.
     */
    @java.lang.Override
    public boolean getIsActive() {
      return isActive_;
    }

    public static final int REPORT_COUNT_FIELD_NUMBER = 29;
    private long reportCount_ = 0L;
    /**
     * <pre>
     * 上报次数
     * </pre>
     *
     * <code>int64 report_count = 29;</code>
     * @return The reportCount.
     */
    @java.lang.Override
    public long getReportCount() {
      return reportCount_;
    }

    public static final int REMARK_FIELD_NUMBER = 30;
    @SuppressWarnings("serial")
    private volatile java.lang.Object remark_ = "";
    /**
     * <pre>
     * 备注
     * </pre>
     *
     * <code>string remark = 30;</code>
     * @return The remark.
     */
    @java.lang.Override
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 备注
     * </pre>
     *
     * <code>string remark = 30;</code>
     * @return The bytes for remark.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IS_DEFAULT_FIELD_NUMBER = 31;
    private boolean isDefault_ = false;
    /**
     * <pre>
     * 是否默认
     * </pre>
     *
     * <code>bool is_default = 31;</code>
     * @return The isDefault.
     */
    @java.lang.Override
    public boolean getIsDefault() {
      return isDefault_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deviceId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, deviceName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hardwareHash_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, hardwareHash_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(cpuInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, cpuInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(memoryInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, memoryInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(diskInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, diskInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(networkInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, networkInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gpuInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, gpuInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, osName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, osVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osArch_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, osArch_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hostname_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, hostname_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(username_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, username_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userHomeDir_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, userHomeDir_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(workDir_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 16, workDir_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, appVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appBuildNo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 18, appBuildNo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ipAddress_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 19, ipAddress_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(macAddress_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, macAddress_);
      }
      if (userId_ != 0) {
        output.writeUInt32(21, userId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(22, getFirstSeenAt());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(23, getLastSeenAt());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(24, getLastReportAt());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(25, getCreatedAt());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(26, getUpdatedAt());
      }
      if (status_ != 0) {
        output.writeInt32(27, status_);
      }
      if (isActive_ != false) {
        output.writeBool(28, isActive_);
      }
      if (reportCount_ != 0L) {
        output.writeInt64(29, reportCount_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 30, remark_);
      }
      if (isDefault_ != false) {
        output.writeBool(31, isDefault_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, deviceId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, deviceName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hardwareHash_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, hardwareHash_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(cpuInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, cpuInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(memoryInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, memoryInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(diskInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, diskInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(networkInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, networkInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(gpuInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, gpuInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, osName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, osVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osArch_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, osArch_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(hostname_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, hostname_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(username_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, username_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userHomeDir_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, userHomeDir_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(workDir_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, workDir_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, appVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appBuildNo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, appBuildNo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ipAddress_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, ipAddress_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(macAddress_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, macAddress_);
      }
      if (userId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(21, userId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(22, getFirstSeenAt());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, getLastSeenAt());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(24, getLastReportAt());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(25, getCreatedAt());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(26, getUpdatedAt());
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(27, status_);
      }
      if (isActive_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(28, isActive_);
      }
      if (reportCount_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(29, reportCount_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(remark_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(30, remark_);
      }
      if (isDefault_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(31, isDefault_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof device.DeviceOuterClass.Device)) {
        return super.equals(obj);
      }
      device.DeviceOuterClass.Device other = (device.DeviceOuterClass.Device) obj;

      if (getId()
          != other.getId()) return false;
      if (!getDeviceId()
          .equals(other.getDeviceId())) return false;
      if (!getDeviceName()
          .equals(other.getDeviceName())) return false;
      if (!getHardwareHash()
          .equals(other.getHardwareHash())) return false;
      if (!getCpuInfo()
          .equals(other.getCpuInfo())) return false;
      if (!getMemoryInfo()
          .equals(other.getMemoryInfo())) return false;
      if (!getDiskInfo()
          .equals(other.getDiskInfo())) return false;
      if (!getNetworkInfo()
          .equals(other.getNetworkInfo())) return false;
      if (!getGpuInfo()
          .equals(other.getGpuInfo())) return false;
      if (!getOsName()
          .equals(other.getOsName())) return false;
      if (!getOsVersion()
          .equals(other.getOsVersion())) return false;
      if (!getOsArch()
          .equals(other.getOsArch())) return false;
      if (!getHostname()
          .equals(other.getHostname())) return false;
      if (!getUsername()
          .equals(other.getUsername())) return false;
      if (!getUserHomeDir()
          .equals(other.getUserHomeDir())) return false;
      if (!getWorkDir()
          .equals(other.getWorkDir())) return false;
      if (!getAppVersion()
          .equals(other.getAppVersion())) return false;
      if (!getAppBuildNo()
          .equals(other.getAppBuildNo())) return false;
      if (!getIpAddress()
          .equals(other.getIpAddress())) return false;
      if (!getMacAddress()
          .equals(other.getMacAddress())) return false;
      if (getUserId()
          != other.getUserId()) return false;
      if (hasFirstSeenAt() != other.hasFirstSeenAt()) return false;
      if (hasFirstSeenAt()) {
        if (!getFirstSeenAt()
            .equals(other.getFirstSeenAt())) return false;
      }
      if (hasLastSeenAt() != other.hasLastSeenAt()) return false;
      if (hasLastSeenAt()) {
        if (!getLastSeenAt()
            .equals(other.getLastSeenAt())) return false;
      }
      if (hasLastReportAt() != other.hasLastReportAt()) return false;
      if (hasLastReportAt()) {
        if (!getLastReportAt()
            .equals(other.getLastReportAt())) return false;
      }
      if (hasCreatedAt() != other.hasCreatedAt()) return false;
      if (hasCreatedAt()) {
        if (!getCreatedAt()
            .equals(other.getCreatedAt())) return false;
      }
      if (hasUpdatedAt() != other.hasUpdatedAt()) return false;
      if (hasUpdatedAt()) {
        if (!getUpdatedAt()
            .equals(other.getUpdatedAt())) return false;
      }
      if (getStatus()
          != other.getStatus()) return false;
      if (getIsActive()
          != other.getIsActive()) return false;
      if (getReportCount()
          != other.getReportCount()) return false;
      if (!getRemark()
          .equals(other.getRemark())) return false;
      if (getIsDefault()
          != other.getIsDefault()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId().hashCode();
      hash = (37 * hash) + DEVICE_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceName().hashCode();
      hash = (37 * hash) + HARDWARE_HASH_FIELD_NUMBER;
      hash = (53 * hash) + getHardwareHash().hashCode();
      hash = (37 * hash) + CPU_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getCpuInfo().hashCode();
      hash = (37 * hash) + MEMORY_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getMemoryInfo().hashCode();
      hash = (37 * hash) + DISK_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getDiskInfo().hashCode();
      hash = (37 * hash) + NETWORK_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getNetworkInfo().hashCode();
      hash = (37 * hash) + GPU_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getGpuInfo().hashCode();
      hash = (37 * hash) + OS_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getOsName().hashCode();
      hash = (37 * hash) + OS_VERSION_FIELD_NUMBER;
      hash = (53 * hash) + getOsVersion().hashCode();
      hash = (37 * hash) + OS_ARCH_FIELD_NUMBER;
      hash = (53 * hash) + getOsArch().hashCode();
      hash = (37 * hash) + HOSTNAME_FIELD_NUMBER;
      hash = (53 * hash) + getHostname().hashCode();
      hash = (37 * hash) + USERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUsername().hashCode();
      hash = (37 * hash) + USER_HOME_DIR_FIELD_NUMBER;
      hash = (53 * hash) + getUserHomeDir().hashCode();
      hash = (37 * hash) + WORK_DIR_FIELD_NUMBER;
      hash = (53 * hash) + getWorkDir().hashCode();
      hash = (37 * hash) + APP_VERSION_FIELD_NUMBER;
      hash = (53 * hash) + getAppVersion().hashCode();
      hash = (37 * hash) + APP_BUILD_NO_FIELD_NUMBER;
      hash = (53 * hash) + getAppBuildNo().hashCode();
      hash = (37 * hash) + IP_ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getIpAddress().hashCode();
      hash = (37 * hash) + MAC_ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getMacAddress().hashCode();
      hash = (37 * hash) + USER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getUserId();
      if (hasFirstSeenAt()) {
        hash = (37 * hash) + FIRST_SEEN_AT_FIELD_NUMBER;
        hash = (53 * hash) + getFirstSeenAt().hashCode();
      }
      if (hasLastSeenAt()) {
        hash = (37 * hash) + LAST_SEEN_AT_FIELD_NUMBER;
        hash = (53 * hash) + getLastSeenAt().hashCode();
      }
      if (hasLastReportAt()) {
        hash = (37 * hash) + LAST_REPORT_AT_FIELD_NUMBER;
        hash = (53 * hash) + getLastReportAt().hashCode();
      }
      if (hasCreatedAt()) {
        hash = (37 * hash) + CREATED_AT_FIELD_NUMBER;
        hash = (53 * hash) + getCreatedAt().hashCode();
      }
      if (hasUpdatedAt()) {
        hash = (37 * hash) + UPDATED_AT_FIELD_NUMBER;
        hash = (53 * hash) + getUpdatedAt().hashCode();
      }
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + IS_ACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsActive());
      hash = (37 * hash) + REPORT_COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReportCount());
      hash = (37 * hash) + REMARK_FIELD_NUMBER;
      hash = (53 * hash) + getRemark().hashCode();
      hash = (37 * hash) + IS_DEFAULT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsDefault());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static device.DeviceOuterClass.Device parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.Device parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.Device parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static device.DeviceOuterClass.Device parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static device.DeviceOuterClass.Device parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.Device parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(device.DeviceOuterClass.Device prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 设备信息
     * </pre>
     *
     * Protobuf type {@code device.Device}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:device.Device)
        device.DeviceOuterClass.DeviceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return device.DeviceOuterClass.internal_static_device_Device_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return device.DeviceOuterClass.internal_static_device_Device_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                device.DeviceOuterClass.Device.class, device.DeviceOuterClass.Device.Builder.class);
      }

      // Construct using device.DeviceOuterClass.Device.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFirstSeenAtFieldBuilder();
          getLastSeenAtFieldBuilder();
          getLastReportAtFieldBuilder();
          getCreatedAtFieldBuilder();
          getUpdatedAtFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        deviceId_ = "";
        deviceName_ = "";
        hardwareHash_ = "";
        cpuInfo_ = "";
        memoryInfo_ = "";
        diskInfo_ = "";
        networkInfo_ = "";
        gpuInfo_ = "";
        osName_ = "";
        osVersion_ = "";
        osArch_ = "";
        hostname_ = "";
        username_ = "";
        userHomeDir_ = "";
        workDir_ = "";
        appVersion_ = "";
        appBuildNo_ = "";
        ipAddress_ = "";
        macAddress_ = "";
        userId_ = 0;
        firstSeenAt_ = null;
        if (firstSeenAtBuilder_ != null) {
          firstSeenAtBuilder_.dispose();
          firstSeenAtBuilder_ = null;
        }
        lastSeenAt_ = null;
        if (lastSeenAtBuilder_ != null) {
          lastSeenAtBuilder_.dispose();
          lastSeenAtBuilder_ = null;
        }
        lastReportAt_ = null;
        if (lastReportAtBuilder_ != null) {
          lastReportAtBuilder_.dispose();
          lastReportAtBuilder_ = null;
        }
        createdAt_ = null;
        if (createdAtBuilder_ != null) {
          createdAtBuilder_.dispose();
          createdAtBuilder_ = null;
        }
        updatedAt_ = null;
        if (updatedAtBuilder_ != null) {
          updatedAtBuilder_.dispose();
          updatedAtBuilder_ = null;
        }
        status_ = 0;
        isActive_ = false;
        reportCount_ = 0L;
        remark_ = "";
        isDefault_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return device.DeviceOuterClass.internal_static_device_Device_descriptor;
      }

      @java.lang.Override
      public device.DeviceOuterClass.Device getDefaultInstanceForType() {
        return device.DeviceOuterClass.Device.getDefaultInstance();
      }

      @java.lang.Override
      public device.DeviceOuterClass.Device build() {
        device.DeviceOuterClass.Device result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public device.DeviceOuterClass.Device buildPartial() {
        device.DeviceOuterClass.Device result = new device.DeviceOuterClass.Device(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(device.DeviceOuterClass.Device result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.deviceId_ = deviceId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.deviceName_ = deviceName_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.hardwareHash_ = hardwareHash_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.cpuInfo_ = cpuInfo_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.memoryInfo_ = memoryInfo_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.diskInfo_ = diskInfo_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.networkInfo_ = networkInfo_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.gpuInfo_ = gpuInfo_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.osName_ = osName_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.osVersion_ = osVersion_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.osArch_ = osArch_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.hostname_ = hostname_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.username_ = username_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.userHomeDir_ = userHomeDir_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.workDir_ = workDir_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.appVersion_ = appVersion_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.appBuildNo_ = appBuildNo_;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.ipAddress_ = ipAddress_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.macAddress_ = macAddress_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.userId_ = userId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.firstSeenAt_ = firstSeenAtBuilder_ == null
              ? firstSeenAt_
              : firstSeenAtBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.lastSeenAt_ = lastSeenAtBuilder_ == null
              ? lastSeenAt_
              : lastSeenAtBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.lastReportAt_ = lastReportAtBuilder_ == null
              ? lastReportAt_
              : lastReportAtBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.createdAt_ = createdAtBuilder_ == null
              ? createdAt_
              : createdAtBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.updatedAt_ = updatedAtBuilder_ == null
              ? updatedAt_
              : updatedAtBuilder_.build();
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.status_ = status_;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.isActive_ = isActive_;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.reportCount_ = reportCount_;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.remark_ = remark_;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.isDefault_ = isDefault_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof device.DeviceOuterClass.Device) {
          return mergeFrom((device.DeviceOuterClass.Device)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(device.DeviceOuterClass.Device other) {
        if (other == device.DeviceOuterClass.Device.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (!other.getDeviceId().isEmpty()) {
          deviceId_ = other.deviceId_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getDeviceName().isEmpty()) {
          deviceName_ = other.deviceName_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getHardwareHash().isEmpty()) {
          hardwareHash_ = other.hardwareHash_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getCpuInfo().isEmpty()) {
          cpuInfo_ = other.cpuInfo_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getMemoryInfo().isEmpty()) {
          memoryInfo_ = other.memoryInfo_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getDiskInfo().isEmpty()) {
          diskInfo_ = other.diskInfo_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (!other.getNetworkInfo().isEmpty()) {
          networkInfo_ = other.networkInfo_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (!other.getGpuInfo().isEmpty()) {
          gpuInfo_ = other.gpuInfo_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (!other.getOsName().isEmpty()) {
          osName_ = other.osName_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (!other.getOsVersion().isEmpty()) {
          osVersion_ = other.osVersion_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (!other.getOsArch().isEmpty()) {
          osArch_ = other.osArch_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (!other.getHostname().isEmpty()) {
          hostname_ = other.hostname_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (!other.getUsername().isEmpty()) {
          username_ = other.username_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        if (!other.getUserHomeDir().isEmpty()) {
          userHomeDir_ = other.userHomeDir_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        if (!other.getWorkDir().isEmpty()) {
          workDir_ = other.workDir_;
          bitField0_ |= 0x00008000;
          onChanged();
        }
        if (!other.getAppVersion().isEmpty()) {
          appVersion_ = other.appVersion_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (!other.getAppBuildNo().isEmpty()) {
          appBuildNo_ = other.appBuildNo_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (!other.getIpAddress().isEmpty()) {
          ipAddress_ = other.ipAddress_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (!other.getMacAddress().isEmpty()) {
          macAddress_ = other.macAddress_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (other.getUserId() != 0) {
          setUserId(other.getUserId());
        }
        if (other.hasFirstSeenAt()) {
          mergeFirstSeenAt(other.getFirstSeenAt());
        }
        if (other.hasLastSeenAt()) {
          mergeLastSeenAt(other.getLastSeenAt());
        }
        if (other.hasLastReportAt()) {
          mergeLastReportAt(other.getLastReportAt());
        }
        if (other.hasCreatedAt()) {
          mergeCreatedAt(other.getCreatedAt());
        }
        if (other.hasUpdatedAt()) {
          mergeUpdatedAt(other.getUpdatedAt());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getIsActive() != false) {
          setIsActive(other.getIsActive());
        }
        if (other.getReportCount() != 0L) {
          setReportCount(other.getReportCount());
        }
        if (!other.getRemark().isEmpty()) {
          remark_ = other.remark_;
          bitField0_ |= 0x20000000;
          onChanged();
        }
        if (other.getIsDefault() != false) {
          setIsDefault(other.getIsDefault());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                deviceId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                deviceName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                hardwareHash_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                cpuInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                memoryInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                diskInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                networkInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                gpuInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                osName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                osVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                osArch_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                hostname_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                username_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                userHomeDir_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                workDir_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                appVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                appBuildNo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                ipAddress_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                macAddress_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 168: {
                userId_ = input.readUInt32();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 178: {
                input.readMessage(
                    getFirstSeenAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                input.readMessage(
                    getLastSeenAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                input.readMessage(
                    getLastReportAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                input.readMessage(
                    getCreatedAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                input.readMessage(
                    getUpdatedAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 216: {
                status_ = input.readInt32();
                bitField0_ |= 0x04000000;
                break;
              } // case 216
              case 224: {
                isActive_ = input.readBool();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 232: {
                reportCount_ = input.readInt64();
                bitField0_ |= 0x10000000;
                break;
              } // case 232
              case 242: {
                remark_ = input.readStringRequireUtf8();
                bitField0_ |= 0x20000000;
                break;
              } // case 242
              case 248: {
                isDefault_ = input.readBool();
                bitField0_ |= 0x40000000;
                break;
              } // case 248
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object deviceId_ = "";
      /**
       * <pre>
       * 设备唯一标识码
       * </pre>
       *
       * <code>string device_id = 2;</code>
       * @return The deviceId.
       */
      public java.lang.String getDeviceId() {
        java.lang.Object ref = deviceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备唯一标识码
       * </pre>
       *
       * <code>string device_id = 2;</code>
       * @return The bytes for deviceId.
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        java.lang.Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备唯一标识码
       * </pre>
       *
       * <code>string device_id = 2;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deviceId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备唯一标识码
       * </pre>
       *
       * <code>string device_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        deviceId_ = getDefaultInstance().getDeviceId();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备唯一标识码
       * </pre>
       *
       * <code>string device_id = 2;</code>
       * @param value The bytes for deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deviceId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object deviceName_ = "";
      /**
       * <pre>
       * 设备自定义名称
       * </pre>
       *
       * <code>string device_name = 3;</code>
       * @return The deviceName.
       */
      public java.lang.String getDeviceName() {
        java.lang.Object ref = deviceName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备自定义名称
       * </pre>
       *
       * <code>string device_name = 3;</code>
       * @return The bytes for deviceName.
       */
      public com.google.protobuf.ByteString
          getDeviceNameBytes() {
        java.lang.Object ref = deviceName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备自定义名称
       * </pre>
       *
       * <code>string device_name = 3;</code>
       * @param value The deviceName to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deviceName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备自定义名称
       * </pre>
       *
       * <code>string device_name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceName() {
        deviceName_ = getDefaultInstance().getDeviceName();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备自定义名称
       * </pre>
       *
       * <code>string device_name = 3;</code>
       * @param value The bytes for deviceName to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deviceName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object hardwareHash_ = "";
      /**
       * <pre>
       * 硬件特征哈希值
       * </pre>
       *
       * <code>string hardware_hash = 4;</code>
       * @return The hardwareHash.
       */
      public java.lang.String getHardwareHash() {
        java.lang.Object ref = hardwareHash_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hardwareHash_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 硬件特征哈希值
       * </pre>
       *
       * <code>string hardware_hash = 4;</code>
       * @return The bytes for hardwareHash.
       */
      public com.google.protobuf.ByteString
          getHardwareHashBytes() {
        java.lang.Object ref = hardwareHash_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hardwareHash_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 硬件特征哈希值
       * </pre>
       *
       * <code>string hardware_hash = 4;</code>
       * @param value The hardwareHash to set.
       * @return This builder for chaining.
       */
      public Builder setHardwareHash(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hardwareHash_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 硬件特征哈希值
       * </pre>
       *
       * <code>string hardware_hash = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHardwareHash() {
        hardwareHash_ = getDefaultInstance().getHardwareHash();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 硬件特征哈希值
       * </pre>
       *
       * <code>string hardware_hash = 4;</code>
       * @param value The bytes for hardwareHash to set.
       * @return This builder for chaining.
       */
      public Builder setHardwareHashBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hardwareHash_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object cpuInfo_ = "";
      /**
       * <pre>
       * 硬件信息
       * </pre>
       *
       * <code>string cpu_info = 5;</code>
       * @return The cpuInfo.
       */
      public java.lang.String getCpuInfo() {
        java.lang.Object ref = cpuInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          cpuInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 硬件信息
       * </pre>
       *
       * <code>string cpu_info = 5;</code>
       * @return The bytes for cpuInfo.
       */
      public com.google.protobuf.ByteString
          getCpuInfoBytes() {
        java.lang.Object ref = cpuInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cpuInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 硬件信息
       * </pre>
       *
       * <code>string cpu_info = 5;</code>
       * @param value The cpuInfo to set.
       * @return This builder for chaining.
       */
      public Builder setCpuInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cpuInfo_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 硬件信息
       * </pre>
       *
       * <code>string cpu_info = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCpuInfo() {
        cpuInfo_ = getDefaultInstance().getCpuInfo();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 硬件信息
       * </pre>
       *
       * <code>string cpu_info = 5;</code>
       * @param value The bytes for cpuInfo to set.
       * @return This builder for chaining.
       */
      public Builder setCpuInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        cpuInfo_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object memoryInfo_ = "";
      /**
       * <pre>
       * 内存信息
       * </pre>
       *
       * <code>string memory_info = 6;</code>
       * @return The memoryInfo.
       */
      public java.lang.String getMemoryInfo() {
        java.lang.Object ref = memoryInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          memoryInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 内存信息
       * </pre>
       *
       * <code>string memory_info = 6;</code>
       * @return The bytes for memoryInfo.
       */
      public com.google.protobuf.ByteString
          getMemoryInfoBytes() {
        java.lang.Object ref = memoryInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          memoryInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 内存信息
       * </pre>
       *
       * <code>string memory_info = 6;</code>
       * @param value The memoryInfo to set.
       * @return This builder for chaining.
       */
      public Builder setMemoryInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        memoryInfo_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 内存信息
       * </pre>
       *
       * <code>string memory_info = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemoryInfo() {
        memoryInfo_ = getDefaultInstance().getMemoryInfo();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 内存信息
       * </pre>
       *
       * <code>string memory_info = 6;</code>
       * @param value The bytes for memoryInfo to set.
       * @return This builder for chaining.
       */
      public Builder setMemoryInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        memoryInfo_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object diskInfo_ = "";
      /**
       * <pre>
       * 磁盘信息
       * </pre>
       *
       * <code>string disk_info = 7;</code>
       * @return The diskInfo.
       */
      public java.lang.String getDiskInfo() {
        java.lang.Object ref = diskInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          diskInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 磁盘信息
       * </pre>
       *
       * <code>string disk_info = 7;</code>
       * @return The bytes for diskInfo.
       */
      public com.google.protobuf.ByteString
          getDiskInfoBytes() {
        java.lang.Object ref = diskInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          diskInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 磁盘信息
       * </pre>
       *
       * <code>string disk_info = 7;</code>
       * @param value The diskInfo to set.
       * @return This builder for chaining.
       */
      public Builder setDiskInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        diskInfo_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 磁盘信息
       * </pre>
       *
       * <code>string disk_info = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiskInfo() {
        diskInfo_ = getDefaultInstance().getDiskInfo();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 磁盘信息
       * </pre>
       *
       * <code>string disk_info = 7;</code>
       * @param value The bytes for diskInfo to set.
       * @return This builder for chaining.
       */
      public Builder setDiskInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        diskInfo_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object networkInfo_ = "";
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string network_info = 8;</code>
       * @return The networkInfo.
       */
      public java.lang.String getNetworkInfo() {
        java.lang.Object ref = networkInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          networkInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string network_info = 8;</code>
       * @return The bytes for networkInfo.
       */
      public com.google.protobuf.ByteString
          getNetworkInfoBytes() {
        java.lang.Object ref = networkInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          networkInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string network_info = 8;</code>
       * @param value The networkInfo to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        networkInfo_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string network_info = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearNetworkInfo() {
        networkInfo_ = getDefaultInstance().getNetworkInfo();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string network_info = 8;</code>
       * @param value The bytes for networkInfo to set.
       * @return This builder for chaining.
       */
      public Builder setNetworkInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        networkInfo_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object gpuInfo_ = "";
      /**
       * <pre>
       * 显卡信息
       * </pre>
       *
       * <code>string gpu_info = 9;</code>
       * @return The gpuInfo.
       */
      public java.lang.String getGpuInfo() {
        java.lang.Object ref = gpuInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          gpuInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显卡信息
       * </pre>
       *
       * <code>string gpu_info = 9;</code>
       * @return The bytes for gpuInfo.
       */
      public com.google.protobuf.ByteString
          getGpuInfoBytes() {
        java.lang.Object ref = gpuInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gpuInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显卡信息
       * </pre>
       *
       * <code>string gpu_info = 9;</code>
       * @param value The gpuInfo to set.
       * @return This builder for chaining.
       */
      public Builder setGpuInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        gpuInfo_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显卡信息
       * </pre>
       *
       * <code>string gpu_info = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearGpuInfo() {
        gpuInfo_ = getDefaultInstance().getGpuInfo();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显卡信息
       * </pre>
       *
       * <code>string gpu_info = 9;</code>
       * @param value The bytes for gpuInfo to set.
       * @return This builder for chaining.
       */
      public Builder setGpuInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        gpuInfo_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object osName_ = "";
      /**
       * <pre>
       * 系统环境信息
       * </pre>
       *
       * <code>string os_name = 10;</code>
       * @return The osName.
       */
      public java.lang.String getOsName() {
        java.lang.Object ref = osName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          osName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 系统环境信息
       * </pre>
       *
       * <code>string os_name = 10;</code>
       * @return The bytes for osName.
       */
      public com.google.protobuf.ByteString
          getOsNameBytes() {
        java.lang.Object ref = osName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          osName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 系统环境信息
       * </pre>
       *
       * <code>string os_name = 10;</code>
       * @param value The osName to set.
       * @return This builder for chaining.
       */
      public Builder setOsName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        osName_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统环境信息
       * </pre>
       *
       * <code>string os_name = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsName() {
        osName_ = getDefaultInstance().getOsName();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统环境信息
       * </pre>
       *
       * <code>string os_name = 10;</code>
       * @param value The bytes for osName to set.
       * @return This builder for chaining.
       */
      public Builder setOsNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        osName_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object osVersion_ = "";
      /**
       * <pre>
       * 操作系统版本
       * </pre>
       *
       * <code>string os_version = 11;</code>
       * @return The osVersion.
       */
      public java.lang.String getOsVersion() {
        java.lang.Object ref = osVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          osVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 操作系统版本
       * </pre>
       *
       * <code>string os_version = 11;</code>
       * @return The bytes for osVersion.
       */
      public com.google.protobuf.ByteString
          getOsVersionBytes() {
        java.lang.Object ref = osVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          osVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 操作系统版本
       * </pre>
       *
       * <code>string os_version = 11;</code>
       * @param value The osVersion to set.
       * @return This builder for chaining.
       */
      public Builder setOsVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        osVersion_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作系统版本
       * </pre>
       *
       * <code>string os_version = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsVersion() {
        osVersion_ = getDefaultInstance().getOsVersion();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作系统版本
       * </pre>
       *
       * <code>string os_version = 11;</code>
       * @param value The bytes for osVersion to set.
       * @return This builder for chaining.
       */
      public Builder setOsVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        osVersion_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object osArch_ = "";
      /**
       * <pre>
       * 系统架构
       * </pre>
       *
       * <code>string os_arch = 12;</code>
       * @return The osArch.
       */
      public java.lang.String getOsArch() {
        java.lang.Object ref = osArch_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          osArch_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 系统架构
       * </pre>
       *
       * <code>string os_arch = 12;</code>
       * @return The bytes for osArch.
       */
      public com.google.protobuf.ByteString
          getOsArchBytes() {
        java.lang.Object ref = osArch_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          osArch_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 系统架构
       * </pre>
       *
       * <code>string os_arch = 12;</code>
       * @param value The osArch to set.
       * @return This builder for chaining.
       */
      public Builder setOsArch(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        osArch_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统架构
       * </pre>
       *
       * <code>string os_arch = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsArch() {
        osArch_ = getDefaultInstance().getOsArch();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统架构
       * </pre>
       *
       * <code>string os_arch = 12;</code>
       * @param value The bytes for osArch to set.
       * @return This builder for chaining.
       */
      public Builder setOsArchBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        osArch_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object hostname_ = "";
      /**
       * <pre>
       * 主机名
       * </pre>
       *
       * <code>string hostname = 13;</code>
       * @return The hostname.
       */
      public java.lang.String getHostname() {
        java.lang.Object ref = hostname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          hostname_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 主机名
       * </pre>
       *
       * <code>string hostname = 13;</code>
       * @return The bytes for hostname.
       */
      public com.google.protobuf.ByteString
          getHostnameBytes() {
        java.lang.Object ref = hostname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          hostname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 主机名
       * </pre>
       *
       * <code>string hostname = 13;</code>
       * @param value The hostname to set.
       * @return This builder for chaining.
       */
      public Builder setHostname(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        hostname_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主机名
       * </pre>
       *
       * <code>string hostname = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearHostname() {
        hostname_ = getDefaultInstance().getHostname();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主机名
       * </pre>
       *
       * <code>string hostname = 13;</code>
       * @param value The bytes for hostname to set.
       * @return This builder for chaining.
       */
      public Builder setHostnameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        hostname_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.lang.Object username_ = "";
      /**
       * <pre>
       * 当前用户名
       * </pre>
       *
       * <code>string username = 14;</code>
       * @return The username.
       */
      public java.lang.String getUsername() {
        java.lang.Object ref = username_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          username_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 当前用户名
       * </pre>
       *
       * <code>string username = 14;</code>
       * @return The bytes for username.
       */
      public com.google.protobuf.ByteString
          getUsernameBytes() {
        java.lang.Object ref = username_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          username_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 当前用户名
       * </pre>
       *
       * <code>string username = 14;</code>
       * @param value The username to set.
       * @return This builder for chaining.
       */
      public Builder setUsername(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        username_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前用户名
       * </pre>
       *
       * <code>string username = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsername() {
        username_ = getDefaultInstance().getUsername();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前用户名
       * </pre>
       *
       * <code>string username = 14;</code>
       * @param value The bytes for username to set.
       * @return This builder for chaining.
       */
      public Builder setUsernameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        username_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      private java.lang.Object userHomeDir_ = "";
      /**
       * <pre>
       * 用户主目录
       * </pre>
       *
       * <code>string user_home_dir = 15;</code>
       * @return The userHomeDir.
       */
      public java.lang.String getUserHomeDir() {
        java.lang.Object ref = userHomeDir_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userHomeDir_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 用户主目录
       * </pre>
       *
       * <code>string user_home_dir = 15;</code>
       * @return The bytes for userHomeDir.
       */
      public com.google.protobuf.ByteString
          getUserHomeDirBytes() {
        java.lang.Object ref = userHomeDir_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userHomeDir_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 用户主目录
       * </pre>
       *
       * <code>string user_home_dir = 15;</code>
       * @param value The userHomeDir to set.
       * @return This builder for chaining.
       */
      public Builder setUserHomeDir(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        userHomeDir_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户主目录
       * </pre>
       *
       * <code>string user_home_dir = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserHomeDir() {
        userHomeDir_ = getDefaultInstance().getUserHomeDir();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户主目录
       * </pre>
       *
       * <code>string user_home_dir = 15;</code>
       * @param value The bytes for userHomeDir to set.
       * @return This builder for chaining.
       */
      public Builder setUserHomeDirBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        userHomeDir_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private java.lang.Object workDir_ = "";
      /**
       * <pre>
       * 工作目录
       * </pre>
       *
       * <code>string work_dir = 16;</code>
       * @return The workDir.
       */
      public java.lang.String getWorkDir() {
        java.lang.Object ref = workDir_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          workDir_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 工作目录
       * </pre>
       *
       * <code>string work_dir = 16;</code>
       * @return The bytes for workDir.
       */
      public com.google.protobuf.ByteString
          getWorkDirBytes() {
        java.lang.Object ref = workDir_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          workDir_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 工作目录
       * </pre>
       *
       * <code>string work_dir = 16;</code>
       * @param value The workDir to set.
       * @return This builder for chaining.
       */
      public Builder setWorkDir(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        workDir_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 工作目录
       * </pre>
       *
       * <code>string work_dir = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearWorkDir() {
        workDir_ = getDefaultInstance().getWorkDir();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 工作目录
       * </pre>
       *
       * <code>string work_dir = 16;</code>
       * @param value The bytes for workDir to set.
       * @return This builder for chaining.
       */
      public Builder setWorkDirBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        workDir_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }

      private java.lang.Object appVersion_ = "";
      /**
       * <pre>
       * 应用信息
       * </pre>
       *
       * <code>string app_version = 17;</code>
       * @return The appVersion.
       */
      public java.lang.String getAppVersion() {
        java.lang.Object ref = appVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用信息
       * </pre>
       *
       * <code>string app_version = 17;</code>
       * @return The bytes for appVersion.
       */
      public com.google.protobuf.ByteString
          getAppVersionBytes() {
        java.lang.Object ref = appVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用信息
       * </pre>
       *
       * <code>string app_version = 17;</code>
       * @param value The appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appVersion_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用信息
       * </pre>
       *
       * <code>string app_version = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppVersion() {
        appVersion_ = getDefaultInstance().getAppVersion();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用信息
       * </pre>
       *
       * <code>string app_version = 17;</code>
       * @param value The bytes for appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appVersion_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private java.lang.Object appBuildNo_ = "";
      /**
       * <pre>
       * 应用构建号
       * </pre>
       *
       * <code>string app_build_no = 18;</code>
       * @return The appBuildNo.
       */
      public java.lang.String getAppBuildNo() {
        java.lang.Object ref = appBuildNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appBuildNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用构建号
       * </pre>
       *
       * <code>string app_build_no = 18;</code>
       * @return The bytes for appBuildNo.
       */
      public com.google.protobuf.ByteString
          getAppBuildNoBytes() {
        java.lang.Object ref = appBuildNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appBuildNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用构建号
       * </pre>
       *
       * <code>string app_build_no = 18;</code>
       * @param value The appBuildNo to set.
       * @return This builder for chaining.
       */
      public Builder setAppBuildNo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appBuildNo_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用构建号
       * </pre>
       *
       * <code>string app_build_no = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppBuildNo() {
        appBuildNo_ = getDefaultInstance().getAppBuildNo();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用构建号
       * </pre>
       *
       * <code>string app_build_no = 18;</code>
       * @param value The bytes for appBuildNo to set.
       * @return This builder for chaining.
       */
      public Builder setAppBuildNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appBuildNo_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object ipAddress_ = "";
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string ip_address = 19;</code>
       * @return The ipAddress.
       */
      public java.lang.String getIpAddress() {
        java.lang.Object ref = ipAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          ipAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string ip_address = 19;</code>
       * @return The bytes for ipAddress.
       */
      public com.google.protobuf.ByteString
          getIpAddressBytes() {
        java.lang.Object ref = ipAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string ip_address = 19;</code>
       * @param value The ipAddress to set.
       * @return This builder for chaining.
       */
      public Builder setIpAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipAddress_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string ip_address = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpAddress() {
        ipAddress_ = getDefaultInstance().getIpAddress();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 网络信息
       * </pre>
       *
       * <code>string ip_address = 19;</code>
       * @param value The bytes for ipAddress to set.
       * @return This builder for chaining.
       */
      public Builder setIpAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ipAddress_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object macAddress_ = "";
      /**
       * <pre>
       * MAC地址
       * </pre>
       *
       * <code>string mac_address = 20;</code>
       * @return The macAddress.
       */
      public java.lang.String getMacAddress() {
        java.lang.Object ref = macAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          macAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * MAC地址
       * </pre>
       *
       * <code>string mac_address = 20;</code>
       * @return The bytes for macAddress.
       */
      public com.google.protobuf.ByteString
          getMacAddressBytes() {
        java.lang.Object ref = macAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          macAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * MAC地址
       * </pre>
       *
       * <code>string mac_address = 20;</code>
       * @param value The macAddress to set.
       * @return This builder for chaining.
       */
      public Builder setMacAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        macAddress_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * MAC地址
       * </pre>
       *
       * <code>string mac_address = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearMacAddress() {
        macAddress_ = getDefaultInstance().getMacAddress();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * MAC地址
       * </pre>
       *
       * <code>string mac_address = 20;</code>
       * @param value The bytes for macAddress to set.
       * @return This builder for chaining.
       */
      public Builder setMacAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        macAddress_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private int userId_ ;
      /**
       * <pre>
       * 用户关联
       * </pre>
       *
       * <code>uint32 user_id = 21;</code>
       * @return The userId.
       */
      @java.lang.Override
      public int getUserId() {
        return userId_;
      }
      /**
       * <pre>
       * 用户关联
       * </pre>
       *
       * <code>uint32 user_id = 21;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(int value) {

        userId_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户关联
       * </pre>
       *
       * <code>uint32 user_id = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00100000);
        userId_ = 0;
        onChanged();
        return this;
      }

      private common.Common.Timestamp firstSeenAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> firstSeenAtBuilder_;
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       * @return Whether the firstSeenAt field is set.
       */
      public boolean hasFirstSeenAt() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       * @return The firstSeenAt.
       */
      public common.Common.Timestamp getFirstSeenAt() {
        if (firstSeenAtBuilder_ == null) {
          return firstSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : firstSeenAt_;
        } else {
          return firstSeenAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public Builder setFirstSeenAt(common.Common.Timestamp value) {
        if (firstSeenAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          firstSeenAt_ = value;
        } else {
          firstSeenAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public Builder setFirstSeenAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (firstSeenAtBuilder_ == null) {
          firstSeenAt_ = builderForValue.build();
        } else {
          firstSeenAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public Builder mergeFirstSeenAt(common.Common.Timestamp value) {
        if (firstSeenAtBuilder_ == null) {
          if (((bitField0_ & 0x00200000) != 0) &&
            firstSeenAt_ != null &&
            firstSeenAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getFirstSeenAtBuilder().mergeFrom(value);
          } else {
            firstSeenAt_ = value;
          }
        } else {
          firstSeenAtBuilder_.mergeFrom(value);
        }
        if (firstSeenAt_ != null) {
          bitField0_ |= 0x00200000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public Builder clearFirstSeenAt() {
        bitField0_ = (bitField0_ & ~0x00200000);
        firstSeenAt_ = null;
        if (firstSeenAtBuilder_ != null) {
          firstSeenAtBuilder_.dispose();
          firstSeenAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public common.Common.Timestamp.Builder getFirstSeenAtBuilder() {
        bitField0_ |= 0x00200000;
        onChanged();
        return getFirstSeenAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      public common.Common.TimestampOrBuilder getFirstSeenAtOrBuilder() {
        if (firstSeenAtBuilder_ != null) {
          return firstSeenAtBuilder_.getMessageOrBuilder();
        } else {
          return firstSeenAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : firstSeenAt_;
        }
      }
      /**
       * <pre>
       * 时间信息
       * </pre>
       *
       * <code>.common.Timestamp first_seen_at = 22;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getFirstSeenAtFieldBuilder() {
        if (firstSeenAtBuilder_ == null) {
          firstSeenAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getFirstSeenAt(),
                  getParentForChildren(),
                  isClean());
          firstSeenAt_ = null;
        }
        return firstSeenAtBuilder_;
      }

      private common.Common.Timestamp lastSeenAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> lastSeenAtBuilder_;
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       * @return Whether the lastSeenAt field is set.
       */
      public boolean hasLastSeenAt() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       * @return The lastSeenAt.
       */
      public common.Common.Timestamp getLastSeenAt() {
        if (lastSeenAtBuilder_ == null) {
          return lastSeenAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastSeenAt_;
        } else {
          return lastSeenAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public Builder setLastSeenAt(common.Common.Timestamp value) {
        if (lastSeenAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lastSeenAt_ = value;
        } else {
          lastSeenAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public Builder setLastSeenAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (lastSeenAtBuilder_ == null) {
          lastSeenAt_ = builderForValue.build();
        } else {
          lastSeenAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public Builder mergeLastSeenAt(common.Common.Timestamp value) {
        if (lastSeenAtBuilder_ == null) {
          if (((bitField0_ & 0x00400000) != 0) &&
            lastSeenAt_ != null &&
            lastSeenAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getLastSeenAtBuilder().mergeFrom(value);
          } else {
            lastSeenAt_ = value;
          }
        } else {
          lastSeenAtBuilder_.mergeFrom(value);
        }
        if (lastSeenAt_ != null) {
          bitField0_ |= 0x00400000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public Builder clearLastSeenAt() {
        bitField0_ = (bitField0_ & ~0x00400000);
        lastSeenAt_ = null;
        if (lastSeenAtBuilder_ != null) {
          lastSeenAtBuilder_.dispose();
          lastSeenAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public common.Common.Timestamp.Builder getLastSeenAtBuilder() {
        bitField0_ |= 0x00400000;
        onChanged();
        return getLastSeenAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      public common.Common.TimestampOrBuilder getLastSeenAtOrBuilder() {
        if (lastSeenAtBuilder_ != null) {
          return lastSeenAtBuilder_.getMessageOrBuilder();
        } else {
          return lastSeenAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : lastSeenAt_;
        }
      }
      /**
       * <pre>
       * 最后见到时间
       * </pre>
       *
       * <code>.common.Timestamp last_seen_at = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getLastSeenAtFieldBuilder() {
        if (lastSeenAtBuilder_ == null) {
          lastSeenAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getLastSeenAt(),
                  getParentForChildren(),
                  isClean());
          lastSeenAt_ = null;
        }
        return lastSeenAtBuilder_;
      }

      private common.Common.Timestamp lastReportAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> lastReportAtBuilder_;
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       * @return Whether the lastReportAt field is set.
       */
      public boolean hasLastReportAt() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       * @return The lastReportAt.
       */
      public common.Common.Timestamp getLastReportAt() {
        if (lastReportAtBuilder_ == null) {
          return lastReportAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastReportAt_;
        } else {
          return lastReportAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public Builder setLastReportAt(common.Common.Timestamp value) {
        if (lastReportAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lastReportAt_ = value;
        } else {
          lastReportAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public Builder setLastReportAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (lastReportAtBuilder_ == null) {
          lastReportAt_ = builderForValue.build();
        } else {
          lastReportAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public Builder mergeLastReportAt(common.Common.Timestamp value) {
        if (lastReportAtBuilder_ == null) {
          if (((bitField0_ & 0x00800000) != 0) &&
            lastReportAt_ != null &&
            lastReportAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getLastReportAtBuilder().mergeFrom(value);
          } else {
            lastReportAt_ = value;
          }
        } else {
          lastReportAtBuilder_.mergeFrom(value);
        }
        if (lastReportAt_ != null) {
          bitField0_ |= 0x00800000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public Builder clearLastReportAt() {
        bitField0_ = (bitField0_ & ~0x00800000);
        lastReportAt_ = null;
        if (lastReportAtBuilder_ != null) {
          lastReportAtBuilder_.dispose();
          lastReportAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public common.Common.Timestamp.Builder getLastReportAtBuilder() {
        bitField0_ |= 0x00800000;
        onChanged();
        return getLastReportAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      public common.Common.TimestampOrBuilder getLastReportAtOrBuilder() {
        if (lastReportAtBuilder_ != null) {
          return lastReportAtBuilder_.getMessageOrBuilder();
        } else {
          return lastReportAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : lastReportAt_;
        }
      }
      /**
       * <pre>
       * 最后上报时间
       * </pre>
       *
       * <code>.common.Timestamp last_report_at = 24;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getLastReportAtFieldBuilder() {
        if (lastReportAtBuilder_ == null) {
          lastReportAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getLastReportAt(),
                  getParentForChildren(),
                  isClean());
          lastReportAt_ = null;
        }
        return lastReportAtBuilder_;
      }

      private common.Common.Timestamp createdAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> createdAtBuilder_;
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       * @return Whether the createdAt field is set.
       */
      public boolean hasCreatedAt() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       * @return The createdAt.
       */
      public common.Common.Timestamp getCreatedAt() {
        if (createdAtBuilder_ == null) {
          return createdAt_ == null ? common.Common.Timestamp.getDefaultInstance() : createdAt_;
        } else {
          return createdAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public Builder setCreatedAt(common.Common.Timestamp value) {
        if (createdAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          createdAt_ = value;
        } else {
          createdAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public Builder setCreatedAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (createdAtBuilder_ == null) {
          createdAt_ = builderForValue.build();
        } else {
          createdAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public Builder mergeCreatedAt(common.Common.Timestamp value) {
        if (createdAtBuilder_ == null) {
          if (((bitField0_ & 0x01000000) != 0) &&
            createdAt_ != null &&
            createdAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getCreatedAtBuilder().mergeFrom(value);
          } else {
            createdAt_ = value;
          }
        } else {
          createdAtBuilder_.mergeFrom(value);
        }
        if (createdAt_ != null) {
          bitField0_ |= 0x01000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public Builder clearCreatedAt() {
        bitField0_ = (bitField0_ & ~0x01000000);
        createdAt_ = null;
        if (createdAtBuilder_ != null) {
          createdAtBuilder_.dispose();
          createdAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public common.Common.Timestamp.Builder getCreatedAtBuilder() {
        bitField0_ |= 0x01000000;
        onChanged();
        return getCreatedAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      public common.Common.TimestampOrBuilder getCreatedAtOrBuilder() {
        if (createdAtBuilder_ != null) {
          return createdAtBuilder_.getMessageOrBuilder();
        } else {
          return createdAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : createdAt_;
        }
      }
      /**
       * <pre>
       * 创建时间
       * </pre>
       *
       * <code>.common.Timestamp created_at = 25;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getCreatedAtFieldBuilder() {
        if (createdAtBuilder_ == null) {
          createdAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getCreatedAt(),
                  getParentForChildren(),
                  isClean());
          createdAt_ = null;
        }
        return createdAtBuilder_;
      }

      private common.Common.Timestamp updatedAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> updatedAtBuilder_;
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       * @return Whether the updatedAt field is set.
       */
      public boolean hasUpdatedAt() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       * @return The updatedAt.
       */
      public common.Common.Timestamp getUpdatedAt() {
        if (updatedAtBuilder_ == null) {
          return updatedAt_ == null ? common.Common.Timestamp.getDefaultInstance() : updatedAt_;
        } else {
          return updatedAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public Builder setUpdatedAt(common.Common.Timestamp value) {
        if (updatedAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          updatedAt_ = value;
        } else {
          updatedAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public Builder setUpdatedAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (updatedAtBuilder_ == null) {
          updatedAt_ = builderForValue.build();
        } else {
          updatedAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public Builder mergeUpdatedAt(common.Common.Timestamp value) {
        if (updatedAtBuilder_ == null) {
          if (((bitField0_ & 0x02000000) != 0) &&
            updatedAt_ != null &&
            updatedAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getUpdatedAtBuilder().mergeFrom(value);
          } else {
            updatedAt_ = value;
          }
        } else {
          updatedAtBuilder_.mergeFrom(value);
        }
        if (updatedAt_ != null) {
          bitField0_ |= 0x02000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public Builder clearUpdatedAt() {
        bitField0_ = (bitField0_ & ~0x02000000);
        updatedAt_ = null;
        if (updatedAtBuilder_ != null) {
          updatedAtBuilder_.dispose();
          updatedAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public common.Common.Timestamp.Builder getUpdatedAtBuilder() {
        bitField0_ |= 0x02000000;
        onChanged();
        return getUpdatedAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      public common.Common.TimestampOrBuilder getUpdatedAtOrBuilder() {
        if (updatedAtBuilder_ != null) {
          return updatedAtBuilder_.getMessageOrBuilder();
        } else {
          return updatedAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : updatedAt_;
        }
      }
      /**
       * <pre>
       * 更新时间
       * </pre>
       *
       * <code>.common.Timestamp updated_at = 26;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getUpdatedAtFieldBuilder() {
        if (updatedAtBuilder_ == null) {
          updatedAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getUpdatedAt(),
                  getParentForChildren(),
                  isClean());
          updatedAt_ = null;
        }
        return updatedAtBuilder_;
      }

      private int status_ ;
      /**
       * <pre>
       * 状态信息
       * </pre>
       *
       * <code>int32 status = 27;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       * 状态信息
       * </pre>
       *
       * <code>int32 status = 27;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {

        status_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态信息
       * </pre>
       *
       * <code>int32 status = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x04000000);
        status_ = 0;
        onChanged();
        return this;
      }

      private boolean isActive_ ;
      /**
       * <pre>
       * 是否活跃
       * </pre>
       *
       * <code>bool is_active = 28;</code>
       * @return The isActive.
       */
      @java.lang.Override
      public boolean getIsActive() {
        return isActive_;
      }
      /**
       * <pre>
       * 是否活跃
       * </pre>
       *
       * <code>bool is_active = 28;</code>
       * @param value The isActive to set.
       * @return This builder for chaining.
       */
      public Builder setIsActive(boolean value) {

        isActive_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否活跃
       * </pre>
       *
       * <code>bool is_active = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsActive() {
        bitField0_ = (bitField0_ & ~0x08000000);
        isActive_ = false;
        onChanged();
        return this;
      }

      private long reportCount_ ;
      /**
       * <pre>
       * 上报次数
       * </pre>
       *
       * <code>int64 report_count = 29;</code>
       * @return The reportCount.
       */
      @java.lang.Override
      public long getReportCount() {
        return reportCount_;
      }
      /**
       * <pre>
       * 上报次数
       * </pre>
       *
       * <code>int64 report_count = 29;</code>
       * @param value The reportCount to set.
       * @return This builder for chaining.
       */
      public Builder setReportCount(long value) {

        reportCount_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上报次数
       * </pre>
       *
       * <code>int64 report_count = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearReportCount() {
        bitField0_ = (bitField0_ & ~0x10000000);
        reportCount_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object remark_ = "";
      /**
       * <pre>
       * 备注
       * </pre>
       *
       * <code>string remark = 30;</code>
       * @return The remark.
       */
      public java.lang.String getRemark() {
        java.lang.Object ref = remark_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          remark_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 备注
       * </pre>
       *
       * <code>string remark = 30;</code>
       * @return The bytes for remark.
       */
      public com.google.protobuf.ByteString
          getRemarkBytes() {
        java.lang.Object ref = remark_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          remark_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 备注
       * </pre>
       *
       * <code>string remark = 30;</code>
       * @param value The remark to set.
       * @return This builder for chaining.
       */
      public Builder setRemark(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        remark_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 备注
       * </pre>
       *
       * <code>string remark = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearRemark() {
        remark_ = getDefaultInstance().getRemark();
        bitField0_ = (bitField0_ & ~0x20000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 备注
       * </pre>
       *
       * <code>string remark = 30;</code>
       * @param value The bytes for remark to set.
       * @return This builder for chaining.
       */
      public Builder setRemarkBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        remark_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }

      private boolean isDefault_ ;
      /**
       * <pre>
       * 是否默认
       * </pre>
       *
       * <code>bool is_default = 31;</code>
       * @return The isDefault.
       */
      @java.lang.Override
      public boolean getIsDefault() {
        return isDefault_;
      }
      /**
       * <pre>
       * 是否默认
       * </pre>
       *
       * <code>bool is_default = 31;</code>
       * @param value The isDefault to set.
       * @return This builder for chaining.
       */
      public Builder setIsDefault(boolean value) {

        isDefault_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否默认
       * </pre>
       *
       * <code>bool is_default = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsDefault() {
        bitField0_ = (bitField0_ & ~0x40000000);
        isDefault_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:device.Device)
    }

    // @@protoc_insertion_point(class_scope:device.Device)
    private static final device.DeviceOuterClass.Device DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new device.DeviceOuterClass.Device();
    }

    public static device.DeviceOuterClass.Device getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Device>
        PARSER = new com.google.protobuf.AbstractParser<Device>() {
      @java.lang.Override
      public Device parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Device> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Device> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public device.DeviceOuterClass.Device getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceOnlineStatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:device.DeviceOnlineStatus)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否在线
     * </pre>
     *
     * <code>bool is_online = 1;</code>
     * @return The isOnline.
     */
    boolean getIsOnline();

    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     * @return Whether the lastActiveAt field is set.
     */
    boolean hasLastActiveAt();
    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     * @return The lastActiveAt.
     */
    common.Common.Timestamp getLastActiveAt();
    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     */
    common.Common.TimestampOrBuilder getLastActiveAtOrBuilder();

    /**
     * <pre>
     * 会话类型
     * </pre>
     *
     * <code>string session_type = 3;</code>
     * @return The sessionType.
     */
    java.lang.String getSessionType();
    /**
     * <pre>
     * 会话类型
     * </pre>
     *
     * <code>string session_type = 3;</code>
     * @return The bytes for sessionType.
     */
    com.google.protobuf.ByteString
        getSessionTypeBytes();

    /**
     * <pre>
     * 登录IP地址
     * </pre>
     *
     * <code>string ip_address = 4;</code>
     * @return The ipAddress.
     */
    java.lang.String getIpAddress();
    /**
     * <pre>
     * 登录IP地址
     * </pre>
     *
     * <code>string ip_address = 4;</code>
     * @return The bytes for ipAddress.
     */
    com.google.protobuf.ByteString
        getIpAddressBytes();

    /**
     * <pre>
     * 用户代理
     * </pre>
     *
     * <code>string user_agent = 5;</code>
     * @return The userAgent.
     */
    java.lang.String getUserAgent();
    /**
     * <pre>
     * 用户代理
     * </pre>
     *
     * <code>string user_agent = 5;</code>
     * @return The bytes for userAgent.
     */
    com.google.protobuf.ByteString
        getUserAgentBytes();

    /**
     * <pre>
     * 设备名称
     * </pre>
     *
     * <code>string device_name = 6;</code>
     * @return The deviceName.
     */
    java.lang.String getDeviceName();
    /**
     * <pre>
     * 设备名称
     * </pre>
     *
     * <code>string device_name = 6;</code>
     * @return The bytes for deviceName.
     */
    com.google.protobuf.ByteString
        getDeviceNameBytes();

    /**
     * <pre>
     * 操作系统信息
     * </pre>
     *
     * <code>string os_info = 7;</code>
     * @return The osInfo.
     */
    java.lang.String getOsInfo();
    /**
     * <pre>
     * 操作系统信息
     * </pre>
     *
     * <code>string os_info = 7;</code>
     * @return The bytes for osInfo.
     */
    com.google.protobuf.ByteString
        getOsInfoBytes();

    /**
     * <pre>
     * 应用版本
     * </pre>
     *
     * <code>string app_version = 8;</code>
     * @return The appVersion.
     */
    java.lang.String getAppVersion();
    /**
     * <pre>
     * 应用版本
     * </pre>
     *
     * <code>string app_version = 8;</code>
     * @return The bytes for appVersion.
     */
    com.google.protobuf.ByteString
        getAppVersionBytes();
  }
  /**
   * <pre>
   * 设备在线状态信息
   * </pre>
   *
   * Protobuf type {@code device.DeviceOnlineStatus}
   */
  public static final class DeviceOnlineStatus extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:device.DeviceOnlineStatus)
      DeviceOnlineStatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceOnlineStatus.newBuilder() to construct.
    private DeviceOnlineStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceOnlineStatus() {
      sessionType_ = "";
      ipAddress_ = "";
      userAgent_ = "";
      deviceName_ = "";
      osInfo_ = "";
      appVersion_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceOnlineStatus();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return device.DeviceOuterClass.internal_static_device_DeviceOnlineStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return device.DeviceOuterClass.internal_static_device_DeviceOnlineStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              device.DeviceOuterClass.DeviceOnlineStatus.class, device.DeviceOuterClass.DeviceOnlineStatus.Builder.class);
    }

    private int bitField0_;
    public static final int IS_ONLINE_FIELD_NUMBER = 1;
    private boolean isOnline_ = false;
    /**
     * <pre>
     * 是否在线
     * </pre>
     *
     * <code>bool is_online = 1;</code>
     * @return The isOnline.
     */
    @java.lang.Override
    public boolean getIsOnline() {
      return isOnline_;
    }

    public static final int LAST_ACTIVE_AT_FIELD_NUMBER = 2;
    private common.Common.Timestamp lastActiveAt_;
    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     * @return Whether the lastActiveAt field is set.
     */
    @java.lang.Override
    public boolean hasLastActiveAt() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     * @return The lastActiveAt.
     */
    @java.lang.Override
    public common.Common.Timestamp getLastActiveAt() {
      return lastActiveAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastActiveAt_;
    }
    /**
     * <pre>
     * 最后活跃时间
     * </pre>
     *
     * <code>.common.Timestamp last_active_at = 2;</code>
     */
    @java.lang.Override
    public common.Common.TimestampOrBuilder getLastActiveAtOrBuilder() {
      return lastActiveAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastActiveAt_;
    }

    public static final int SESSION_TYPE_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object sessionType_ = "";
    /**
     * <pre>
     * 会话类型
     * </pre>
     *
     * <code>string session_type = 3;</code>
     * @return The sessionType.
     */
    @java.lang.Override
    public java.lang.String getSessionType() {
      java.lang.Object ref = sessionType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionType_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 会话类型
     * </pre>
     *
     * <code>string session_type = 3;</code>
     * @return The bytes for sessionType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSessionTypeBytes() {
      java.lang.Object ref = sessionType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ADDRESS_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ipAddress_ = "";
    /**
     * <pre>
     * 登录IP地址
     * </pre>
     *
     * <code>string ip_address = 4;</code>
     * @return The ipAddress.
     */
    @java.lang.Override
    public java.lang.String getIpAddress() {
      java.lang.Object ref = ipAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ipAddress_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 登录IP地址
     * </pre>
     *
     * <code>string ip_address = 4;</code>
     * @return The bytes for ipAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIpAddressBytes() {
      java.lang.Object ref = ipAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USER_AGENT_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object userAgent_ = "";
    /**
     * <pre>
     * 用户代理
     * </pre>
     *
     * <code>string user_agent = 5;</code>
     * @return The userAgent.
     */
    @java.lang.Override
    public java.lang.String getUserAgent() {
      java.lang.Object ref = userAgent_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userAgent_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 用户代理
     * </pre>
     *
     * <code>string user_agent = 5;</code>
     * @return The bytes for userAgent.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserAgentBytes() {
      java.lang.Object ref = userAgent_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userAgent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICE_NAME_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deviceName_ = "";
    /**
     * <pre>
     * 设备名称
     * </pre>
     *
     * <code>string device_name = 6;</code>
     * @return The deviceName.
     */
    @java.lang.Override
    public java.lang.String getDeviceName() {
      java.lang.Object ref = deviceName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备名称
     * </pre>
     *
     * <code>string device_name = 6;</code>
     * @return The bytes for deviceName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceNameBytes() {
      java.lang.Object ref = deviceName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OS_INFO_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object osInfo_ = "";
    /**
     * <pre>
     * 操作系统信息
     * </pre>
     *
     * <code>string os_info = 7;</code>
     * @return The osInfo.
     */
    @java.lang.Override
    public java.lang.String getOsInfo() {
      java.lang.Object ref = osInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        osInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 操作系统信息
     * </pre>
     *
     * <code>string os_info = 7;</code>
     * @return The bytes for osInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOsInfoBytes() {
      java.lang.Object ref = osInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        osInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_VERSION_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appVersion_ = "";
    /**
     * <pre>
     * 应用版本
     * </pre>
     *
     * <code>string app_version = 8;</code>
     * @return The appVersion.
     */
    @java.lang.Override
    public java.lang.String getAppVersion() {
      java.lang.Object ref = appVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 应用版本
     * </pre>
     *
     * <code>string app_version = 8;</code>
     * @return The bytes for appVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppVersionBytes() {
      java.lang.Object ref = appVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (isOnline_ != false) {
        output.writeBool(1, isOnline_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getLastActiveAt());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sessionType_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sessionType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ipAddress_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, ipAddress_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userAgent_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, userAgent_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deviceName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, osInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, appVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (isOnline_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isOnline_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getLastActiveAt());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sessionType_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sessionType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ipAddress_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, ipAddress_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userAgent_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, userAgent_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deviceName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, osInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, appVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof device.DeviceOuterClass.DeviceOnlineStatus)) {
        return super.equals(obj);
      }
      device.DeviceOuterClass.DeviceOnlineStatus other = (device.DeviceOuterClass.DeviceOnlineStatus) obj;

      if (getIsOnline()
          != other.getIsOnline()) return false;
      if (hasLastActiveAt() != other.hasLastActiveAt()) return false;
      if (hasLastActiveAt()) {
        if (!getLastActiveAt()
            .equals(other.getLastActiveAt())) return false;
      }
      if (!getSessionType()
          .equals(other.getSessionType())) return false;
      if (!getIpAddress()
          .equals(other.getIpAddress())) return false;
      if (!getUserAgent()
          .equals(other.getUserAgent())) return false;
      if (!getDeviceName()
          .equals(other.getDeviceName())) return false;
      if (!getOsInfo()
          .equals(other.getOsInfo())) return false;
      if (!getAppVersion()
          .equals(other.getAppVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IS_ONLINE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsOnline());
      if (hasLastActiveAt()) {
        hash = (37 * hash) + LAST_ACTIVE_AT_FIELD_NUMBER;
        hash = (53 * hash) + getLastActiveAt().hashCode();
      }
      hash = (37 * hash) + SESSION_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getSessionType().hashCode();
      hash = (37 * hash) + IP_ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getIpAddress().hashCode();
      hash = (37 * hash) + USER_AGENT_FIELD_NUMBER;
      hash = (53 * hash) + getUserAgent().hashCode();
      hash = (37 * hash) + DEVICE_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceName().hashCode();
      hash = (37 * hash) + OS_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getOsInfo().hashCode();
      hash = (37 * hash) + APP_VERSION_FIELD_NUMBER;
      hash = (53 * hash) + getAppVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static device.DeviceOuterClass.DeviceOnlineStatus parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static device.DeviceOuterClass.DeviceOnlineStatus parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.DeviceOnlineStatus parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(device.DeviceOuterClass.DeviceOnlineStatus prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 设备在线状态信息
     * </pre>
     *
     * Protobuf type {@code device.DeviceOnlineStatus}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:device.DeviceOnlineStatus)
        device.DeviceOuterClass.DeviceOnlineStatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return device.DeviceOuterClass.internal_static_device_DeviceOnlineStatus_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return device.DeviceOuterClass.internal_static_device_DeviceOnlineStatus_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                device.DeviceOuterClass.DeviceOnlineStatus.class, device.DeviceOuterClass.DeviceOnlineStatus.Builder.class);
      }

      // Construct using device.DeviceOuterClass.DeviceOnlineStatus.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getLastActiveAtFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        isOnline_ = false;
        lastActiveAt_ = null;
        if (lastActiveAtBuilder_ != null) {
          lastActiveAtBuilder_.dispose();
          lastActiveAtBuilder_ = null;
        }
        sessionType_ = "";
        ipAddress_ = "";
        userAgent_ = "";
        deviceName_ = "";
        osInfo_ = "";
        appVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return device.DeviceOuterClass.internal_static_device_DeviceOnlineStatus_descriptor;
      }

      @java.lang.Override
      public device.DeviceOuterClass.DeviceOnlineStatus getDefaultInstanceForType() {
        return device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance();
      }

      @java.lang.Override
      public device.DeviceOuterClass.DeviceOnlineStatus build() {
        device.DeviceOuterClass.DeviceOnlineStatus result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public device.DeviceOuterClass.DeviceOnlineStatus buildPartial() {
        device.DeviceOuterClass.DeviceOnlineStatus result = new device.DeviceOuterClass.DeviceOnlineStatus(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(device.DeviceOuterClass.DeviceOnlineStatus result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isOnline_ = isOnline_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastActiveAt_ = lastActiveAtBuilder_ == null
              ? lastActiveAt_
              : lastActiveAtBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.sessionType_ = sessionType_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ipAddress_ = ipAddress_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.userAgent_ = userAgent_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.deviceName_ = deviceName_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.osInfo_ = osInfo_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.appVersion_ = appVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof device.DeviceOuterClass.DeviceOnlineStatus) {
          return mergeFrom((device.DeviceOuterClass.DeviceOnlineStatus)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(device.DeviceOuterClass.DeviceOnlineStatus other) {
        if (other == device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance()) return this;
        if (other.getIsOnline() != false) {
          setIsOnline(other.getIsOnline());
        }
        if (other.hasLastActiveAt()) {
          mergeLastActiveAt(other.getLastActiveAt());
        }
        if (!other.getSessionType().isEmpty()) {
          sessionType_ = other.sessionType_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getIpAddress().isEmpty()) {
          ipAddress_ = other.ipAddress_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getUserAgent().isEmpty()) {
          userAgent_ = other.userAgent_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getDeviceName().isEmpty()) {
          deviceName_ = other.deviceName_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getOsInfo().isEmpty()) {
          osInfo_ = other.osInfo_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (!other.getAppVersion().isEmpty()) {
          appVersion_ = other.appVersion_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                isOnline_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getLastActiveAtFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                sessionType_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                ipAddress_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                userAgent_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                deviceName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                osInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                appVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean isOnline_ ;
      /**
       * <pre>
       * 是否在线
       * </pre>
       *
       * <code>bool is_online = 1;</code>
       * @return The isOnline.
       */
      @java.lang.Override
      public boolean getIsOnline() {
        return isOnline_;
      }
      /**
       * <pre>
       * 是否在线
       * </pre>
       *
       * <code>bool is_online = 1;</code>
       * @param value The isOnline to set.
       * @return This builder for chaining.
       */
      public Builder setIsOnline(boolean value) {

        isOnline_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否在线
       * </pre>
       *
       * <code>bool is_online = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOnline() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isOnline_ = false;
        onChanged();
        return this;
      }

      private common.Common.Timestamp lastActiveAt_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> lastActiveAtBuilder_;
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       * @return Whether the lastActiveAt field is set.
       */
      public boolean hasLastActiveAt() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       * @return The lastActiveAt.
       */
      public common.Common.Timestamp getLastActiveAt() {
        if (lastActiveAtBuilder_ == null) {
          return lastActiveAt_ == null ? common.Common.Timestamp.getDefaultInstance() : lastActiveAt_;
        } else {
          return lastActiveAtBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public Builder setLastActiveAt(common.Common.Timestamp value) {
        if (lastActiveAtBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lastActiveAt_ = value;
        } else {
          lastActiveAtBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public Builder setLastActiveAt(
          common.Common.Timestamp.Builder builderForValue) {
        if (lastActiveAtBuilder_ == null) {
          lastActiveAt_ = builderForValue.build();
        } else {
          lastActiveAtBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public Builder mergeLastActiveAt(common.Common.Timestamp value) {
        if (lastActiveAtBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            lastActiveAt_ != null &&
            lastActiveAt_ != common.Common.Timestamp.getDefaultInstance()) {
            getLastActiveAtBuilder().mergeFrom(value);
          } else {
            lastActiveAt_ = value;
          }
        } else {
          lastActiveAtBuilder_.mergeFrom(value);
        }
        if (lastActiveAt_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public Builder clearLastActiveAt() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastActiveAt_ = null;
        if (lastActiveAtBuilder_ != null) {
          lastActiveAtBuilder_.dispose();
          lastActiveAtBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public common.Common.Timestamp.Builder getLastActiveAtBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getLastActiveAtFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      public common.Common.TimestampOrBuilder getLastActiveAtOrBuilder() {
        if (lastActiveAtBuilder_ != null) {
          return lastActiveAtBuilder_.getMessageOrBuilder();
        } else {
          return lastActiveAt_ == null ?
              common.Common.Timestamp.getDefaultInstance() : lastActiveAt_;
        }
      }
      /**
       * <pre>
       * 最后活跃时间
       * </pre>
       *
       * <code>.common.Timestamp last_active_at = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder> 
          getLastActiveAtFieldBuilder() {
        if (lastActiveAtBuilder_ == null) {
          lastActiveAtBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.Timestamp, common.Common.Timestamp.Builder, common.Common.TimestampOrBuilder>(
                  getLastActiveAt(),
                  getParentForChildren(),
                  isClean());
          lastActiveAt_ = null;
        }
        return lastActiveAtBuilder_;
      }

      private java.lang.Object sessionType_ = "";
      /**
       * <pre>
       * 会话类型
       * </pre>
       *
       * <code>string session_type = 3;</code>
       * @return The sessionType.
       */
      public java.lang.String getSessionType() {
        java.lang.Object ref = sessionType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sessionType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 会话类型
       * </pre>
       *
       * <code>string session_type = 3;</code>
       * @return The bytes for sessionType.
       */
      public com.google.protobuf.ByteString
          getSessionTypeBytes() {
        java.lang.Object ref = sessionType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sessionType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 会话类型
       * </pre>
       *
       * <code>string session_type = 3;</code>
       * @param value The sessionType to set.
       * @return This builder for chaining.
       */
      public Builder setSessionType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        sessionType_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 会话类型
       * </pre>
       *
       * <code>string session_type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSessionType() {
        sessionType_ = getDefaultInstance().getSessionType();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 会话类型
       * </pre>
       *
       * <code>string session_type = 3;</code>
       * @param value The bytes for sessionType to set.
       * @return This builder for chaining.
       */
      public Builder setSessionTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        sessionType_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object ipAddress_ = "";
      /**
       * <pre>
       * 登录IP地址
       * </pre>
       *
       * <code>string ip_address = 4;</code>
       * @return The ipAddress.
       */
      public java.lang.String getIpAddress() {
        java.lang.Object ref = ipAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          ipAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 登录IP地址
       * </pre>
       *
       * <code>string ip_address = 4;</code>
       * @return The bytes for ipAddress.
       */
      public com.google.protobuf.ByteString
          getIpAddressBytes() {
        java.lang.Object ref = ipAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 登录IP地址
       * </pre>
       *
       * <code>string ip_address = 4;</code>
       * @param value The ipAddress to set.
       * @return This builder for chaining.
       */
      public Builder setIpAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipAddress_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 登录IP地址
       * </pre>
       *
       * <code>string ip_address = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpAddress() {
        ipAddress_ = getDefaultInstance().getIpAddress();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 登录IP地址
       * </pre>
       *
       * <code>string ip_address = 4;</code>
       * @param value The bytes for ipAddress to set.
       * @return This builder for chaining.
       */
      public Builder setIpAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ipAddress_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object userAgent_ = "";
      /**
       * <pre>
       * 用户代理
       * </pre>
       *
       * <code>string user_agent = 5;</code>
       * @return The userAgent.
       */
      public java.lang.String getUserAgent() {
        java.lang.Object ref = userAgent_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userAgent_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 用户代理
       * </pre>
       *
       * <code>string user_agent = 5;</code>
       * @return The bytes for userAgent.
       */
      public com.google.protobuf.ByteString
          getUserAgentBytes() {
        java.lang.Object ref = userAgent_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userAgent_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 用户代理
       * </pre>
       *
       * <code>string user_agent = 5;</code>
       * @param value The userAgent to set.
       * @return This builder for chaining.
       */
      public Builder setUserAgent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        userAgent_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户代理
       * </pre>
       *
       * <code>string user_agent = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserAgent() {
        userAgent_ = getDefaultInstance().getUserAgent();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户代理
       * </pre>
       *
       * <code>string user_agent = 5;</code>
       * @param value The bytes for userAgent to set.
       * @return This builder for chaining.
       */
      public Builder setUserAgentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        userAgent_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object deviceName_ = "";
      /**
       * <pre>
       * 设备名称
       * </pre>
       *
       * <code>string device_name = 6;</code>
       * @return The deviceName.
       */
      public java.lang.String getDeviceName() {
        java.lang.Object ref = deviceName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备名称
       * </pre>
       *
       * <code>string device_name = 6;</code>
       * @return The bytes for deviceName.
       */
      public com.google.protobuf.ByteString
          getDeviceNameBytes() {
        java.lang.Object ref = deviceName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备名称
       * </pre>
       *
       * <code>string device_name = 6;</code>
       * @param value The deviceName to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deviceName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备名称
       * </pre>
       *
       * <code>string device_name = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceName() {
        deviceName_ = getDefaultInstance().getDeviceName();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备名称
       * </pre>
       *
       * <code>string device_name = 6;</code>
       * @param value The bytes for deviceName to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deviceName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object osInfo_ = "";
      /**
       * <pre>
       * 操作系统信息
       * </pre>
       *
       * <code>string os_info = 7;</code>
       * @return The osInfo.
       */
      public java.lang.String getOsInfo() {
        java.lang.Object ref = osInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          osInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 操作系统信息
       * </pre>
       *
       * <code>string os_info = 7;</code>
       * @return The bytes for osInfo.
       */
      public com.google.protobuf.ByteString
          getOsInfoBytes() {
        java.lang.Object ref = osInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          osInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 操作系统信息
       * </pre>
       *
       * <code>string os_info = 7;</code>
       * @param value The osInfo to set.
       * @return This builder for chaining.
       */
      public Builder setOsInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        osInfo_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作系统信息
       * </pre>
       *
       * <code>string os_info = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsInfo() {
        osInfo_ = getDefaultInstance().getOsInfo();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作系统信息
       * </pre>
       *
       * <code>string os_info = 7;</code>
       * @param value The bytes for osInfo to set.
       * @return This builder for chaining.
       */
      public Builder setOsInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        osInfo_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object appVersion_ = "";
      /**
       * <pre>
       * 应用版本
       * </pre>
       *
       * <code>string app_version = 8;</code>
       * @return The appVersion.
       */
      public java.lang.String getAppVersion() {
        java.lang.Object ref = appVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用版本
       * </pre>
       *
       * <code>string app_version = 8;</code>
       * @return The bytes for appVersion.
       */
      public com.google.protobuf.ByteString
          getAppVersionBytes() {
        java.lang.Object ref = appVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用版本
       * </pre>
       *
       * <code>string app_version = 8;</code>
       * @param value The appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appVersion_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用版本
       * </pre>
       *
       * <code>string app_version = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppVersion() {
        appVersion_ = getDefaultInstance().getAppVersion();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用版本
       * </pre>
       *
       * <code>string app_version = 8;</code>
       * @param value The bytes for appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appVersion_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:device.DeviceOnlineStatus)
    }

    // @@protoc_insertion_point(class_scope:device.DeviceOnlineStatus)
    private static final device.DeviceOuterClass.DeviceOnlineStatus DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new device.DeviceOuterClass.DeviceOnlineStatus();
    }

    public static device.DeviceOuterClass.DeviceOnlineStatus getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceOnlineStatus>
        PARSER = new com.google.protobuf.AbstractParser<DeviceOnlineStatus>() {
      @java.lang.Override
      public DeviceOnlineStatus parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceOnlineStatus> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceOnlineStatus> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public device.DeviceOuterClass.DeviceOnlineStatus getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetDeviceByIDRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:device.GetDeviceByIDRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string device_id = 1;</code>
     * @return The deviceId.
     */
    java.lang.String getDeviceId();
    /**
     * <code>string device_id = 1;</code>
     * @return The bytes for deviceId.
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();
  }
  /**
   * <pre>
   * 根据设备ID获取设备信息请求
   * </pre>
   *
   * Protobuf type {@code device.GetDeviceByIDRequest}
   */
  public static final class GetDeviceByIDRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:device.GetDeviceByIDRequest)
      GetDeviceByIDRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetDeviceByIDRequest.newBuilder() to construct.
    private GetDeviceByIDRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetDeviceByIDRequest() {
      deviceId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetDeviceByIDRequest();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return device.DeviceOuterClass.internal_static_device_GetDeviceByIDRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return device.DeviceOuterClass.internal_static_device_GetDeviceByIDRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              device.DeviceOuterClass.GetDeviceByIDRequest.class, device.DeviceOuterClass.GetDeviceByIDRequest.Builder.class);
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object deviceId_ = "";
    /**
     * <code>string device_id = 1;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      }
    }
    /**
     * <code>string device_id = 1;</code>
     * @return The bytes for deviceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, deviceId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, deviceId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof device.DeviceOuterClass.GetDeviceByIDRequest)) {
        return super.equals(obj);
      }
      device.DeviceOuterClass.GetDeviceByIDRequest other = (device.DeviceOuterClass.GetDeviceByIDRequest) obj;

      if (!getDeviceId()
          .equals(other.getDeviceId())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static device.DeviceOuterClass.GetDeviceByIDRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static device.DeviceOuterClass.GetDeviceByIDRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.GetDeviceByIDRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(device.DeviceOuterClass.GetDeviceByIDRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 根据设备ID获取设备信息请求
     * </pre>
     *
     * Protobuf type {@code device.GetDeviceByIDRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:device.GetDeviceByIDRequest)
        device.DeviceOuterClass.GetDeviceByIDRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceByIDRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceByIDRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                device.DeviceOuterClass.GetDeviceByIDRequest.class, device.DeviceOuterClass.GetDeviceByIDRequest.Builder.class);
      }

      // Construct using device.DeviceOuterClass.GetDeviceByIDRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        deviceId_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceByIDRequest_descriptor;
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceByIDRequest getDefaultInstanceForType() {
        return device.DeviceOuterClass.GetDeviceByIDRequest.getDefaultInstance();
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceByIDRequest build() {
        device.DeviceOuterClass.GetDeviceByIDRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceByIDRequest buildPartial() {
        device.DeviceOuterClass.GetDeviceByIDRequest result = new device.DeviceOuterClass.GetDeviceByIDRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(device.DeviceOuterClass.GetDeviceByIDRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.deviceId_ = deviceId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof device.DeviceOuterClass.GetDeviceByIDRequest) {
          return mergeFrom((device.DeviceOuterClass.GetDeviceByIDRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(device.DeviceOuterClass.GetDeviceByIDRequest other) {
        if (other == device.DeviceOuterClass.GetDeviceByIDRequest.getDefaultInstance()) return this;
        if (!other.getDeviceId().isEmpty()) {
          deviceId_ = other.deviceId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                deviceId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object deviceId_ = "";
      /**
       * <code>string device_id = 1;</code>
       * @return The deviceId.
       */
      public java.lang.String getDeviceId() {
        java.lang.Object ref = deviceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string device_id = 1;</code>
       * @return The bytes for deviceId.
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        java.lang.Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string device_id = 1;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        deviceId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string device_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        deviceId_ = getDefaultInstance().getDeviceId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string device_id = 1;</code>
       * @param value The bytes for deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        deviceId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:device.GetDeviceByIDRequest)
    }

    // @@protoc_insertion_point(class_scope:device.GetDeviceByIDRequest)
    private static final device.DeviceOuterClass.GetDeviceByIDRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new device.DeviceOuterClass.GetDeviceByIDRequest();
    }

    public static device.DeviceOuterClass.GetDeviceByIDRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetDeviceByIDRequest>
        PARSER = new com.google.protobuf.AbstractParser<GetDeviceByIDRequest>() {
      @java.lang.Override
      public GetDeviceByIDRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetDeviceByIDRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetDeviceByIDRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public device.DeviceOuterClass.GetDeviceByIDRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetDeviceInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:device.GetDeviceInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.common.BaseResponse base = 1;</code>
     * @return Whether the base field is set.
     */
    boolean hasBase();
    /**
     * <code>.common.BaseResponse base = 1;</code>
     * @return The base.
     */
    common.Common.BaseResponse getBase();
    /**
     * <code>.common.BaseResponse base = 1;</code>
     */
    common.Common.BaseResponseOrBuilder getBaseOrBuilder();

    /**
     * <code>.device.Device device = 2;</code>
     * @return Whether the device field is set.
     */
    boolean hasDevice();
    /**
     * <code>.device.Device device = 2;</code>
     * @return The device.
     */
    device.DeviceOuterClass.Device getDevice();
    /**
     * <code>.device.Device device = 2;</code>
     */
    device.DeviceOuterClass.DeviceOrBuilder getDeviceOrBuilder();

    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     * @return Whether the onlineStatus field is set.
     */
    boolean hasOnlineStatus();
    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     * @return The onlineStatus.
     */
    device.DeviceOuterClass.DeviceOnlineStatus getOnlineStatus();
    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     */
    device.DeviceOuterClass.DeviceOnlineStatusOrBuilder getOnlineStatusOrBuilder();
  }
  /**
   * <pre>
   * 获取设备信息和在线状态响应
   * </pre>
   *
   * Protobuf type {@code device.GetDeviceInfoResponse}
   */
  public static final class GetDeviceInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:device.GetDeviceInfoResponse)
      GetDeviceInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetDeviceInfoResponse.newBuilder() to construct.
    private GetDeviceInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetDeviceInfoResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetDeviceInfoResponse();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return device.DeviceOuterClass.internal_static_device_GetDeviceInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return device.DeviceOuterClass.internal_static_device_GetDeviceInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              device.DeviceOuterClass.GetDeviceInfoResponse.class, device.DeviceOuterClass.GetDeviceInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int BASE_FIELD_NUMBER = 1;
    private common.Common.BaseResponse base_;
    /**
     * <code>.common.BaseResponse base = 1;</code>
     * @return Whether the base field is set.
     */
    @java.lang.Override
    public boolean hasBase() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.common.BaseResponse base = 1;</code>
     * @return The base.
     */
    @java.lang.Override
    public common.Common.BaseResponse getBase() {
      return base_ == null ? common.Common.BaseResponse.getDefaultInstance() : base_;
    }
    /**
     * <code>.common.BaseResponse base = 1;</code>
     */
    @java.lang.Override
    public common.Common.BaseResponseOrBuilder getBaseOrBuilder() {
      return base_ == null ? common.Common.BaseResponse.getDefaultInstance() : base_;
    }

    public static final int DEVICE_FIELD_NUMBER = 2;
    private device.DeviceOuterClass.Device device_;
    /**
     * <code>.device.Device device = 2;</code>
     * @return Whether the device field is set.
     */
    @java.lang.Override
    public boolean hasDevice() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.device.Device device = 2;</code>
     * @return The device.
     */
    @java.lang.Override
    public device.DeviceOuterClass.Device getDevice() {
      return device_ == null ? device.DeviceOuterClass.Device.getDefaultInstance() : device_;
    }
    /**
     * <code>.device.Device device = 2;</code>
     */
    @java.lang.Override
    public device.DeviceOuterClass.DeviceOrBuilder getDeviceOrBuilder() {
      return device_ == null ? device.DeviceOuterClass.Device.getDefaultInstance() : device_;
    }

    public static final int ONLINE_STATUS_FIELD_NUMBER = 3;
    private device.DeviceOuterClass.DeviceOnlineStatus onlineStatus_;
    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     * @return Whether the onlineStatus field is set.
     */
    @java.lang.Override
    public boolean hasOnlineStatus() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     * @return The onlineStatus.
     */
    @java.lang.Override
    public device.DeviceOuterClass.DeviceOnlineStatus getOnlineStatus() {
      return onlineStatus_ == null ? device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance() : onlineStatus_;
    }
    /**
     * <code>.device.DeviceOnlineStatus online_status = 3;</code>
     */
    @java.lang.Override
    public device.DeviceOuterClass.DeviceOnlineStatusOrBuilder getOnlineStatusOrBuilder() {
      return onlineStatus_ == null ? device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance() : onlineStatus_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getBase());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getDevice());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getOnlineStatus());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBase());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getDevice());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getOnlineStatus());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof device.DeviceOuterClass.GetDeviceInfoResponse)) {
        return super.equals(obj);
      }
      device.DeviceOuterClass.GetDeviceInfoResponse other = (device.DeviceOuterClass.GetDeviceInfoResponse) obj;

      if (hasBase() != other.hasBase()) return false;
      if (hasBase()) {
        if (!getBase()
            .equals(other.getBase())) return false;
      }
      if (hasDevice() != other.hasDevice()) return false;
      if (hasDevice()) {
        if (!getDevice()
            .equals(other.getDevice())) return false;
      }
      if (hasOnlineStatus() != other.hasOnlineStatus()) return false;
      if (hasOnlineStatus()) {
        if (!getOnlineStatus()
            .equals(other.getOnlineStatus())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBase()) {
        hash = (37 * hash) + BASE_FIELD_NUMBER;
        hash = (53 * hash) + getBase().hashCode();
      }
      if (hasDevice()) {
        hash = (37 * hash) + DEVICE_FIELD_NUMBER;
        hash = (53 * hash) + getDevice().hashCode();
      }
      if (hasOnlineStatus()) {
        hash = (37 * hash) + ONLINE_STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getOnlineStatus().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static device.DeviceOuterClass.GetDeviceInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static device.DeviceOuterClass.GetDeviceInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static device.DeviceOuterClass.GetDeviceInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(device.DeviceOuterClass.GetDeviceInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 获取设备信息和在线状态响应
     * </pre>
     *
     * Protobuf type {@code device.GetDeviceInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:device.GetDeviceInfoResponse)
        device.DeviceOuterClass.GetDeviceInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                device.DeviceOuterClass.GetDeviceInfoResponse.class, device.DeviceOuterClass.GetDeviceInfoResponse.Builder.class);
      }

      // Construct using device.DeviceOuterClass.GetDeviceInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBaseFieldBuilder();
          getDeviceFieldBuilder();
          getOnlineStatusFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        base_ = null;
        if (baseBuilder_ != null) {
          baseBuilder_.dispose();
          baseBuilder_ = null;
        }
        device_ = null;
        if (deviceBuilder_ != null) {
          deviceBuilder_.dispose();
          deviceBuilder_ = null;
        }
        onlineStatus_ = null;
        if (onlineStatusBuilder_ != null) {
          onlineStatusBuilder_.dispose();
          onlineStatusBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return device.DeviceOuterClass.internal_static_device_GetDeviceInfoResponse_descriptor;
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceInfoResponse getDefaultInstanceForType() {
        return device.DeviceOuterClass.GetDeviceInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceInfoResponse build() {
        device.DeviceOuterClass.GetDeviceInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public device.DeviceOuterClass.GetDeviceInfoResponse buildPartial() {
        device.DeviceOuterClass.GetDeviceInfoResponse result = new device.DeviceOuterClass.GetDeviceInfoResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(device.DeviceOuterClass.GetDeviceInfoResponse result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.base_ = baseBuilder_ == null
              ? base_
              : baseBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.device_ = deviceBuilder_ == null
              ? device_
              : deviceBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.onlineStatus_ = onlineStatusBuilder_ == null
              ? onlineStatus_
              : onlineStatusBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof device.DeviceOuterClass.GetDeviceInfoResponse) {
          return mergeFrom((device.DeviceOuterClass.GetDeviceInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(device.DeviceOuterClass.GetDeviceInfoResponse other) {
        if (other == device.DeviceOuterClass.GetDeviceInfoResponse.getDefaultInstance()) return this;
        if (other.hasBase()) {
          mergeBase(other.getBase());
        }
        if (other.hasDevice()) {
          mergeDevice(other.getDevice());
        }
        if (other.hasOnlineStatus()) {
          mergeOnlineStatus(other.getOnlineStatus());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getBaseFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getDeviceFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getOnlineStatusFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private common.Common.BaseResponse base_;
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.BaseResponse, common.Common.BaseResponse.Builder, common.Common.BaseResponseOrBuilder> baseBuilder_;
      /**
       * <code>.common.BaseResponse base = 1;</code>
       * @return Whether the base field is set.
       */
      public boolean hasBase() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       * @return The base.
       */
      public common.Common.BaseResponse getBase() {
        if (baseBuilder_ == null) {
          return base_ == null ? common.Common.BaseResponse.getDefaultInstance() : base_;
        } else {
          return baseBuilder_.getMessage();
        }
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public Builder setBase(common.Common.BaseResponse value) {
        if (baseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          base_ = value;
        } else {
          baseBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public Builder setBase(
          common.Common.BaseResponse.Builder builderForValue) {
        if (baseBuilder_ == null) {
          base_ = builderForValue.build();
        } else {
          baseBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public Builder mergeBase(common.Common.BaseResponse value) {
        if (baseBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            base_ != null &&
            base_ != common.Common.BaseResponse.getDefaultInstance()) {
            getBaseBuilder().mergeFrom(value);
          } else {
            base_ = value;
          }
        } else {
          baseBuilder_.mergeFrom(value);
        }
        if (base_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public Builder clearBase() {
        bitField0_ = (bitField0_ & ~0x00000001);
        base_ = null;
        if (baseBuilder_ != null) {
          baseBuilder_.dispose();
          baseBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public common.Common.BaseResponse.Builder getBaseBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getBaseFieldBuilder().getBuilder();
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      public common.Common.BaseResponseOrBuilder getBaseOrBuilder() {
        if (baseBuilder_ != null) {
          return baseBuilder_.getMessageOrBuilder();
        } else {
          return base_ == null ?
              common.Common.BaseResponse.getDefaultInstance() : base_;
        }
      }
      /**
       * <code>.common.BaseResponse base = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          common.Common.BaseResponse, common.Common.BaseResponse.Builder, common.Common.BaseResponseOrBuilder> 
          getBaseFieldBuilder() {
        if (baseBuilder_ == null) {
          baseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              common.Common.BaseResponse, common.Common.BaseResponse.Builder, common.Common.BaseResponseOrBuilder>(
                  getBase(),
                  getParentForChildren(),
                  isClean());
          base_ = null;
        }
        return baseBuilder_;
      }

      private device.DeviceOuterClass.Device device_;
      private com.google.protobuf.SingleFieldBuilderV3<
          device.DeviceOuterClass.Device, device.DeviceOuterClass.Device.Builder, device.DeviceOuterClass.DeviceOrBuilder> deviceBuilder_;
      /**
       * <code>.device.Device device = 2;</code>
       * @return Whether the device field is set.
       */
      public boolean hasDevice() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.device.Device device = 2;</code>
       * @return The device.
       */
      public device.DeviceOuterClass.Device getDevice() {
        if (deviceBuilder_ == null) {
          return device_ == null ? device.DeviceOuterClass.Device.getDefaultInstance() : device_;
        } else {
          return deviceBuilder_.getMessage();
        }
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public Builder setDevice(device.DeviceOuterClass.Device value) {
        if (deviceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          device_ = value;
        } else {
          deviceBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public Builder setDevice(
          device.DeviceOuterClass.Device.Builder builderForValue) {
        if (deviceBuilder_ == null) {
          device_ = builderForValue.build();
        } else {
          deviceBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public Builder mergeDevice(device.DeviceOuterClass.Device value) {
        if (deviceBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            device_ != null &&
            device_ != device.DeviceOuterClass.Device.getDefaultInstance()) {
            getDeviceBuilder().mergeFrom(value);
          } else {
            device_ = value;
          }
        } else {
          deviceBuilder_.mergeFrom(value);
        }
        if (device_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public Builder clearDevice() {
        bitField0_ = (bitField0_ & ~0x00000002);
        device_ = null;
        if (deviceBuilder_ != null) {
          deviceBuilder_.dispose();
          deviceBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public device.DeviceOuterClass.Device.Builder getDeviceBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getDeviceFieldBuilder().getBuilder();
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      public device.DeviceOuterClass.DeviceOrBuilder getDeviceOrBuilder() {
        if (deviceBuilder_ != null) {
          return deviceBuilder_.getMessageOrBuilder();
        } else {
          return device_ == null ?
              device.DeviceOuterClass.Device.getDefaultInstance() : device_;
        }
      }
      /**
       * <code>.device.Device device = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          device.DeviceOuterClass.Device, device.DeviceOuterClass.Device.Builder, device.DeviceOuterClass.DeviceOrBuilder> 
          getDeviceFieldBuilder() {
        if (deviceBuilder_ == null) {
          deviceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              device.DeviceOuterClass.Device, device.DeviceOuterClass.Device.Builder, device.DeviceOuterClass.DeviceOrBuilder>(
                  getDevice(),
                  getParentForChildren(),
                  isClean());
          device_ = null;
        }
        return deviceBuilder_;
      }

      private device.DeviceOuterClass.DeviceOnlineStatus onlineStatus_;
      private com.google.protobuf.SingleFieldBuilderV3<
          device.DeviceOuterClass.DeviceOnlineStatus, device.DeviceOuterClass.DeviceOnlineStatus.Builder, device.DeviceOuterClass.DeviceOnlineStatusOrBuilder> onlineStatusBuilder_;
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       * @return Whether the onlineStatus field is set.
       */
      public boolean hasOnlineStatus() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       * @return The onlineStatus.
       */
      public device.DeviceOuterClass.DeviceOnlineStatus getOnlineStatus() {
        if (onlineStatusBuilder_ == null) {
          return onlineStatus_ == null ? device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance() : onlineStatus_;
        } else {
          return onlineStatusBuilder_.getMessage();
        }
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public Builder setOnlineStatus(device.DeviceOuterClass.DeviceOnlineStatus value) {
        if (onlineStatusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          onlineStatus_ = value;
        } else {
          onlineStatusBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public Builder setOnlineStatus(
          device.DeviceOuterClass.DeviceOnlineStatus.Builder builderForValue) {
        if (onlineStatusBuilder_ == null) {
          onlineStatus_ = builderForValue.build();
        } else {
          onlineStatusBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public Builder mergeOnlineStatus(device.DeviceOuterClass.DeviceOnlineStatus value) {
        if (onlineStatusBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            onlineStatus_ != null &&
            onlineStatus_ != device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance()) {
            getOnlineStatusBuilder().mergeFrom(value);
          } else {
            onlineStatus_ = value;
          }
        } else {
          onlineStatusBuilder_.mergeFrom(value);
        }
        if (onlineStatus_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public Builder clearOnlineStatus() {
        bitField0_ = (bitField0_ & ~0x00000004);
        onlineStatus_ = null;
        if (onlineStatusBuilder_ != null) {
          onlineStatusBuilder_.dispose();
          onlineStatusBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public device.DeviceOuterClass.DeviceOnlineStatus.Builder getOnlineStatusBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getOnlineStatusFieldBuilder().getBuilder();
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      public device.DeviceOuterClass.DeviceOnlineStatusOrBuilder getOnlineStatusOrBuilder() {
        if (onlineStatusBuilder_ != null) {
          return onlineStatusBuilder_.getMessageOrBuilder();
        } else {
          return onlineStatus_ == null ?
              device.DeviceOuterClass.DeviceOnlineStatus.getDefaultInstance() : onlineStatus_;
        }
      }
      /**
       * <code>.device.DeviceOnlineStatus online_status = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          device.DeviceOuterClass.DeviceOnlineStatus, device.DeviceOuterClass.DeviceOnlineStatus.Builder, device.DeviceOuterClass.DeviceOnlineStatusOrBuilder> 
          getOnlineStatusFieldBuilder() {
        if (onlineStatusBuilder_ == null) {
          onlineStatusBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              device.DeviceOuterClass.DeviceOnlineStatus, device.DeviceOuterClass.DeviceOnlineStatus.Builder, device.DeviceOuterClass.DeviceOnlineStatusOrBuilder>(
                  getOnlineStatus(),
                  getParentForChildren(),
                  isClean());
          onlineStatus_ = null;
        }
        return onlineStatusBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:device.GetDeviceInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:device.GetDeviceInfoResponse)
    private static final device.DeviceOuterClass.GetDeviceInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new device.DeviceOuterClass.GetDeviceInfoResponse();
    }

    public static device.DeviceOuterClass.GetDeviceInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetDeviceInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<GetDeviceInfoResponse>() {
      @java.lang.Override
      public GetDeviceInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetDeviceInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetDeviceInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public device.DeviceOuterClass.GetDeviceInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_device_Device_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_device_Device_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_device_DeviceOnlineStatus_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_device_DeviceOnlineStatus_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_device_GetDeviceByIDRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_device_GetDeviceByIDRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_device_GetDeviceInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_device_GetDeviceInfoResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023device/device.proto\022\006device\032\023common/co" +
      "mmon.proto\"\306\005\n\006Device\022\n\n\002id\030\001 \001(\r\022\021\n\tdev" +
      "ice_id\030\002 \001(\t\022\023\n\013device_name\030\003 \001(\t\022\025\n\rhar" +
      "dware_hash\030\004 \001(\t\022\020\n\010cpu_info\030\005 \001(\t\022\023\n\013me" +
      "mory_info\030\006 \001(\t\022\021\n\tdisk_info\030\007 \001(\t\022\024\n\014ne" +
      "twork_info\030\010 \001(\t\022\020\n\010gpu_info\030\t \001(\t\022\017\n\007os" +
      "_name\030\n \001(\t\022\022\n\nos_version\030\013 \001(\t\022\017\n\007os_ar" +
      "ch\030\014 \001(\t\022\020\n\010hostname\030\r \001(\t\022\020\n\010username\030\016" +
      " \001(\t\022\025\n\ruser_home_dir\030\017 \001(\t\022\020\n\010work_dir\030" +
      "\020 \001(\t\022\023\n\013app_version\030\021 \001(\t\022\024\n\014app_build_" +
      "no\030\022 \001(\t\022\022\n\nip_address\030\023 \001(\t\022\023\n\013mac_addr" +
      "ess\030\024 \001(\t\022\017\n\007user_id\030\025 \001(\r\022(\n\rfirst_seen" +
      "_at\030\026 \001(\0132\021.common.Timestamp\022\'\n\014last_see" +
      "n_at\030\027 \001(\0132\021.common.Timestamp\022)\n\016last_re" +
      "port_at\030\030 \001(\0132\021.common.Timestamp\022%\n\ncrea" +
      "ted_at\030\031 \001(\0132\021.common.Timestamp\022%\n\nupdat" +
      "ed_at\030\032 \001(\0132\021.common.Timestamp\022\016\n\006status" +
      "\030\033 \001(\005\022\021\n\tis_active\030\034 \001(\010\022\024\n\014report_coun" +
      "t\030\035 \001(\003\022\016\n\006remark\030\036 \001(\t\022\022\n\nis_default\030\037 " +
      "\001(\010\"\313\001\n\022DeviceOnlineStatus\022\021\n\tis_online\030" +
      "\001 \001(\010\022)\n\016last_active_at\030\002 \001(\0132\021.common.T" +
      "imestamp\022\024\n\014session_type\030\003 \001(\t\022\022\n\nip_add" +
      "ress\030\004 \001(\t\022\022\n\nuser_agent\030\005 \001(\t\022\023\n\013device" +
      "_name\030\006 \001(\t\022\017\n\007os_info\030\007 \001(\t\022\023\n\013app_vers" +
      "ion\030\010 \001(\t\")\n\024GetDeviceByIDRequest\022\021\n\tdev" +
      "ice_id\030\001 \001(\t\"\216\001\n\025GetDeviceInfoResponse\022\"" +
      "\n\004base\030\001 \001(\0132\024.common.BaseResponse\022\036\n\006de" +
      "vice\030\002 \001(\0132\016.device.Device\0221\n\ronline_sta" +
      "tus\030\003 \001(\0132\032.device.DeviceOnlineStatus2]\n" +
      "\rDeviceService\022L\n\rGetDeviceInfo\022\034.device" +
      ".GetDeviceByIDRequest\032\035.device.GetDevice" +
      "InfoResponseBGZEgithub.com/flipped-auror" +
      "a/gin-vue-admin/server/proto/generated/d" +
      "eviceb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          common.Common.getDescriptor(),
        });
    internal_static_device_Device_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_device_Device_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_device_Device_descriptor,
        new java.lang.String[] { "Id", "DeviceId", "DeviceName", "HardwareHash", "CpuInfo", "MemoryInfo", "DiskInfo", "NetworkInfo", "GpuInfo", "OsName", "OsVersion", "OsArch", "Hostname", "Username", "UserHomeDir", "WorkDir", "AppVersion", "AppBuildNo", "IpAddress", "MacAddress", "UserId", "FirstSeenAt", "LastSeenAt", "LastReportAt", "CreatedAt", "UpdatedAt", "Status", "IsActive", "ReportCount", "Remark", "IsDefault", });
    internal_static_device_DeviceOnlineStatus_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_device_DeviceOnlineStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_device_DeviceOnlineStatus_descriptor,
        new java.lang.String[] { "IsOnline", "LastActiveAt", "SessionType", "IpAddress", "UserAgent", "DeviceName", "OsInfo", "AppVersion", });
    internal_static_device_GetDeviceByIDRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_device_GetDeviceByIDRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_device_GetDeviceByIDRequest_descriptor,
        new java.lang.String[] { "DeviceId", });
    internal_static_device_GetDeviceInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_device_GetDeviceInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_device_GetDeviceInfoResponse_descriptor,
        new java.lang.String[] { "Base", "Device", "OnlineStatus", });
    common.Common.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
