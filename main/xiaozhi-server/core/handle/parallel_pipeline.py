"""
并行管道处理器
实现语音识别→向量检索→大模型→语音合成的并行优化
"""

import asyncio
import time
from typing import Optional, Any, Dict
from dataclasses import dataclass
from config.logger import setup_logging
from core.utils.voice_pipeline_timer import get_voice_timer

logger = setup_logging()


@dataclass
class PipelineResult:
    """管道处理结果"""
    stage: str
    result: Any
    processing_time: float
    success: bool = True
    error: Optional[str] = None


class ParallelPipelineProcessor:
    """并行管道处理器"""
    
    def __init__(self):
        self.performance_stats = {
            "asr_times": [],
            "vector_times": [],
            "intent_times": [],
            "llm_times": [],
            "tts_times": [],
            "total_times": []
        }
    
    async def process_voice_input(self, conn, text: str) -> bool:
        """
        并行处理语音输入
        
        Args:
            conn: 连接对象
            text: 识别到的文本
            
        Returns:
            bool: 处理是否成功
        """
        start_time = time.time()
        logger.info(f"开始并行管道处理: {text}")
        
        # 获取时间统计器
        timer = get_voice_timer(conn.session_id)
        
        try:
            # 阶段1: 并行启动意图分析和向量检索
            timer.start_stage("intent", {"query": text})
            timer.start_stage("vector", {"query": text})
            
            intent_task = asyncio.create_task(self._process_intent(conn, text))
            vector_task = asyncio.create_task(self._process_vector_search(conn, text))
            
            # 阶段2: 等待意图分析和向量检索完成
            intent_result, vector_result = await asyncio.gather(
                intent_task, vector_task, return_exceptions=True
            )
            
            # 处理异常
            if isinstance(intent_result, Exception):
                logger.error(f"意图分析失败: {intent_result}")
                timer.end_stage("intent", "failed", {"error": str(intent_result)})
                intent_handled = False
            else:
                timer.end_stage("intent", "completed" if intent_result.success else "failed", 
                              {"handled": intent_result.result, "processing_time": intent_result.processing_time})
                intent_handled = intent_result.success and intent_result.result
            
            if isinstance(vector_result, Exception):
                logger.error(f"向量检索失败: {vector_result}")
                timer.end_stage("vector", "failed", {"error": str(vector_result)})
                vector_tools = []
            else:
                timer.end_stage("vector", "completed" if vector_result.success else "failed",
                              {"tools_count": len(vector_result.result) if vector_result.result else 0,
                               "processing_time": vector_result.processing_time})
                vector_tools = vector_result.result if vector_result.success else []
            
            # 如果意图已被处理，直接返回
            if intent_handled:
                total_time = time.time() - start_time
                self.performance_stats["total_times"].append(total_time)
                logger.info(f"意图处理完成，总耗时: {total_time:.3f}s")
                return True
            
            # 阶段3: 准备TTS服务（异步预热）
            timer.start_stage("tts_prep", {"action": "warmup"})
            tts_prep_task = asyncio.create_task(self._prepare_tts_service(conn))
            
            # 阶段4: LLM处理
            timer.start_stage("llm", {"query": text, "vector_tools": len(vector_tools)})
            llm_start = time.time()
            llm_success = await self._process_llm(conn, text, vector_tools)
            llm_time = time.time() - llm_start
            self.performance_stats["llm_times"].append(llm_time)
            
            timer.end_stage("llm", "completed" if llm_success else "failed",
                          {"processing_time": llm_time, "success": llm_success})
            
            # 确保TTS服务已准备好
            tts_prep_result = await tts_prep_task
            timer.end_stage("tts_prep", "completed" if tts_prep_result.success else "failed",
                          {"processing_time": tts_prep_result.processing_time})
            
            total_time = time.time() - start_time
            self.performance_stats["total_times"].append(total_time)
            
            logger.info(f"并行管道处理完成，总耗时: {total_time:.3f}s")
            return llm_success
            
        except Exception as e:
            logger.error(f"并行管道处理失败: {e}")
            timer.end_stage("pipeline", "failed", {"error": str(e)})
            return False
    
    async def _process_intent(self, conn, text: str) -> PipelineResult:
        """处理用户意图"""
        start_time = time.time()
        
        try:
            # 导入处理函数
            from core.handle.intentHandler import handle_user_intent
            
            # 执行意图处理
            intent_handled = await handle_user_intent(conn, text)
            
            processing_time = time.time() - start_time
            self.performance_stats["intent_times"].append(processing_time)
            
            return PipelineResult(
                stage="intent",
                result=intent_handled,
                processing_time=processing_time,
                success=True
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"意图处理异常: {e}")
            
            return PipelineResult(
                stage="intent",
                result=False,
                processing_time=processing_time,
                success=False,
                error=str(e)
            )
    
    async def _process_vector_search(self, conn, text: str) -> PipelineResult:
        """处理向量检索"""
        start_time = time.time()
        
        try:
            # 这里应该调用向量检索逻辑
            # 由于向量检索可能在不同模块中，这里提供一个接口
            vector_tools = await self._get_vector_tools(conn, text)
            
            processing_time = time.time() - start_time
            self.performance_stats["vector_times"].append(processing_time)
            
            return PipelineResult(
                stage="vector_search",
                result=vector_tools,
                processing_time=processing_time,
                success=True
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"向量检索异常: {e}")
            
            return PipelineResult(
                stage="vector_search",
                result=[],
                processing_time=processing_time,
                success=False,
                error=str(e)
            )
    
    async def _get_vector_tools(self, conn, text: str) -> list:
        """获取向量检索工具（需要根据实际实现调整）"""
        try:
            # 这里需要根据实际的向量检索实现来调整
            # 假设有一个全局的向量检索服务
            if hasattr(conn, 'vector_retrieval_service'):
                return await conn.vector_retrieval_service.search_tools(text)
            else:
                # 如果没有向量检索服务，返回空列表
                return []
        except Exception as e:
            logger.error(f"向量检索调用失败: {e}")
            return []
    
    async def _prepare_tts_service(self, conn) -> PipelineResult:
        """预热TTS服务 - 优化版本"""
        start_time = time.time()
        
        try:
            # 1. 预建立TTS WebSocket连接
            if hasattr(conn.tts, '_ensure_connection'):
                await conn.tts._ensure_connection()
                logger.debug("TTS WebSocket连接已预热")
            
            # 2. 预发送连接确认请求  
            if hasattr(conn.tts, 'start_connection') and conn.tts.ws:
                await conn.tts.start_connection()
                logger.debug("TTS连接握手已完成")
            
            # 3. 预准备会话状态
            if hasattr(conn.tts, 'session_ready'):
                conn.tts.session_ready = True
                
            processing_time = time.time() - start_time
            logger.info(f"TTS服务预热完成，耗时: {processing_time:.3f}s")
            
            return PipelineResult(
                stage="tts_preparation",
                result=True,
                processing_time=processing_time,
                success=True
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"TTS服务预热失败: {e}")
            
            return PipelineResult(
                stage="tts_preparation",
                result=False,
                processing_time=processing_time,
                success=False,
                error=str(e)
            )
    
    async def _process_llm(self, conn, text: str, vector_tools: list) -> bool:
        """处理大模型请求"""
        try:
            # 导入发送消息函数
            from core.handle.sendAudioHandle import send_stt_message
            
            # 发送STT消息
            await send_stt_message(conn, text)
            
            # 提交给线程池处理LLM
            # 这里使用线程池是因为LLM处理可能是CPU密集型的
            conn.executor.submit(conn.chat, text)
            
            return True
            
        except Exception as e:
            logger.error(f"LLM处理失败: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {}
        
        for stage, times in self.performance_stats.items():
            if times:
                stats[stage] = {
                    "count": len(times),
                    "avg": sum(times) / len(times),
                    "min": min(times),
                    "max": max(times),
                    "total": sum(times)
                }
            else:
                stats[stage] = {
                    "count": 0,
                    "avg": 0,
                    "min": 0,
                    "max": 0,
                    "total": 0
                }
        
        return stats
    
    def reset_stats(self):
        """重置统计数据"""
        for key in self.performance_stats:
            self.performance_stats[key].clear()


# 全局管道处理器实例
pipeline_processor = ParallelPipelineProcessor()