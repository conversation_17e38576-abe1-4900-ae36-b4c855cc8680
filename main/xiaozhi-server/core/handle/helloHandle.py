import time
import json
import random
import asyncio
from core.utils.dialogue import Message
from core.utils.util import audio_to_data
from core.handle.sendAudioHandle import sendAudioMessage, send_stt_message
from core.utils.util import remove_punctuation_and_length, opus_datas_to_wav_bytes
from core.providers.tts.dto.dto import ContentType, SentenceType
from core.providers.tools.device_mcp import (
    MCPClient,
    send_mcp_initialize_message,
    send_mcp_tools_list_request,
)
from core.utils.wakeup_word import WakeupWordsConfig

TAG = __name__

WAKEUP_CONFIG = {
    "refresh_time": 5,
    "words": ["你好", "你好啊", "嘿，你好", "嗨"],
}

# 创建全局的唤醒词配置管理器
wakeup_words_config = WakeupWordsConfig()

# 用于防止并发调用wakeupWordsResponse的锁
_wakeup_response_lock = asyncio.Lock()


async def handleHelloMessage(conn, msg_json):
    """处理hello消息"""
    audio_params = msg_json.get("audio_params")
    if audio_params:
        format = audio_params.get("format")
        conn.logger.bind(tag=TAG).info(f"客户端音频格式: {format}")
        conn.logger.bind(tag=TAG).info(f"完整音频参数: {audio_params}")
        conn.audio_format = format
        conn.welcome_msg["audio_params"] = audio_params
    else:
        conn.logger.bind(tag=TAG).warning("客户端未提供音频参数，使用默认opus格式")
        conn.audio_format = "opus"
    features = msg_json.get("features")
    if features:
        conn.logger.bind(tag=TAG).info(f"客户端特性: {features}")
        conn.features = features
        if features.get("mcp"):
            conn.logger.bind(tag=TAG).info("客户端支持MCP")
            conn.mcp_client = MCPClient()
            # 发送初始化
            asyncio.create_task(send_mcp_initialize_message(conn))
            # 发送mcp消息，获取tools列表
            asyncio.create_task(send_mcp_tools_list_request(conn))

    await conn.websocket.send(json.dumps(conn.welcome_msg))

    # 新增: 如果是 rapido 连接，跳过初始问候
    if getattr(conn, "is_text", False):
        return

    # 连接建立后异步等待TTS初始化完成再发送固定音频和文本
    asyncio.create_task(send_initial_greeting_after_tts_ready(conn))





async def send_initial_greeting_after_tts_ready(conn):
    """等待TTS准备好后发送初始问候（使用与唤醒词相同的方式）"""
    try:
        # 检查是否已经处理过唤醒词，如果是则跳过初始问候
        if hasattr(conn, 'just_woken_up') and conn.just_woken_up:
            conn.logger.bind(tag=TAG).info("检测到已处理过唤醒词，跳过初始问候")
            return

        # 等待TTS初始化完成（最多等待15秒）
        max_wait_time = 15.0
        wait_interval = 0.2
        waited_time = 0.0

        conn.logger.bind(tag=TAG).info("等待TTS初始化完成后发送初始问候...")

        while waited_time < max_wait_time:
            # 检查TTS是否已初始化
            if (hasattr(conn, 'tts') and conn.tts is not None and
                hasattr(conn.tts, 'tts_priority_thread') and
                conn.tts.tts_priority_thread is not None and
                conn.tts.tts_priority_thread.is_alive()):
                conn.logger.bind(tag=TAG).info(f"TTS初始化完成，等待了 {waited_time:.1f} 秒，开始发送初始问候")
                break
            await asyncio.sleep(wait_interval)
            waited_time += wait_interval

        # 如果TTS仍未初始化，跳过
        if not hasattr(conn, 'tts') or conn.tts is None:
            conn.logger.bind(tag=TAG).warning("TTS未初始化，跳过初始问候音频发送")
            return

        # 额外等待一小段时间确保TTS完全就绪
        await asyncio.sleep(0.5)

        # 再次检查是否已经处理过唤醒词（在等待期间可能已经触发）
        if hasattr(conn, 'just_woken_up') and conn.just_woken_up:
            conn.logger.bind(tag=TAG).info("TTS初始化期间已处理过唤醒词，跳过初始问候")
            return

        # 获取唤醒词回复配置（与checkWakeupWords完全相同的逻辑）
        response = wakeup_words_config.get_wakeup_response("default")
        if not response or not response.get("file_path"):
            response = {
                "voice": "default",
                "file_path": "config/assets/哆啦A梦.wav",
                "time": 0,
                "text": "我在",
            }

        # 播放初始问候音频（与checkWakeupWords完全相同的方式）
        conn.client_abort = False

        # 根据客户端音频格式决定是否使用Opus编码
        is_opus = getattr(conn, 'audio_format', 'opus') == 'opus'
        audio_packets, _ = audio_to_data(response.get("file_path"), is_opus=is_opus)

        conn.logger.bind(tag=TAG).info(f"TTS就绪后播放初始问候: {response.get('text')}, 音频格式: {getattr(conn, 'audio_format', 'opus')}")
        conn.logger.bind(tag=TAG).info(f"音频包数量: {len(audio_packets)}, 前几个包大小: {[len(p) for p in audio_packets[:3]]}")

        # 发送完整的TTS消息序列（与正常TTS流程一致）
        from core.handle.sendAudioHandle import send_tts_message

        # 1. 设置客户端状态并发送start消息
        conn.client_is_speaking = True
        await send_tts_message(conn, "start")

        # 2. 使用与唤醒词完全相同的发送方式
        await sendAudioMessage(conn, SentenceType.FIRST, audio_packets, response.get("text"))
        await sendAudioMessage(conn, SentenceType.LAST, [], None)

        conn.logger.bind(tag=TAG).info("初始问候音频发送完成")

        # 补充对话
        if hasattr(conn, 'dialogue') and conn.dialogue:
            conn.dialogue.put(Message(role="assistant", content=response.get("text")))

    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"TTS就绪后发送初始问候失败: {e}")
        import traceback
        conn.logger.bind(tag=TAG).error(f"详细错误信息: {traceback.format_exc()}")





async def checkWakeupWords(conn, text):
    enable_wakeup_words_response_cache = conn.config[
        "enable_wakeup_words_response_cache"
    ]

    if not enable_wakeup_words_response_cache or not conn.tts:
        return False

    _, filtered_text = remove_punctuation_and_length(text)
    if filtered_text not in conn.config.get("wakeup_words"):
        return False

    conn.just_woken_up = True
    await send_stt_message(conn, text)

    # 获取当前音色
    voice = getattr(conn.tts, "voice", "default")
    if not voice:
        voice = "default"

    # 获取唤醒词回复配置
    response = wakeup_words_config.get_wakeup_response(voice)
    if not response or not response.get("file_path"):
        response = {
            "voice": "default",
            "file_path": "config/assets/哆啦A梦.wav",
            "time": 0,
            "text": "我在",
        }

    # 播放唤醒词回复
    conn.client_abort = False

    # 根据客户端音频格式决定是否使用Opus编码
    is_opus = getattr(conn, 'audio_format', 'opus') == 'opus'
    audio_packets, _ = audio_to_data(response.get("file_path"), is_opus=is_opus)

    conn.logger.bind(tag=TAG).info(f"播放唤醒词回复: {response.get('text')}, 音频格式: {getattr(conn, 'audio_format', 'opus')}")
    await sendAudioMessage(conn, SentenceType.FIRST, audio_packets, response.get("text"))
    await sendAudioMessage(conn, SentenceType.LAST, [], None)

    # 补充对话
    conn.dialogue.put(Message(role="assistant", content=response.get("text")))

    # 检查是否需要更新唤醒词回复（暂时禁用以避免不必要的LLM调用）
    # if time.time() - response.get("time", 0) > WAKEUP_CONFIG["refresh_time"]:
    #     if not _wakeup_response_lock.locked():
    #         asyncio.create_task(wakeupWordsResponse(conn))
    return True


async def wakeupWordsResponse(conn):
    if not conn.tts or not conn.llm or not conn.llm.response_no_stream:
        return

    try:
        # 尝试获取锁，如果获取不到就返回
        if not await _wakeup_response_lock.acquire():
            return

        # 生成唤醒词回复
        wakeup_word = random.choice(WAKEUP_CONFIG["words"])
        question = (
            "此刻用户正在和你说```"
            + wakeup_word
            + "```。\n请你根据以上用户的内容进行20-30字回复。要符合系统设置的角色情感和态度，不要像机器人一样说话。\n"
            + "请勿对这条内容本身进行任何解释和回应，请勿返回表情符号，仅返回对用户的内容的回复。"
        )

        result = conn.llm.response_no_stream(conn.config["prompt"], question)
        if not result or len(result) == 0:
            return

        # 生成TTS音频
        tts_result = await asyncio.to_thread(conn.tts.to_tts, result)
        if not tts_result:
            return

        # 获取当前音色
        voice = getattr(conn.tts, "voice", "default")

        wav_bytes = opus_datas_to_wav_bytes(tts_result, sample_rate=16000)
        file_path = wakeup_words_config.generate_file_path(voice)
        with open(file_path, "wb") as f:
            f.write(wav_bytes)
        # 更新配置
        wakeup_words_config.update_wakeup_response(voice, file_path, result)
    finally:
        # 确保在任何情况下都释放锁
        if _wakeup_response_lock.locked():
            _wakeup_response_lock.release()
