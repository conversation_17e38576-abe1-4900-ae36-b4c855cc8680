"""
豆包ASR连接池管理器
优化WebSocket连接复用，减少建立连接的开销
"""

import asyncio
import uuid
import json
import gzip
import time
import logging
import websockets
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class ASRConnection:
    """ASR连接数据结构"""
    websocket: websockets.WebSocketServerProtocol
    connection_id: str
    created_at: float
    last_used: float
    in_use: bool = False
    is_healthy: bool = True


class ASRConnectionPool:
    """ASR连接池管理器"""
    
    def __init__(self, config: Dict[str, Any], max_connections: int = 3):
        """
        初始化连接池
        
        Args:
            config: ASR配置
            max_connections: 最大连接数
        """
        self.config = config
        self.max_connections = max_connections
        self.connections: deque[ASRConnection] = deque()
        self.lock = asyncio.Lock()
        
        # 连接配置
        self.appid = str(config.get("appid"))
        self.cluster = config.get("cluster")
        self.access_token = config.get("access_token")
        self.boosting_table_name = config.get("boosting_table_name", "")
        self.correct_table_name = config.get("correct_table_name", "")
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"
        
        # 连接池配置
        self.connection_timeout = 30  # 连接超时时间
        self.idle_timeout = 60  # 空闲超时时间
        self.health_check_interval = 10  # 健康检查间隔
        
        # 启动清理任务
        self._cleanup_task = None
        
    async def start(self):
        """启动连接池"""
        # 预创建连接
        await self._preconnect()
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_connections())
        logger.info(f"ASR连接池已启动，最大连接数: {self.max_connections}")
    
    async def stop(self):
        """停止连接池"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        async with self.lock:
            while self.connections:
                conn = self.connections.popleft()
                try:
                    await conn.websocket.close()
                except Exception as e:
                    logger.warning(f"关闭连接时出错: {e}")
        
        logger.info("ASR连接池已停止")
    
    async def get_connection(self) -> Optional[ASRConnection]:
        """获取可用连接"""
        async with self.lock:
            # 查找空闲且健康的连接
            for i, conn in enumerate(self.connections):
                if not conn.in_use and conn.is_healthy:
                    # 检查连接是否过期
                    if time.time() - conn.last_used < self.idle_timeout:
                        conn.in_use = True
                        conn.last_used = time.time()
                        # 移动到队列末尾（LRU）
                        self.connections.remove(conn)
                        self.connections.append(conn)
                        logger.debug(f"复用ASR连接: {conn.connection_id}")
                        return conn
                    else:
                        # 连接过期，标记为不健康
                        conn.is_healthy = False
            
            # 如果没有可用连接且未达到最大数量，创建新连接
            if len([c for c in self.connections if c.is_healthy]) < self.max_connections:
                new_conn = await self._create_connection()
                if new_conn:
                    new_conn.in_use = True
                    self.connections.append(new_conn)
                    logger.info(f"创建新ASR连接: {new_conn.connection_id}")
                    return new_conn
            
            logger.warning("无可用ASR连接")
            return None
    
    async def return_connection(self, conn: ASRConnection):
        """归还连接"""
        async with self.lock:
            conn.in_use = False
            conn.last_used = time.time()
            logger.debug(f"归还ASR连接: {conn.connection_id}")
    
    async def mark_unhealthy(self, conn: ASRConnection):
        """标记连接为不健康"""
        async with self.lock:
            conn.is_healthy = False
            conn.in_use = False
            logger.warning(f"标记ASR连接为不健康: {conn.connection_id}")
    
    async def _preconnect(self):
        """预创建连接"""
        for i in range(min(2, self.max_connections)):  # 预创建2个连接
            try:
                conn = await self._create_connection()
                if conn:
                    self.connections.append(conn)
                    logger.info(f"预创建ASR连接: {conn.connection_id}")
            except Exception as e:
                logger.error(f"预创建连接失败: {e}")
    
    async def _create_connection(self) -> Optional[ASRConnection]:
        """创建新连接"""
        try:
            # 构建认证头
            headers = {
                "X-Api-App-Key": self.appid,
                "X-Api-Access-Key": self.access_token,
                "X-Api-Resource-Id": "volc.bigasr.sauc.duration",
                "X-Api-Connect-Id": str(uuid.uuid4()),
            }
            
            # 建立WebSocket连接
            websocket = await websockets.connect(
                self.ws_url,
                additional_headers=headers,
                max_size=1000000000,
                ping_interval=None,
                ping_timeout=None,
                close_timeout=10,
            )
            
            # 发送初始化请求
            await self._initialize_connection(websocket)
            
            connection_id = str(uuid.uuid4())
            current_time = time.time()
            
            return ASRConnection(
                websocket=websocket,
                connection_id=connection_id,
                created_at=current_time,
                last_used=current_time,
                in_use=False,
                is_healthy=True
            )
            
        except Exception as e:
            logger.error(f"创建ASR连接失败: {e}")
            return None
    
    async def _initialize_connection(self, websocket):
        """初始化连接"""
        request_params = {
            "app": {
                "appid": self.appid,
                "cluster": self.cluster,
                "token": self.access_token,
            },
            "user": {"uid": "streaming_asr_service"},
            "request": {
                "reqid": str(uuid.uuid4()),
                "workflow": "audio_in,resample,partition,vad,fe,decode,itn,nlu_punctuate",
                "show_utterances": True,
                "result_type": "single",
                "sequence": 1,
                "boosting_table_name": self.boosting_table_name,
                "correct_table_name": self.correct_table_name,
                "end_window_size": 200,
            },
            "audio": {
                "format": "pcm",
                "codec": "pcm",
                "rate": 16000,
                "language": "zh-CN",
                "bits": 16,
                "channel": 1,
                "sample_rate": 16000,
            },
        }
        
        # 构建请求数据
        payload_bytes = str.encode(json.dumps(request_params))
        payload_bytes = gzip.compress(payload_bytes)
        header = self._generate_header()
        header.extend((len(payload_bytes)).to_bytes(4, "big"))
        header.extend(payload_bytes)
        
        # 发送初始化请求
        await websocket.send(header)
        
        # 等待初始化响应
        response = await websocket.recv()
        result = self._parse_response(response)
        
        if "code" in result and result["code"] != 1000:
            raise Exception(f"ASR初始化失败: {result}")
    
    def _generate_header(self, version=0x01, message_type=0x01, 
                        message_type_specific_flags=0x00, serial_method=0x01,
                        compression_type=0x01, reserved_data=0x00,
                        extension_header: bytes = b""):
        """生成协议头"""
        header = bytearray()
        header_size = int(len(extension_header) / 4) + 1
        header.append((version << 4) | header_size)
        header.append((message_type << 4) | message_type_specific_flags)
        header.append((serial_method << 4) | compression_type)
        header.append(reserved_data)
        header.extend(extension_header)
        return header
    
    def _parse_response(self, res: bytes) -> dict:
        """解析响应"""
        try:
            if len(res) < 4:
                return {"error": "响应数据长度不足"}
            
            header = res[:4]
            message_type = header[1] >> 4
            
            if message_type == 0x0F:  # ERROR_RESPONSE
                code = int.from_bytes(res[4:8], "big", signed=False)
                msg_length = int.from_bytes(res[8:12], "big", signed=False)
                error_msg = json.loads(res[12:].decode("utf-8"))
                return {"code": code, "msg_length": msg_length, "payload_msg": error_msg}
            
            try:
                json_data = res[12:].decode("utf-8")
                result = json.loads(json_data)
                return {"payload_msg": result}
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                raise Exception(f"JSON解析失败: {e}")
                
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            raise
    
    async def _cleanup_connections(self):
        """清理过期和不健康的连接"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                async with self.lock:
                    current_time = time.time()
                    connections_to_remove = []
                    
                    for conn in self.connections:
                        # 检查连接是否过期或不健康
                        if (not conn.is_healthy or 
                            (not conn.in_use and current_time - conn.last_used > self.idle_timeout)):
                            connections_to_remove.append(conn)
                    
                    # 移除过期连接
                    for conn in connections_to_remove:
                        self.connections.remove(conn)
                        try:
                            await conn.websocket.close()
                            logger.info(f"清理ASR连接: {conn.connection_id}")
                        except Exception as e:
                            logger.warning(f"关闭连接时出错: {e}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理连接时出错: {e}")
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        healthy_count = sum(1 for c in self.connections if c.is_healthy)
        in_use_count = sum(1 for c in self.connections if c.in_use)
        
        return {
            "total_connections": len(self.connections),
            "healthy_connections": healthy_count,
            "in_use_connections": in_use_count,
            "available_connections": healthy_count - in_use_count,
            "max_connections": self.max_connections
        }