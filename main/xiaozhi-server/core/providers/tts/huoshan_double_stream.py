import os
import uuid
import json
import queue
import asyncio
import traceback
import websockets
from websockets.protocol import State
from core.utils.tts import MarkdownCleaner
from config.logger import setup_logging
from core.utils import opus_encoder_utils
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from core.providers.tts.dto.dto import SentenceType, ContentType, InterfaceType
from asyncio import Task


TAG = __name__
logger = setup_logging()

PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type:
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# Message Type Specific Flags
MsgTypeFlagNoSeq = 0b0000  # Non-terminal packet with no sequence
MsgTypeFlagPositiveSeq = 0b1  # Non-terminal packet with sequence > 0
MsgTypeFlagLastNoSeq = 0b10  # last packet with no sequence
MsgTypeFlagNegativeSeq = 0b11  # Payload contains event number (int32)
MsgTypeFlagWithEvent = 0b100
# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001
# Message Compression
COMPRESSION_NO = 0b0000
COMPRESSION_GZIP = 0b0001

EVENT_NONE = 0
EVENT_Start_Connection = 1

EVENT_FinishConnection = 2

EVENT_ConnectionStarted = 50  # 成功建连

EVENT_ConnectionFailed = 51  # 建连失败（可能是无法通过权限认证）

EVENT_ConnectionFinished = 52  # 连接结束

# 上行Session事件
EVENT_StartSession = 100
EVENT_CancelSession = 101
EVENT_FinishSession = 102
# 下行Session事件
EVENT_SessionStarted = 150
EVENT_SessionCanceled = 151
EVENT_SessionFinished = 152

EVENT_SessionFailed = 153

# 上行通用事件
EVENT_TaskRequest = 200

# 下行TTS事件
EVENT_TTSSentenceStart = 350

EVENT_TTSSentenceEnd = 351

EVENT_TTSResponse = 352


class Header:
    def __init__(
        self,
        protocol_version=PROTOCOL_VERSION,
        header_size=DEFAULT_HEADER_SIZE,
        message_type: int = 0,
        message_type_specific_flags: int = 0,
        serial_method: int = NO_SERIALIZATION,
        compression_type: int = COMPRESSION_NO,
        reserved_data=0,
    ):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        return bytes(
            [
                (self.protocol_version << 4) | self.header_size,
                (self.message_type << 4) | self.message_type_specific_flags,
                (self.serial_method << 4) | self.compression_type,
                self.reserved_data,
            ]
        )


class Optional:
    def __init__(
        self, event: int = EVENT_NONE, sessionId: str = None, sequence: int = None
    ):
        self.event = event
        self.sessionId = sessionId
        self.errorCode: int = 0
        self.connectionId: str | None = None
        self.response_meta_json: str | None = None
        self.sequence = sequence

    # 转成 byte 序列
    def as_bytes(self) -> bytes:
        option_bytes = bytearray()
        if self.event != EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.sessionId is not None:
            session_id_bytes = str.encode(self.sessionId)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class Response:
    def __init__(self, header: Header, optional: Optional):
        self.optional = optional
        self.header = header
        self.payload: bytes | None = None

    def __str__(self):
        return super().__str__()


class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.ws = None
        self.interface_type = InterfaceType.DUAL_STREAM
        self._monitor_task = None  # 监听任务引用
        self.appId = config.get("appid")
        self.access_token = config.get("access_token")
        self.voice = "zh_male_aojiaobazong_moon_bigtts"
        # 其他配置保持从config读取
        self.cluster = config.get("cluster")
        self.resource_id = config.get("resource_id")
        self.ws_url = config.get("ws_url")
        self.authorization = config.get("authorization")
        self.header = {"Authorization": f"{self.authorization}{self.access_token}"}
        self.enable_two_way = True
        self.tts_text = ""
        self.clone_speaker_id = config.get("private_voice")

        if self.clone_speaker_id=="S_Plr6OXbx1":
            self.use_clone_speaker = True  # 启用声音复刻
            logger.bind(tag=TAG).info(f"启用声音复刻，复刻发音人ID: {self.clone_speaker_id}")
        else:
            self.use_clone_speaker = False
            # 预置发音人从speaker或voice_id字段获取
            self.voice = config.get("private_voice")
            logger.bind(tag=TAG).info(f"使用预置发音人: {self.voice}")

            # 音频后处理参数：音调、语速、音量
        self.post_process = {
            "pitch": 0,      # 音调调整 (-12到12)
            "speed": 1.0,    # 语速调整 (0.5到2.0)
            "volume": 0.6    # 音量调整 (0.0到1.0) - 设置为60%
        }
        self.opus_encoder = opus_encoder_utils.OpusEncoderUtils(
            sample_rate=16000, channels=1, frame_size_ms=60
        )
        model_key_msg = check_model_key("TTS", self.access_token)
        if model_key_msg:
            logger.bind(tag=TAG).error(model_key_msg)

    def set_volume(self, volume_percent):
        """
        设置TTS音量
        Args:
            volume_percent: 音量百分比 (0-100)
        """
        # 将百分比转换为0.0-1.0的范围
        volume = max(0.0, min(1.0, volume_percent / 100.0))
        self.post_process["volume"] = volume
        logger.bind(tag=TAG).info(f"TTS音量已调整为: {volume_percent}% (内部值: {volume})")

    def set_speed(self, speed_factor):
        """
        设置TTS语速
        Args:
            speed_factor: 语速倍数 (0.5-2.0)
        """
        speed = max(0.5, min(2.0, speed_factor))
        self.post_process["speed"] = speed
        logger.bind(tag=TAG).info(f"TTS语速已调整为: {speed}倍")

    def set_pitch(self, pitch_value):
        """
        设置TTS音调
        Args:
            pitch_value: 音调值 (-12到12)
        """
        pitch = max(-12, min(12, pitch_value))
        self.post_process["pitch"] = pitch
        logger.bind(tag=TAG).info(f"TTS音调已调整为: {pitch}")

    async def open_audio_channels(self, conn):
        try:
            logger.bind(tag=TAG).info("开始打开火山引擎TTS音频通道")
            await super().open_audio_channels(conn)
            logger.bind(tag=TAG).info("火山引擎TTS音频通道打开成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"打开火山引擎TTS音频通道失败: {str(e)}")
            self.ws = None
            raise

    async def _ensure_connection(self):
        """建立新的WebSocket连接"""
        max_retries = 3
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                # 检查现有连接是否可用
                if self.ws and self.ws.state != State.CLOSED:
                    logger.bind(tag=TAG).info(f"使用已有连接...")
                    return self.ws
                    
                logger.bind(tag=TAG).info(f"开始建立新连接... (尝试 {attempt + 1}/{max_retries})")

                # 根据是否使用复刻发音人选择Resource ID
                resource_id = self.resource_id
                if self.use_clone_speaker:
                    resource_id = 'volc.megatts.default'  # 复刻发音人
                    logger.bind(tag=TAG).info(f"使用复刻发音人，Resource ID: {resource_id}")
                else:
                    logger.bind(tag=TAG).info(f"使用预置发音人，Resource ID: {resource_id}")

                ws_header = {
                    "X-Api-App-Key": self.appId,
                    "X-Api-Access-Key": self.access_token,
                    "X-Api-Resource-Id": resource_id,
                    "X-Api-Connect-Id": str(uuid.uuid4()),
                }

                # 添加连接超时和ping设置
                self.ws = await websockets.connect(
                    self.ws_url,
                    additional_headers=ws_header,
                    max_size=1000000000,
                    ping_interval=30,  # 30秒ping间隔
                    ping_timeout=10,   # 10秒ping超时
                    close_timeout=10   # 10秒关闭超时
                )
                logger.bind(tag=TAG).info("WebSocket连接建立成功")
                return self.ws

            except Exception as e:
                logger.bind(tag=TAG).error(f"建立连接失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                self.ws = None

                if attempt < max_retries - 1:
                    logger.bind(tag=TAG).info(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    raise

    def tts_text_priority_thread(self):
        """火山引擎双流式TTS的文本处理线程"""
        try:
            logger.bind(tag=TAG).info("火山引擎TTS文本处理线程已启动")
            logger.bind(tag=TAG).info(f"TTS队列对象: {self.tts_text_queue}")
            logger.bind(tag=TAG).info(f"连接对象: {self.conn}")
            logger.bind(tag=TAG).info(f"停止事件状态: {self.conn.stop_event.is_set()}")
        except Exception as e:
            logger.bind(tag=TAG).error(f"TTS线程启动时发生错误: {e}")
            return

        while not self.conn.stop_event.is_set():
            try:
                message = self.tts_text_queue.get(timeout=1)
                logger.bind(tag=TAG).info(
                    f"收到TTS任务｜{message.sentence_type.name} ｜ {message.content_type.name} | 会话ID: {self.conn.sentence_id}"
                )

                if message.sentence_type == SentenceType.FIRST:
                    self.conn.client_abort = False

                if self.conn.client_abort:
                    try:
                        logger.bind(tag=TAG).info("收到打断信息，终止TTS文本处理线程")
                        asyncio.run_coroutine_threadsafe(
                            self.cancel_session(self.conn.sentence_id),
                            loop=self.conn.loop,
                        )
                        continue
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"取消TTS会话失败: {str(e)}")
                        continue

                if message.sentence_type == SentenceType.FIRST:
                    # 初始化参数
                    try:
                        if not getattr(self.conn, "sentence_id", None):
                            self.conn.sentence_id = uuid.uuid4().hex
                            logger.bind(tag=TAG).info(f"自动生成新的 会话ID: {self.conn.sentence_id}")

                        logger.bind(tag=TAG).info("开始启动TTS会话...")
                        future = asyncio.run_coroutine_threadsafe(
                            self.start_session(self.conn.sentence_id),
                            loop=self.conn.loop,
                        )
                        future.result()
                        self.tts_audio_first_sentence = True
                        self.before_stop_play_files.clear()
                        logger.bind(tag=TAG).info("TTS会话启动成功")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"启动TTS会话失败: {str(e)}")
                        continue

                elif ContentType.TEXT == message.content_type:
                    if message.content_detail:
                        try:
                            logger.bind(tag=TAG).debug(
                                f"开始发送TTS文本: {message.content_detail}"
                            )
                            future = asyncio.run_coroutine_threadsafe(
                                self.text_to_speak(message.content_detail, None),
                                loop=self.conn.loop,
                            )
                            future.result()
                            logger.bind(tag=TAG).debug("TTS文本发送成功")
                        except Exception as e:
                            logger.bind(tag=TAG).error(f"发送TTS文本失败: {str(e)}")
                            # 如果是连接相关错误，清理连接状态
                            if "websocket" in str(e).lower() or "connection" in str(e).lower():
                                logger.bind(tag=TAG).warning("检测到连接相关错误，清理连接状态")
                                self.ws = None
                            continue

                elif ContentType.FILE == message.content_type:
                    logger.bind(tag=TAG).info(
                        f"添加音频文件到待播放列表: {message.content_file}"
                    )
                    if message.content_file and os.path.exists(message.content_file):
                        # 直接添加文件路径，在播放时处理
                        self.before_stop_play_files.append(
                            (message.content_file, message.content_detail)
                        )

                if message.sentence_type == SentenceType.LAST:
                    try:
                        logger.bind(tag=TAG).info("开始结束TTS会话...")
                        future = asyncio.run_coroutine_threadsafe(
                            self.finish_session(self.conn.sentence_id),
                            loop=self.conn.loop,
                        )
                        future.result()
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"结束TTS会话失败: {str(e)}")
                        continue

            except queue.Empty:
                continue
            except Exception as e:
                logger.bind(tag=TAG).error(
                    f"处理TTS文本失败: {str(e)}, 类型: {type(e).__name__}, 堆栈: {traceback.format_exc()}"
                )
                continue

        logger.bind(tag=TAG).info("火山引擎TTS文本处理线程已退出")

    async def text_to_speak(self, text, _):
        """发送文本到TTS服务"""
        try:
            # 检查WebSocket连接状态，如果连接失败则尝试重新建立
            if self.ws is None or self.ws.state == State.CLOSED:
                logger.bind(tag=TAG).warning(f"WebSocket连接不存在或已关闭，尝试重新建立连接")
                try:
                    await self._ensure_connection()
                    if self.ws is None or self.ws.state == State.CLOSED:
                        logger.bind(tag=TAG).error("重新建立连接失败，终止发送文本")
                        return
                    logger.bind(tag=TAG).info("WebSocket连接重新建立成功")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"重新建立连接失败: {str(e)}")
                    return

            #  过滤Markdown
            filtered_text = MarkdownCleaner.clean_markdown(text)

            if not filtered_text.strip():
                logger.bind(tag=TAG).debug("过滤后的文本为空，跳过发送")
                return

            # 发送文本
            logger.bind(tag=TAG).debug(f"发送TTS文本: {filtered_text[:50]}...")
            logger.bind(tag=TAG).debug(f"当前发音人配置: voice={self.voice}, use_clone_speaker={self.use_clone_speaker}, clone_speaker_id={self.clone_speaker_id}")
            await self.send_text(self.voice, filtered_text, self.conn.sentence_id)
            return
        except websockets.ConnectionClosed:
            logger.bind(tag=TAG).warning("WebSocket连接已关闭，无法发送文本")
            self.ws = None
        except Exception as e:
            logger.bind(tag=TAG).error(f"发送TTS文本失败: {str(e)}")
            if self.ws:
                try:
                    await self.ws.close()
                except:
                    pass
                self.ws = None
            raise

    async def start_session(self, session_id):
        logger.bind(tag=TAG).info(f"开始会话～～{session_id}")
        try:
            # 会话开始时检测上个会话的监听状态
            if (
                self._monitor_task is not None
                and isinstance(self._monitor_task, Task)
                and not self._monitor_task.done()
            ):
                logger.bind(tag=TAG).info("检测到未完成的上个会话，关闭监听任务和连接...")
                await self.close()

            # 建立新连接
            await self._ensure_connection()

            # 先发送start_connection请求
            await self.start_connection()

            # 等待连接确认响应
            try:
                msg = await asyncio.wait_for(self.ws.recv(), timeout=5)
                res = self.parser_response(msg)
                self.print_response(res, "start_connection response:")

                if res.optional.event != EVENT_ConnectionStarted:
                    error_msg = f"连接建立失败，事件码: {res.optional.event}"
                    if hasattr(res.optional, 'response_meta_json') and res.optional.response_meta_json:
                        error_msg += f", 错误信息: {res.optional.response_meta_json}"
                    if hasattr(res, 'payload') and res.payload:
                        try:
                            payload_str = res.payload.decode('utf-8')
                            error_msg += f", 响应内容: {payload_str}"
                        except:
                            pass
                    logger.bind(tag=TAG).error(error_msg)
                    raise RuntimeError(error_msg)

                logger.bind(tag=TAG).info("WebSocket连接确认成功")
            except asyncio.TimeoutError:
                logger.bind(tag=TAG).error("等待连接确认超时")
                raise RuntimeError("WebSocket连接确认超时")

            # 启动监听任务
            self._monitor_task = asyncio.create_task(self._start_monitor_tts_response())

            # 发送会话启动请求
            header = Header(
                message_type=FULL_CLIENT_REQUEST,
                message_type_specific_flags=MsgTypeFlagWithEvent,
                serial_method=JSON,
            ).as_bytes()
            optional = Optional(
                event=EVENT_StartSession, sessionId=session_id
            ).as_bytes()
            payload = self.get_payload_bytes(
                event=EVENT_StartSession, speaker=self.voice
            )
            await self.send_event(self.ws, header, optional, payload)
            logger.bind(tag=TAG).info("会话启动请求已发送")

            # 等待会话启动确认
            try:
                # 给监听任务一点时间来处理会话启动响应
                await asyncio.sleep(0.2)
                logger.bind(tag=TAG).info("TTS会话启动成功")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"等待会话启动确认异常: {e}")

        except Exception as e:
            logger.bind(tag=TAG).error(f"启动会话失败: {str(e)}")
            # 确保清理资源
            await self.close()
            if hasattr(self, "_monitor_task") and self._monitor_task:
                try:
                    self._monitor_task.cancel()
                    await self._monitor_task
                except:
                    pass
                self._monitor_task = None
            if self.ws:
                try:
                    await self.ws.close()
                except:
                    pass
                self.ws = None
            raise

    async def finish_session(self, session_id):
        logger.bind(tag=TAG).info(f"关闭会话～～{session_id}")
        try:
            if self.ws:
                header = Header(
                    message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON,
                ).as_bytes()
                optional = Optional(
                    event=EVENT_FinishSession, sessionId=session_id
                ).as_bytes()
                payload = str.encode("{}")
                await self.send_event(self.ws, header, optional, payload)
                logger.bind(tag=TAG).info("会话结束请求已发送")

                # 等待监听任务完成
                if self._monitor_task:
                    try:
                        await self._monitor_task
                    except Exception as e:
                        logger.bind(tag=TAG).error(
                            f"等待监听任务完成时发生错误: {str(e)}"
                        )
                    finally:
                        self._monitor_task = None

        except Exception as e:
            logger.bind(tag=TAG).error(f"关闭会话失败: {str(e)}")
            # 确保清理资源
            await self.close()
            raise

    async def cancel_session(self,session_id):
        logger.bind(tag=TAG).info(f"取消会话，释放服务端资源～～{session_id}")
        try:
            if self.ws:
                header = Header(
                    message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON,
                ).as_bytes()
                optional = Optional(
                    event=EVENT_CancelSession, sessionId=session_id
                ).as_bytes()
                payload = str.encode("{}")
                await self.send_event(self.ws, header, optional, payload)
                logger.bind(tag=TAG).info("会话取消请求已发送")
        except Exception as e:
            logger.bind(tag=TAG).error(f"取消会话失败: {str(e)}")
            # 确保清理资源
            await self.close()
            raise

    async def close(self):
        """资源清理方法"""
        # 取消监听任务
        if self._monitor_task:
            try:
                self._monitor_task.cancel()
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.bind(tag=TAG).warning(f"关闭时取消监听任务错误: {e}")
            self._monitor_task = None

        if self.ws:
            try:
                await self.ws.close()
            except:
                pass
            self.ws = None

    async def _start_monitor_tts_response(self):
        """监听TTS响应"""
        opus_datas_cache = []
        is_first_sentence = True
        first_sentence_segment_count = 0  # 添加计数器
        logger.bind(tag=TAG).info("开始监听TTS响应")
        try:
            session_finished = False  # 标记会话是否正常结束
            while not self.conn.stop_event.is_set():
                try:
                    # 检查WebSocket连接状态
                    if self.ws is None:
                        logger.bind(tag=TAG).warning("WebSocket连接为None，退出监听")
                        break
                    if self.ws.state == State.CLOSED:
                        logger.bind(tag=TAG).warning(f"WebSocket连接已关闭，状态: {self.ws.state}，退出监听")
                        break

                    # 确保 `recv()` 运行在同一个 event loop
                    msg = await asyncio.wait_for(self.ws.recv(), timeout=30)
                    res = self.parser_response(msg)
                    self.print_response(res, "TTS响应:")

                    if res.optional.event == EVENT_SessionCanceled:
                        logger.bind(tag=TAG).debug(f"释放服务端资源成功～～")
                        session_finished = True
                        break
                    elif res.optional.event == EVENT_TTSSentenceStart:
                        json_data = json.loads(res.payload.decode("utf-8"))
                        self.tts_text = json_data.get("text", "")
                        logger.bind(tag=TAG).debug(f"句子语音生成开始: {self.tts_text}")
                        self.tts_audio_queue.put(
                            (SentenceType.FIRST, [], self.tts_text)
                        )
                        opus_datas_cache = []
                        first_sentence_segment_count = 0  # 重置计数器
                    elif (
                        res.optional.event == EVENT_TTSResponse
                        and res.header.message_type == AUDIO_ONLY_RESPONSE
                    ):
                        logger.bind(tag=TAG).info(f"收到音频响应，开始处理音频数据")
                        opus_datas = self.wav_to_opus_data_audio_raw(res.payload)
                        logger.bind(tag=TAG).info(f"音频转换完成，opus帧数: {len(opus_datas)}, payload大小: {len(res.payload) if res.payload else 0}")
                        
                        if is_first_sentence:
                            first_sentence_segment_count += 1
                            if first_sentence_segment_count <= 3:
                                logger.bind(tag=TAG).info(f"第一句话直接推送，帧数: {len(opus_datas)}, segment_count: {first_sentence_segment_count}")
                                self.tts_audio_queue.put(
                                    (SentenceType.MIDDLE, opus_datas, None)
                                )
                            else:
                                logger.bind(tag=TAG).info(f"第一句话缓存，当前缓存帧数: {len(opus_datas_cache)}, 新增帧数: {len(opus_datas)}")
                                opus_datas_cache.extend(opus_datas)
                        else:
                            # 后续句子缓存
                            logger.bind(tag=TAG).info(f"后续句子缓存，当前缓存帧数: {len(opus_datas_cache)}, 新增帧数: {len(opus_datas)}")
                            opus_datas_cache.extend(opus_datas)
                    elif res.optional.event == EVENT_TTSSentenceEnd:
                        logger.bind(tag=TAG).info(f"句子语音生成成功：{self.tts_text}")
                        # 修复：无论分片数量多少，第一句结束时都推送缓存
                        if opus_datas_cache:
                            logger.bind(tag=TAG).info(f"句子结束，推送缓存的音频数据，帧数: {len(opus_datas_cache)}")
                            self.tts_audio_queue.put(
                                (SentenceType.MIDDLE, opus_datas_cache, None)
                            )
                        else:
                            logger.bind(tag=TAG).warning(f"句子结束，但缓存的音频数据为空！")
                        is_first_sentence = False
                    elif res.optional.event == EVENT_SessionFinished:
                        logger.bind(tag=TAG).debug(f"会话结束～～")
                        self._process_before_stop_play_files()
                        session_finished = True
                        break
                except asyncio.TimeoutError:
                    logger.bind(tag=TAG).warning("接收TTS响应超时，检查连接状态")
                    if self.ws and self.ws.state != State.CLOSED:
                        continue  # 连接正常，继续等待
                    else:
                        logger.bind(tag=TAG).warning("WebSocket连接异常，退出监听")
                        break
                except websockets.ConnectionClosed as e:
                    logger.bind(tag=TAG).warning(f"WebSocket连接已关闭: {e}")
                    break
                except Exception as e:
                    logger.bind(tag=TAG).error(
                        f"监听TTS响应时发生错误: {e}"
                    )
                    traceback.print_exc()
                    break
            # 仅在连接异常时才关闭
            logger.bind(tag=TAG).info("监听任务结束，开始清理资源")
            if not session_finished and self.ws:
                try:
                    await self.ws.close()
                    logger.bind(tag=TAG).info("WebSocket连接已关闭")
                except Exception as e:
                    logger.bind(tag=TAG).warning(f"关闭WebSocket连接时出错: {e}")
                self.ws = None
        # 监听任务退出时清理引用
        finally:
            self._monitor_task = None

    async def send_event(
        self,
        ws: websockets.WebSocketClientProtocol,
        header: bytes,
        optional: bytes | None = None,
        payload: bytes = None,
    ):
        try:
            full_client_request = bytearray(header)
            if optional is not None:
                full_client_request.extend(optional)
            if payload is not None:
                payload_size = len(payload).to_bytes(4, "big", signed=True)
                full_client_request.extend(payload_size)
                full_client_request.extend(payload)
            await ws.send(full_client_request)
        except websockets.ConnectionClosed:
            logger.bind(tag=TAG).error(f"ConnectionClosed")
            raise

    async def send_text(self, speaker: str, text: str, session_id):
        header = Header(
            message_type=FULL_CLIENT_REQUEST,
            message_type_specific_flags=MsgTypeFlagWithEvent,
            serial_method=JSON,
        ).as_bytes()
        optional = Optional(event=EVENT_TaskRequest, sessionId=session_id).as_bytes()
        payload = self.get_payload_bytes(
            event=EVENT_TaskRequest, text=text, speaker=speaker
        )
        return await self.send_event(self.ws, header, optional, payload)

    # 读取 res 数组某段 字符串内容
    def read_res_content(self, res: bytes, offset: int):
        content_size = int.from_bytes(res[offset : offset + 4], "big", signed=True)
        offset += 4
        content = str(res[offset : offset + content_size])
        offset += content_size
        return content, offset

    # 读取 payload
    def read_res_payload(self, res: bytes, offset: int):
        payload_size = int.from_bytes(res[offset : offset + 4], "big", signed=True)
        offset += 4
        payload = res[offset : offset + payload_size]
        offset += payload_size
        return payload, offset

    def parser_response(self, res) -> Response:
        if isinstance(res, str):
            raise RuntimeError(res)
        response = Response(Header(), Optional())
        # 解析结果
        # header
        header = response.header
        num = 0b00001111
        header.protocol_version = res[0] >> 4 & num
        header.header_size = res[0] & 0x0F
        header.message_type = (res[1] >> 4) & num
        header.message_type_specific_flags = res[1] & 0x0F
        header.serialization_method = res[2] >> num
        header.message_compression = res[2] & 0x0F
        header.reserved = res[3]
        #
        offset = 4
        optional = response.optional
        if header.message_type == FULL_SERVER_RESPONSE or AUDIO_ONLY_RESPONSE:
            # read event
            if header.message_type_specific_flags == MsgTypeFlagWithEvent:
                optional.event = int.from_bytes(res[offset:8], "big", signed=True)
                offset += 4
                if optional.event == EVENT_NONE:
                    return response
                # read connectionId
                elif optional.event == EVENT_ConnectionStarted:
                    optional.connectionId, offset = self.read_res_content(res, offset)
                elif optional.event == EVENT_ConnectionFailed:
                    optional.response_meta_json, offset = self.read_res_content(
                        res, offset
                    )
                elif (
                    optional.event == EVENT_SessionStarted
                    or optional.event == EVENT_SessionFailed
                    or optional.event == EVENT_SessionFinished
                ):
                    optional.sessionId, offset = self.read_res_content(res, offset)
                    optional.response_meta_json, offset = self.read_res_content(
                        res, offset
                    )
                else:
                    optional.sessionId, offset = self.read_res_content(res, offset)
                    response.payload, offset = self.read_res_payload(res, offset)

        elif header.message_type == ERROR_INFORMATION:
            optional.errorCode = int.from_bytes(
                res[offset : offset + 4], "big", signed=True
            )
            offset += 4
            response.payload, offset = self.read_res_payload(res, offset)
        return response

    async def start_connection(self):
        header = Header(
            message_type=FULL_CLIENT_REQUEST,
            message_type_specific_flags=MsgTypeFlagWithEvent,
        ).as_bytes()
        optional = Optional(event=EVENT_Start_Connection).as_bytes()
        payload = str.encode("{}")
        return await self.send_event(self.ws, header, optional, payload)

    def print_response(self, res, tag_msg: str):
        logger.bind(tag=TAG).debug(f"===>{tag_msg} header:{res.header.__dict__}")
        logger.bind(tag=TAG).debug(f"===>{tag_msg} optional:{res.optional.__dict__}")

    def get_payload_bytes(
        self,
        uid="1234",
        event=EVENT_NONE,
        text="",
        speaker="",
        audio_format="pcm",
        audio_sample_rate=16000,
        use_clone_speaker=None,
        clone_speaker_id=None,
        post_process=None
    ):
        # 使用实例配置作为默认值
        if use_clone_speaker is None:
            use_clone_speaker = self.use_clone_speaker
        if clone_speaker_id is None:
            clone_speaker_id = self.clone_speaker_id
        if post_process is None:
            post_process = self.post_process

        req_params = {
            "text": text,
            "audio_params": {
                "format": audio_format,
                "sample_rate": audio_sample_rate,
            }
        }

        # 设置发音人和后处理参数
        if use_clone_speaker and clone_speaker_id:
            req_params["speaker"] = clone_speaker_id
            logger.bind(tag=TAG).debug(f"使用复刻发音人ID: {clone_speaker_id}")

            # 后处理参数 - 复刻发音人使用additions字段
            if post_process:
                req_params["additions"] = json.dumps({"post_process": post_process})
                logger.bind(tag=TAG).debug(f"复刻发音人后处理参数: {post_process}")
            else:
                default_params = {"pitch": 0, "speed": 1.0, "volume": 0.6}
                req_params["additions"] = json.dumps({"post_process": default_params})
        elif speaker and speaker.strip():
            req_params["speaker"] = speaker
            logger.bind(tag=TAG).debug(f"使用预置发音人: {speaker}")
            # 预置发音人使用audio_params中的post_process
            if post_process:
                req_params["audio_params"]["post_process"] = post_process
                logger.bind(tag=TAG).debug(f"预置发音人后处理参数: {post_process}")
            else:
                default_params = {"pitch": 0, "speed": 1.0, "volume": 0.6}
                req_params["audio_params"]["post_process"] = default_params
        else:
            # 如果既没有启用复刻也没有指定speaker，使用默认发音人
            default_speaker = "zh_female_wanwanxiaohe_moon_bigtts"
            req_params["speaker"] = default_speaker
            logger.bind(tag=TAG).warning(f"未指定发音人，使用默认发音人: {default_speaker}")
            default_params = {"pitch": 0, "speed": 1.0, "volume": 0.6}
            req_params["audio_params"]["post_process"] = default_params

        payload = {
            "user": {"uid": uid},
            "event": event,
            "namespace": "BidirectionalTTS",
            "req_params": req_params,
        }

        return str.encode(json.dumps(payload))

    def wav_to_opus_data_audio_raw(self, raw_data_var, is_end=False):
        logger.bind(tag=TAG).info(f"开始音频转换，输入数据大小: {len(raw_data_var) if raw_data_var else 0} bytes, is_end: {is_end}")
        if not raw_data_var:
            logger.bind(tag=TAG).warning(f"输入音频数据为空，无法转换")
            return []
        
        try:
            opus_datas = self.opus_encoder.encode_pcm_to_opus(raw_data_var, is_end)
            logger.bind(tag=TAG).info(f"音频转换成功，输出opus帧数: {len(opus_datas) if opus_datas else 0}")
            return opus_datas
        except Exception as e:
            logger.bind(tag=TAG).error(f"音频转换失败: {e}")
            return []

