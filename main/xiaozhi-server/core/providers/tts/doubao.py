import uuid
import json
import base64
import asyncio
import aiohttp
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from core.providers.tts.doubao_streaming import StreamingTTSProvider  # 导入流式处理器
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        if config.get("appid"):
            self.appid = int(config.get("appid"))
        else:
            self.appid = ""
        self.access_token = config.get("access_token")
        self.cluster = config.get("cluster")

        if config.get("private_voice"):
            self.voice = config.get("private_voice")
        else:
            self.voice = config.get("voice")

        # 处理空字符串的情况
        speed_ratio = config.get("speed_ratio", "1.0")
        volume_ratio = config.get("volume_ratio", "1.0")
        pitch_ratio = config.get("pitch_ratio", "1.0")
        self.audio_file_type = config.get("format", "wav")
        self.speed_ratio = float(speed_ratio) if speed_ratio else 1.0
        self.volume_ratio = float(volume_ratio) if volume_ratio else 1.0
        self.pitch_ratio = float(pitch_ratio) if pitch_ratio else 1.0

        self.api_url = config.get("api_url")
        self.authorization = config.get("authorization")
        
        # 性能优化：使用流式处理器
        self.streaming_processor = StreamingTTSProvider(config, delete_audio_file)
        self.enable_streaming = config.get("enable_streaming", True)  # 默认启用流式处理
        self.text_length_threshold = config.get("streaming_threshold", 50)  # 超过50字符使用流式处理
        
        model_key_msg = check_model_key("TTS", self.access_token)
        if model_key_msg:
            logger.bind(tag=TAG).error(model_key_msg)

    async def text_to_speak(self, text, output_file):
        """智能选择处理方式"""
        # 如果文本较长且启用了流式处理，使用流式处理
        if self.enable_streaming and len(text) > self.text_length_threshold:
            logger.info(f"使用流式处理TTS: 文本长度 {len(text)} 字符")
            return await self.streaming_processor.text_to_speak(text, output_file)
        else:
            # 使用原有的单次处理方式
            return await self._text_to_speak_single(text, output_file)

    async def _text_to_speak_single(self, text, output_file):
        """原有的单次处理方式（优化为异步）"""
        request_json = {
            "app": {
                "appid": f"{self.appid}",
                "token": self.access_token,
                "cluster": self.cluster,
            },
            "user": {"uid": "1"},
            "audio": {
                "voice_type": self.voice,
                "encoding": self.audio_file_type,
                "speed_ratio": self.speed_ratio,
                "volume_ratio": self.volume_ratio,
                "pitch_ratio": self.pitch_ratio,
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson",
            },
        }

        try:
            headers = {"Authorization": f"{self.authorization}{self.access_token}"}
            
            # 使用异步HTTP客户端替代同步requests
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.api_url,
                    json=request_json,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as resp:
                    if resp.status == 200:
                        resp_data = await resp.json()
                        if "data" in resp_data:
                            data = resp_data["data"]
                            audio_bytes = base64.b64decode(data)
                            if output_file:
                                with open(output_file, "wb") as file_to_save:
                                    file_to_save.write(audio_bytes)
                            else:
                                return audio_bytes
                        else:
                            raise Exception(
                                f"TTS响应中没有音频数据: {resp_data}"
                            )
                    else:
                        response_text = await resp.text()
                        raise Exception(f"TTS请求失败: {resp.status} - {response_text}")
                        
        except Exception as e:
            raise Exception(f"{__name__} error: {e}")

    async def text_to_speak_streaming(self, text):
        """提供流式接口"""
        return self.streaming_processor.text_to_speak_streaming(text)

    async def text_to_speak_with_callback(self, text, audio_callback):
        """使用回调处理音频流"""
        return await self.streaming_processor.text_to_speak_with_callback(text, audio_callback)
