import queue
import threading
import time
import uuid
import json
import os
import websocket
import asyncio
from config.logger import setup_logging
from core.utils.tts import MarkdownCleaner
from core.providers.tts.base import TTSProviderBase
from core.utils import opus_encoder_utils
from core.utils.util import audio_bytes_to_data
from core.providers.tts.dto.dto import SentenceType, ContentType, InterfaceType

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.interface_type = InterfaceType.DUAL_STREAM
        self.api_key = config.get("api_key") or config.get("appkey") or os.environ.get("DASHSCOPE_API_KEY")
        self.model = config.get("model", "cosyvoice-v2")
        self.voice = config.get("voice", "longhua_v2")
        self.ws_url = config.get("ws_url", "wss://dashscope.aliyuncs.com/api-ws/v1/inference/")
        self.first_text_time = None
        self.first_audio_time = None
        self.before_stop_play_files = []
        self.current_segment_text = None
        self.task_id = str(uuid.uuid4())
        self.ws = None
        self.task_started = False
        self.task_finished = False
        self._ws_thread = None
        self._ws_ready_event = threading.Event()
        self._pending_segments = queue.Queue()
        self._pending_finish = False
        self._lock = threading.Lock()
        self.opus_encoder = opus_encoder_utils.OpusEncoderUtils(
            sample_rate=16000, channels=1, frame_size_ms=60
        )
        # 音频缓存相关
        self.opus_datas_cache = []
        self.is_first_sentence = True
        self.first_sentence_segment_count = 0
        self.session_started = False
        # 预建立连接标志
        self.connection_pre_established = False
        # 文本缓存队列
        self.cached_texts = []

    def tts_text_priority_thread(self):
        """真正的双流式处理：立即处理每个文本段，不等待收集完成"""
        self.first_text_time = None
        self.first_audio_time = None
        self.task_started = False
        self.task_finished = False
        self.session_started = False
        self.is_first_sentence = True
        self.first_sentence_segment_count = 0
        self.opus_datas_cache = []
        
        while not self.conn.stop_event.is_set():
            try:
                message = self.tts_text_queue.get(timeout=1)
                logger.bind(tag=TAG).debug(
                    f"收到TTS任务｜{message.sentence_type.name} ｜ {message.content_type.name}"
                )
                
                if self.conn.client_abort:
                    logger.bind(tag=TAG).info("收到打断信息，终止TTS文本处理线程")
                    continue

                if message.sentence_type == SentenceType.FIRST:
                    # 立即启动会话，不等待
                    try:
                        logger.bind(tag=TAG).info("开始启动阿里云TTS会话...")
                        future = asyncio.run_coroutine_threadsafe(
                            self.start_session(),
                            loop=self.conn.loop,
                        )
                        future.result()
                        self.tts_audio_first_sentence = True
                        self.before_stop_play_files.clear()
                        logger.bind(tag=TAG).info("阿里云TTS会话启动成功")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"启动阿里云TTS会话失败: {str(e)}")
                        continue

                elif ContentType.TEXT == message.content_type:
                    if message.content_detail:
                        try:
                            # 记录第一个文本时间
                            if self.first_text_time is None:
                                self.first_text_time = time.time()
                                logger.bind(tag=TAG).info(f"[aliyun_double_stream] 大模型下发第一个文本时间: {self.first_text_time:.3f}, 文本内容: {message.content_detail}")
                            
                            logger.bind(tag=TAG).debug(f"开始发送TTS文本: {message.content_detail}")
                            future = asyncio.run_coroutine_threadsafe(
                                self.text_to_speak(message.content_detail, None),
                                loop=self.conn.loop,
                            )
                            future.result()
                            logger.bind(tag=TAG).debug("TTS文本发送成功")
                        except Exception as e:
                            logger.bind(tag=TAG).error(f"发送TTS文本失败: {str(e)}")
                            continue

                elif ContentType.FILE == message.content_type:
                    logger.bind(tag=TAG).info(
                        f"添加音频文件到待播放列表: {message.content_file}"
                    )
                    self.before_stop_play_files.append(
                        (message.content_file, message.content_detail)
                    )

                if message.sentence_type == SentenceType.LAST:
                    try:
                        logger.bind(tag=TAG).info("开始结束阿里云TTS会话...")
                        future = asyncio.run_coroutine_threadsafe(
                            self.finish_session(),
                            loop=self.conn.loop,
                        )
                        future.result()
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"结束阿里云TTS会话失败: {str(e)}")
                        continue
                        
            except queue.Empty:
                continue
            except Exception as e:
                logger.bind(tag=TAG).error(f"处理TTS文本失败: {str(e)}")
                continue

    async def start_session(self):
        """简化的会话启动 - 主要用于初始化"""
        logger.bind(tag=TAG).info("开始启动阿里云TTS会话...")
        try:
            # 如果连接还没建立，立即建立
            if not self.connection_pre_established:
                await self._ensure_connection_async()
            
            # 最小等待WebSocket连接
            if not self._ws_ready_event.wait(timeout=1):
                logger.bind(tag=TAG).warning("WebSocket连接等待超时，继续执行")
                
            self.session_started = True
            logger.bind(tag=TAG).info("阿里云TTS会话启动完成")
        except Exception as e:
            logger.bind(tag=TAG).error(f"启动阿里云TTS会话失败: {str(e)}")
            # 不抛出异常，继续执行
            self.session_started = True

    async def finish_session(self):
        """结束阿里云TTS会话（只发送finish-task，不立即关闭WebSocket，等待task-finished事件）"""
        logger.bind(tag=TAG).info("结束阿里云TTS会话...")
        try:
            self._send_finish_task()
            # 不再主动关闭WebSocket，等待task-finished事件
            timeout = 2  # 最多等2秒
            start_time = time.time()
            while not self.task_finished and (time.time() - start_time) < timeout:
                await asyncio.sleep(0.05)
            logger.bind(tag=TAG).info("阿里云TTS会话finish-task已发送，等待task-finished事件关闭连接")
        except Exception as e:
            logger.bind(tag=TAG).error(f"结束阿里云TTS会话失败: {str(e)}")
            raise

    async def text_to_speak(self, text, _):
        """发送文本到TTS服务 - 激进优化版本"""
        try:
            # 清理Markdown并发送文本
            filtered_text = MarkdownCleaner.clean_markdown(text)
            
            # 如果连接还没建立，立即建立（不等待会话启动完成）
            if not self.connection_pre_established:
                logger.bind(tag=TAG).warning("连接未预建立，立即建立连接")
                # 异步启动连接，不等待完成
                asyncio.create_task(self._ensure_connection_async())
            
            # 最小等待逻辑：只等待WebSocket连接，不等待task_started
            max_wait = 10  # 最多等待1秒
            wait_count = 0
            while not self._ws_ready_event.is_set() and wait_count < max_wait:
                await asyncio.sleep(0.1)
                wait_count += 1
            
            # 直接发送文本，不等待task_started
            self._send_continue_task(filtered_text)
            logger.bind(tag=TAG).info(f"已直接发送文本到阿里云TTS: {filtered_text}")
            return
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"发送TTS文本失败: {str(e)}")
            raise

    async def _ensure_connection_async(self):
        """异步建立连接"""
        try:
            if not self.connection_pre_established:
                self._ws_thread = threading.Thread(target=self._run_ws_client, daemon=True)
                self._ws_thread.start()
                self.connection_pre_established = True
        except Exception as e:
            logger.bind(tag=TAG).error(f"异步建立连接失败: {str(e)}")

    def _run_ws_client(self):
        header = [
            f"Authorization: Bearer {self.api_key}",
            "X-DashScope-DataInspection: enable"
        ]
        try:
            self.ws = websocket.WebSocketApp(
                self.ws_url,
                header=header,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            logger.bind(tag=TAG).info("[aliyun_double_stream] WebSocket客户端启动...")
            self.ws.run_forever()
        except Exception as e:
            logger.bind(tag=TAG).error(f"[aliyun_double_stream] WebSocket客户端异常: {e}")

    def _on_open(self, ws):
        logger.bind(tag=TAG).info("[aliyun_double_stream] WebSocket已连接，发送run-task...")
        self._ws_ready_event.set()
        run_task_cmd = {
            "header": {
                "action": "run-task",
                "task_id": self.task_id,
                "streaming": "duplex"
            },
            "payload": {
                "task_group": "audio",
                "task": "tts",
                "function": "SpeechSynthesizer",
                "model": self.model,
                "parameters": {
                    "text_type": "PlainText",
                    "voice": self.voice,
                    "format": "pcm",
                    "sample_rate": 16000,
                    "volume": 50,
                    "rate": 1,
                    "pitch": 1
                },
                "input": {}
            }
        }
        ws.send(json.dumps(run_task_cmd))
        logger.bind(tag=TAG).info("[aliyun_double_stream] 已发送run-task指令")

    def _on_message(self, ws, message):
        if isinstance(message, str):
            try:
                msg_json = json.loads(message)
                if "header" in msg_json:
                    header = msg_json["header"]
                    event = header.get("event")
                    if event == "task-started":
                        self.task_started = True
                        logger.bind(tag=TAG).info("[aliyun_double_stream] 任务已启动，发送缓存的文本")
                        # 立即发送所有缓存的文本
                        self._send_cached_texts()
                    elif event == "result-generated":
                        pass
                    elif event == "task-finished":
                        self.task_finished = True
                        # 处理剩余缓存的音频数据
                        if self.opus_datas_cache:
                            self.tts_audio_queue.put(
                                (SentenceType.MIDDLE, self.opus_datas_cache, None)
                            )
                            self.opus_datas_cache = []
                        self._process_before_stop_play_files()
                        logger.bind(tag=TAG).info(f"[aliyun_double_stream] 任务已完成")
                        # 在此处关闭WebSocket
                        if self.ws:
                            try:
                                self.ws.close()
                            except:
                                pass
                            self.ws = None
                        logger.bind(tag=TAG).info("阿里云TTS会话已结束")
            except Exception as e:
                logger.bind(tag=TAG).error(f"[aliyun_double_stream] JSON消息处理异常: {e}")
        else:
            # 二进制音频流 - 直接处理，不打印调试日志
            self._process_audio_data(message)

    def _process_audio_data(self, audio_data):
        """处理音频数据，参考火山双流式的缓存机制"""
        # 记录第一句音频输出时间
        if self.first_audio_time is None:
            self.first_audio_time = time.time()
            delay = self.first_audio_time - self.first_text_time if self.first_text_time else 0
            logger.bind(tag=TAG).info(f"[aliyun_double_stream] 第一句音频输出时间: {self.first_audio_time:.3f}, 延迟: {delay:.3f} 秒")
            
            # 发送第一帧音频作为句子开始标记
            self.tts_audio_queue.put(
                (SentenceType.FIRST, [], "阿里云TTS音频开始")
            )
        
        # 编码音频数据
        end_of_stream = self.task_finished
        opus_datas = self.opus_encoder.encode_pcm_to_opus(audio_data, end_of_stream)
        
        # 移除频繁的调试日志
        # logger.bind(tag=TAG).debug(f"[aliyun_double_stream] 收到音频分片: {len(audio_data)} 字节，opus帧数: {len(opus_datas)}")
        
        # 优化缓存策略：更激进的直发策略
        if self.is_first_sentence:
            self.first_sentence_segment_count += 1
            if self.first_sentence_segment_count <= 12:  # 增加到12帧直发
                # 第一句话的前12帧直接发送，确保极低延迟
                self.tts_audio_queue.put(
                    (SentenceType.MIDDLE, opus_datas, None)
                )
            else:
                # 后续帧缓存
                self.opus_datas_cache.extend(opus_datas)
        else:
            # 后续句子也适当增加直发帧数
            if len(self.opus_datas_cache) < 10:
                # 直接发送前几帧
                self.tts_audio_queue.put(
                    (SentenceType.MIDDLE, opus_datas, None)
                )
            else:
                # 后续帧缓存
                self.opus_datas_cache.extend(opus_datas)
        
        # 优化的句子结束检测
        if self._should_trigger_sentence_end():
            self._on_sentence_end()

    def _should_trigger_sentence_end(self):
        """优化的句子结束判断逻辑"""
        if self.task_finished:
            return True
            
        # 基于缓存帧数的简单策略，减少触发阈值
        cache_frames = len(self.opus_datas_cache)
        
        # 第一句话：当缓存帧数较少时就触发，提高响应速度
        if self.is_first_sentence and cache_frames >= 15:  # 从25减少到15
            return True
            
        # 后续句子：也减少阈值
        if not self.is_first_sentence and cache_frames >= 25:  # 从40减少到25
            return True
            
        return False

    def _on_sentence_end(self):
        """优化的句子结束处理，参考火山双流式的处理"""
        # 减少调试日志
        # logger.bind(tag=TAG).debug(f"[aliyun_double_stream] 句子结束，发送缓存的音频帧: {len(self.opus_datas_cache)}")
        
        if self.opus_datas_cache:
            # 发送缓存的音频数据
            self.tts_audio_queue.put(
                (SentenceType.MIDDLE, self.opus_datas_cache[:], None)  # 复制列表避免引用问题
            )
            self.opus_datas_cache.clear()  # 清空缓存
        
        # 更新句子状态
        if self.is_first_sentence:
            self.is_first_sentence = False
            # logger.bind(tag=TAG).debug(f"[aliyun_double_stream] 第一句话处理完成，切换到后续句子模式")

    def _on_error(self, ws, error):
        logger.bind(tag=TAG).error(f"[aliyun_double_stream] WebSocket出错: {error}")

    def _on_close(self, ws, close_status_code, close_msg):
        logger.bind(tag=TAG).info(f"[aliyun_double_stream] WebSocket已关闭: {close_msg} ({close_status_code})")

    def _send_cached_texts(self):
        """发送所有缓存的文本"""
        with self._lock:
            for text in self.cached_texts:
                self._send_continue_task_immediate(text)
                logger.bind(tag=TAG).info(f"发送缓存文本: {text}")
            self.cached_texts.clear()

    def _send_continue_task(self, text):
        """智能发送continue-task指令：如果task_started则立即发送，否则缓存"""
        with self._lock:
            if self.task_started:
                # 任务已启动，立即发送
                self._send_continue_task_immediate(text)
            else:
                # 任务未启动，缓存文本
                self.cached_texts.append(text)
                logger.bind(tag=TAG).info(f"缓存文本等待task_started: {text}")

    def _send_continue_task_immediate(self, text):
        """立即发送continue-task指令"""
        cmd = {
            "header": {
                "action": "continue-task",
                "task_id": self.task_id,
                "streaming": "duplex"
            },
            "payload": {
                "input": {
                    "text": text
                }
            }
        }
        try:
            if self.ws and self.ws.sock and self.ws.sock.connected:
                self.ws.send(json.dumps(cmd))
                # 减少debug日志
                # logger.bind(tag=TAG).debug(f"[aliyun_double_stream] 已发送continue-task: {text}")
            else:
                logger.bind(tag=TAG).warning(f"[aliyun_double_stream] WebSocket已关闭，无法发送: {text}")
        except Exception as e:
            logger.bind(tag=TAG).warning(f"[aliyun_double_stream] 发送continue-task异常: {e}")

    def _send_finish_task(self):
        """发送finish-task指令"""
        cmd = {
            "header": {
                "action": "finish-task",
                "task_id": self.task_id,
                "streaming": "duplex"
            },
            "payload": {
                "input": {}
            }
        }
        try:
            if self.ws and self.ws.sock and self.ws.sock.connected:
                self.ws.send(json.dumps(cmd))
                # logger.bind(tag=TAG).debug("[aliyun_double_stream] 已发送finish-task指令")
            else:
                logger.bind(tag=TAG).warning("[aliyun_double_stream] WebSocket已关闭，无法发送finish-task")
        except Exception as e:
            logger.bind(tag=TAG).warning(f"[aliyun_double_stream] 发送finish-task异常: {e}")

    def _process_before_stop_play_files(self):
        """处理待播放的音频文件"""
        for file_info in self.before_stop_play_files:
            if isinstance(file_info, tuple) and len(file_info) == 2:
                file_path, description = file_info
                logger.bind(tag=TAG).info(f"[aliyun_double_stream] 添加音频文件到播放队列: {file_path}")
                self.tts_audio_queue.put((SentenceType.MIDDLE, [file_path], description))
            else:
                logger.bind(tag=TAG).info(f"[aliyun_double_stream] 添加音频文件到播放队列: {file_info}")
                self.tts_audio_queue.put((SentenceType.MIDDLE, [file_info], None)) 