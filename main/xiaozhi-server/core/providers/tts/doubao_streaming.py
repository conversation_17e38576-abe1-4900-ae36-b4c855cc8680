"""
豆包TTS流式处理优化器
实现文本块并行处理和音频预生成功能
"""

import uuid
import json
import base64
import asyncio
import aiohttp
from typing import List, AsyncGenerator, Optional, Dict, Any
from dataclasses import dataclass
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


@dataclass
class TextChunk:
    """文本块数据结构"""
    text: str
    chunk_id: str
    order: int
    is_final: bool = False


@dataclass
class AudioChunk:
    """音频块数据结构"""
    audio_data: bytes
    chunk_id: str
    order: int
    is_final: bool = False


class StreamingTTSProvider(TTSProviderBase):
    """流式TTS提供者"""
    
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        
        # 豆包TTS配置
        if config.get("appid"):
            self.appid = int(config.get("appid"))
        else:
            self.appid = ""
        self.access_token = config.get("access_token")
        self.cluster = config.get("cluster")

        if config.get("private_voice"):
            self.voice = config.get("private_voice")
        else:
            self.voice = config.get("voice")

        # 处理空字符串的情况
        speed_ratio = config.get("speed_ratio", "1.0")
        volume_ratio = config.get("volume_ratio", "1.0")
        pitch_ratio = config.get("pitch_ratio", "1.0")
        self.audio_file_type = config.get("format", "wav")
        self.speed_ratio = float(speed_ratio) if speed_ratio else 1.0
        self.volume_ratio = float(volume_ratio) if volume_ratio else 1.0
        self.pitch_ratio = float(pitch_ratio) if pitch_ratio else 1.0

        self.api_url = config.get("api_url")
        self.authorization = config.get("authorization")
        
        # 流式处理配置
        self.max_concurrent_requests = 3  # 最大并发请求数
        self.chunk_size = 100  # 文本块大小（字符数）
        self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        model_key_msg = check_model_key("TTS", self.access_token)
        if model_key_msg:
            logger.bind(tag=TAG).error(model_key_msg)

    def split_text_into_chunks(self, text: str) -> List[TextChunk]:
        """将长文本分割为小块"""
        chunks = []
        sentences = self._split_sentences(text)
        
        current_chunk = ""
        chunk_order = 0
        
        for sentence in sentences:
            # 如果当前块加上新句子超过限制，创建新块
            if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                chunks.append(TextChunk(
                    text=current_chunk.strip(),
                    chunk_id=str(uuid.uuid4()),
                    order=chunk_order
                ))
                current_chunk = sentence
                chunk_order += 1
            else:
                current_chunk += sentence
        
        # 添加最后一个块
        if current_chunk.strip():
            chunks.append(TextChunk(
                text=current_chunk.strip(),
                chunk_id=str(uuid.uuid4()),
                order=chunk_order,
                is_final=True
            ))
        
        return chunks

    def _split_sentences(self, text: str) -> List[str]:
        """智能分句"""
        import re
        
        # 定义句子结束符
        sentence_endings = r'[。！？；：.!?;:]'
        
        # 分割句子，保留标点符号
        sentences = re.split(f'({sentence_endings})', text)
        
        # 重新组合句子和标点符号
        result = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                if sentence.strip():
                    result.append(sentence)
        
        # 处理最后一个句子（如果没有标点符号）
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            result.append(sentences[-1])
        
        return result

    async def text_to_speak_streaming(self, text: str) -> AsyncGenerator[AudioChunk, None]:
        """流式文本转语音"""
        # 分割文本
        text_chunks = self.split_text_into_chunks(text)
        
        if not text_chunks:
            return
        
        logger.info(f"文本分割为 {len(text_chunks)} 个块，开始并行处理")
        
        # 创建并发任务
        tasks = []
        for chunk in text_chunks:
            task = asyncio.create_task(self._process_text_chunk(chunk))
            tasks.append(task)
        
        # 按顺序收集结果
        audio_chunks = {}
        completed_tasks = asyncio.as_completed(tasks)
        
        async for task in completed_tasks:
            try:
                audio_chunk = await task
                if audio_chunk:
                    audio_chunks[audio_chunk.order] = audio_chunk
                    
                    # 按顺序输出已完成的音频块
                    while len(audio_chunks) > 0:
                        min_order = min(audio_chunks.keys())
                        if min_order in audio_chunks:
                            chunk = audio_chunks.pop(min_order)
                            yield chunk
                            if chunk.is_final:
                                break
                        else:
                            break
                            
            except Exception as e:
                logger.error(f"处理音频块时出错: {e}")

    async def _process_text_chunk(self, text_chunk: TextChunk) -> Optional[AudioChunk]:
        """处理单个文本块"""
        async with self.semaphore:  # 限制并发数
            try:
                logger.debug(f"处理文本块 {text_chunk.order}: {text_chunk.text[:50]}...")
                
                # 构建请求
                request_json = {
                    "app": {
                        "appid": f"{self.appid}",
                        "token": self.access_token,
                        "cluster": self.cluster,
                    },
                    "user": {"uid": "1"},
                    "audio": {
                        "voice_type": self.voice,
                        "encoding": self.audio_file_type,
                        "speed_ratio": self.speed_ratio,
                        "volume_ratio": self.volume_ratio,
                        "pitch_ratio": self.pitch_ratio,
                    },
                    "request": {
                        "reqid": text_chunk.chunk_id,
                        "text": text_chunk.text,
                        "text_type": "plain",
                        "operation": "query",
                        "with_frontend": 1,
                        "frontend_type": "unitTson",
                    },
                }
                
                # 使用异步HTTP客户端
                headers = {"Authorization": f"{self.authorization}{self.access_token}"}
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.api_url,
                        json=request_json,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        if response.status == 200:
                            resp_data = await response.json()
                            if "data" in resp_data:
                                audio_bytes = base64.b64decode(resp_data["data"])
                                
                                return AudioChunk(
                                    audio_data=audio_bytes,
                                    chunk_id=text_chunk.chunk_id,
                                    order=text_chunk.order,
                                    is_final=text_chunk.is_final
                                )
                            else:
                                raise Exception(f"TTS响应中没有音频数据: {resp_data}")
                        else:
                            response_text = await response.text()
                            raise Exception(f"TTS请求失败: {response.status} - {response_text}")
                            
            except Exception as e:
                logger.error(f"处理文本块 {text_chunk.order} 失败: {e}")
                return None

    async def text_to_speak(self, text, output_file):
        """兼容原有接口的方法"""
        if output_file:
            # 如果需要保存到文件，收集所有音频数据
            audio_data = b""
            async for audio_chunk in self.text_to_speak_streaming(text):
                audio_data += audio_chunk.audio_data
            
            with open(output_file, "wb") as file_to_save:
                file_to_save.write(audio_data)
        else:
            # 如果不需要保存文件，收集所有音频数据并返回
            audio_data = b""
            async for audio_chunk in self.text_to_speak_streaming(text):
                audio_data += audio_chunk.audio_data
            return audio_data

    async def text_to_speak_with_callback(self, text: str, audio_callback):
        """使用回调函数处理音频流"""
        async for audio_chunk in self.text_to_speak_streaming(text):
            await audio_callback(audio_chunk.audio_data, audio_chunk.is_final)

    def get_streaming_stats(self) -> Dict[str, Any]:
        """获取流式处理统计信息"""
        return {
            "max_concurrent_requests": self.max_concurrent_requests,
            "chunk_size": self.chunk_size,
            "current_semaphore_value": self.semaphore._value
        }