import queue
import asyncio
import traceback
import websockets
import uuid
import json
import io
from config.logger import setup_logging
from core.utils.tts import MarkdownCleaner
from core.providers.tts.base import TTSProviderBase
from core.utils import opus_encoder_utils
from core.providers.tts.dto.dto import SentenceType, ContentType, InterfaceType
from core.utils.util import audio_bytes_to_data
import ssl
import time

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.interface_type = InterfaceType.SINGLE_STREAM
        self.api_key = config.get("api_key")
        self.model = config.get("model")
        self.group_id = config.get("group_id")
        if config.get("private_voice"):
            self.voice = config.get("private_voice")
        else:
            self.voice = config.get("voice_id")
        self.voice_setting = config.get("voice_setting", {
            "voice_id": self.voice or "female-shaonv",
            "speed": 1,
            "vol": 1,
            "pitch": 0,
            "emotion": "happy",
        })
        self.audio_setting = config.get("audio_setting", {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1,
        })
        self.header = {"Authorization": f"Bearer {self.api_key}"}
        self.ws_url = "wss://api.minimaxi.com/ws/v1/t2a_v2"
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
        self.opus_encoder = opus_encoder_utils.OpusEncoderUtils(
            sample_rate=16000, channels=1, frame_size_ms=60
        )
        self.before_stop_play_files = []
        self.segment_count = 0
        self.pcm_buffer = bytearray()
        self.first_text_time = None
        self.first_audio_time = None
        # 添加会话状态相关属性
        self.session_finished = False

    def tts_text_priority_thread(self):
        """minimax单流式TTS文本处理线程"""
        while not self.conn.stop_event.is_set():
            try:
                message = self.tts_text_queue.get(timeout=1)
                if message.sentence_type == SentenceType.FIRST:
                    self.tts_stop_request = False
                    self.processed_chars = 0
                    self.tts_text_buff = []
                    self.segment_count = 0
                    self.tts_audio_first_sentence = True
                    self.before_stop_play_files.clear()
                    self.session_finished = False
                elif ContentType.TEXT == message.content_type:
                    # 累积文本
                    self.tts_text_buff.append(message.content_detail)
                    # 尝试分段处理，避免死循环
                    while True:
                        segment_text = self._get_segment_text()
                        if segment_text is None:
                            break  # 没有可分段的文本，退出循环
                        logger.bind(tag=TAG).info(f"[minimax_single_stream] 分段文本: {segment_text}")
                        self.to_tts_single_stream(segment_text)
                        
                elif ContentType.FILE == message.content_type:
                    logger.bind(tag=TAG).info(
                        f"添加音频文件到待播放列表: {message.content_file}"
                    )
                    self.before_stop_play_files.append(
                        (message.content_file, message.content_detail)
                    )
                    
                if message.sentence_type == SentenceType.LAST:
                    logger.bind(tag=TAG).info(f"[minimax_single_stream] 处理剩余文本: {''.join(self.tts_text_buff)[self.processed_chars:]}")
                    self._process_remaining_text(True)
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.bind(tag=TAG).error(
                    f"处理TTS文本失败: {str(e)}, 类型: {type(e).__name__}, 堆栈: {traceback.format_exc()}"
                )

    def _process_remaining_text(self, is_last=False):
        """处理剩余文本，确保只处理一次"""
        if self.session_finished:
            return
            
        full_text = "".join(self.tts_text_buff)
        remaining_text = full_text[self.processed_chars:]
        
        if remaining_text and remaining_text.strip():
            logger.bind(tag=TAG).info(f"[minimax_single_stream] 处理剩余文本: {remaining_text}")
            self.to_tts_single_stream(remaining_text, is_last)
            self.processed_chars = len(full_text)  # 标记全部已处理
        
        # 处理音频文件
        self._process_before_stop_play_files()
        self.session_finished = True

    def to_tts_single_stream(self, text, is_last=False):
        logger.bind(tag=TAG).info(f"[minimax_single_stream] to_tts_single_stream调用, 文本: {text}")
        try:
            text = MarkdownCleaner.clean_markdown(text)
            if text and text.strip():  # 确保文本不为空
                asyncio.run(self.text_to_speak(text, is_last))
        except Exception as e:
            logger.bind(tag=TAG).error(f"Failed to generate TTS file: {e}")
        finally:
            return None

    async def text_to_speak(self, text, is_last):
        # 记录大模型下发第一个文本的时间
        if self.first_text_time is None:
            self.first_text_time = time.time()
            logger.bind(tag=TAG).info(f"[minimax_single_stream] 大模型下发第一个文本时间: {self.first_text_time:.3f}, 文本内容: {text}")
        await self._tts_request(text, is_last)

    async def close(self):
        await super().close()
        if hasattr(self, "opus_encoder"):
            self.opus_encoder.close()

    def _process_before_stop_play_files(self):
        """处理待播放的音频文件"""
        for file_info in self.before_stop_play_files:
            if isinstance(file_info, tuple) and len(file_info) == 2:
                file_path, description = file_info
                if file_path and isinstance(file_path, str):
                    # 处理音频文件并转换为opus数据
                    try:
                        audio_datas = self._process_audio_file(file_path)
                        self.tts_audio_queue.put((SentenceType.MIDDLE, audio_datas, description))
                        logger.bind(tag=TAG).info(f"[minimax_single_stream] 添加音频文件到播放队列: {file_path}")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"[minimax_single_stream] 处理音频文件失败: {file_path}, 错误: {e}")
                else:
                    # 如果文件路径为空，直接添加描述文本
                    self.tts_audio_queue.put((SentenceType.MIDDLE, [], description))
            else:
                # 兼容其他格式
                logger.bind(tag=TAG).info(f"[minimax_single_stream] 添加音频到播放队列: {file_info}")
                self.tts_audio_queue.put((SentenceType.MIDDLE, [], str(file_info)))
        
        # 清空待播放文件列表
        self.before_stop_play_files.clear()
        
        # 推送会话结束标记
        if not self.session_finished:
            self.tts_audio_queue.put((SentenceType.LAST, [], None))

    async def _tts_request(self, text: str, is_last: bool) -> None:
        ws = None
        try:
            logger.bind(tag=TAG).info(f"[minimax_single_stream] 开始TTS请求: {text}")
            ws = await websockets.connect(
                self.ws_url,
                additional_headers=self.header,
                ssl=self.ssl_context,
                max_size=1000000000
            )
            # 1. 等待 connected_success
            while True:
                resp = await ws.recv()
                response = json.loads(resp)
                if response.get("event") == "connected_success":
                    break
            # 2. 发送task_start
            start_msg = {
                "event": "task_start",
                "model": self.model,
                "voice_setting": self.voice_setting,
                "audio_setting": self.audio_setting,
            }
            await ws.send(json.dumps(start_msg))
            # 3. 等待 task_started
            while True:
                resp = await ws.recv()
                response = json.loads(resp)
                if response.get("event") == "task_started":
                    break
            # 发送task_continue
            await ws.send(json.dumps({"event": "task_continue", "text": text}))
            self.pcm_buffer.clear()
            
            # 记录音频输出时间（第一帧）
            if self.first_audio_time is None:
                self.first_audio_time = time.time()
                logger.bind(tag=TAG).info(f"[minimax_single_stream] 第一句音频输出时间: {self.first_audio_time:.3f}, 延迟: {self.first_audio_time - self.first_text_time:.3f} 秒")
            
            # 每个分段都发送FIRST消息以显示文本（参考火山引擎的实现）
            # 这样确保每个分段的文本都能在设备屏幕上显示
            self.tts_audio_queue.put((SentenceType.FIRST, [], text))
            
            chunk_counter = 0
            hex_audio = ""
            while True:
                resp = await ws.recv()
                response = json.loads(resp)
                if "data" in response and "audio" in response["data"]:
                    audio = response["data"]["audio"]
                    hex_audio += audio
                    chunk_counter += 1
                if response.get("is_final"):
                    break
                    
            # 合并所有块，转为bytes
            audio_bytes = bytes.fromhex(hex_audio)
            logger.bind(tag=TAG).info(f"[minimax_single_stream] 收到音频数据大小: {len(audio_bytes)} bytes")
            # mp3转pcm并编码为opus帧
            opus_datas, _ = audio_bytes_to_data(audio_bytes, file_type="mp3", is_opus=True)
            logger.bind(tag=TAG).info(f"[minimax_single_stream] 本段音频opus帧数: {len(opus_datas)}")
            
            # 优化音频帧推送逻辑 - 与火山引擎保持一致的格式
            if opus_datas:  # 确保有音频数据才推送
                logger.bind(tag=TAG).info(f"[minimax_single_stream] 开始推送音频帧，总帧数: {len(opus_datas)}")
                # 采用与火山引擎一致的缓存策略：前10帧直接推送，其余缓存
                if len(opus_datas) <= 10:
                    # 全部直接推送，与火山引擎格式一致
                    logger.bind(tag=TAG).info(f"[minimax_single_stream] 推送全部帧: {len(opus_datas)}")
                    self.tts_audio_queue.put((SentenceType.MIDDLE, opus_datas, None))
                else:
                    # 前10帧直接推送
                    first_frames = opus_datas[:10]
                    logger.bind(tag=TAG).info(f"[minimax_single_stream] 推送前置帧: {len(first_frames)}")
                    self.tts_audio_queue.put((SentenceType.MIDDLE, first_frames, None))
                    # 剩余帧批量推送
                    cache = opus_datas[10:]
                    if cache:
                        logger.bind(tag=TAG).info(f"[minimax_single_stream] 推送缓存帧批次: {len(cache)}")
                        self.tts_audio_queue.put((SentenceType.MIDDLE, cache, None))
            else:
                logger.bind(tag=TAG).warning(f"[minimax_single_stream] 本段音频为空，跳过推送")
            
            # 如果是最后一段，推送结束标记
            if is_last:
                self.tts_audio_queue.put((SentenceType.LAST, [], None))
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"minimax ws流式TTS失败: {e}, 文本: {text}")
            logger.bind(tag=TAG).error(f"详细错误信息: {traceback.format_exc()}")
            # 出错时确保推送结束标记
            if not self.session_finished:
                self.tts_audio_queue.put((SentenceType.LAST, [], None))
        finally:
            if ws:
                await ws.close()
                logger.bind(tag=TAG).info(f"[minimax_single_stream] WebSocket连接已关闭，文本: {text}") 