import traceback

from ..base import MemoryProviderBase, logger
from mem0 import Memory

TAG = __name__


class MemoryProvider(MemoryProviderBase):
    def __init__(self, config, summary_memory=None):
        super().__init__(config)
        
        # 本地部署不需要api_key
        self.use_mem0 = True
        
        # 检查是否使用自定义配置（如Ollama）
        use_custom_config = "mem0_config" in config
        
        # 获取OpenAI API密钥（如果不是使用自定义配置）
        openai_api_key = ""
        if not use_custom_config:
            openai_api_key = config.get("openai_api_key", "")
            if not openai_api_key:
                # 如果配置中没有提供，尝试从环境变量获取
                import os
                openai_api_key = os.getenv("OPENAI_API_KEY", "")
            
            if not openai_api_key:
                logger.bind(tag=TAG).error("未找到OpenAI API密钥，请在配置中添加openai_api_key或设置OPENAI_API_KEY环境变量")
                self.use_mem0 = False
                return

        # 配置本地mem0
        if use_custom_config:
            # 使用自定义配置（如Ollama），从配置中获取完整的mem0配置
            mem0_config = config["mem0_config"].copy()
            # 确保vector_store配置存在
            if "vector_store" not in mem0_config:
                mem0_config["vector_store"] = {
                    "provider": config.get("vector_store", "chroma"),
                    "config": {
                        "collection_name": config.get("collection_name", "xiaozhi_memory"),
                        "path": config.get("vector_store_path", "./memory_db")
                    }
                }
        else:
            # 使用默认OpenAI配置
            mem0_config = {
                "llm": {
                    "provider": "openai",  # 可以配置为其他本地LLM
                    "config": {
                        "model": config.get("llm_model", "gpt-4o-mini"),
                        "temperature": config.get("temperature", 0.1),
                        "max_tokens": config.get("max_tokens", 1000),
                        "api_key": openai_api_key,
                    }
                },
                "embedder": {
                    "provider": "openai",  # 可以配置为其他embedder，包括阿里云
                    "config": {
                        "model": config.get("embedding_model", "text-embedding-ada-002"),
                        "api_key": openai_api_key,
                    }
                },
                "vector_store": {
                    "provider": config.get("vector_store", "chroma"),  # 默认使用chroma作为本地向量数据库
                    "config": {
                        "collection_name": config.get("collection_name", "xiaozhi_memory"),
                        "path": config.get("vector_store_path", "./memory_db")  # 本地数据库路径
                    }
                }
            }

        
        # 特殊处理阿里云embedding配置（仅在非自定义配置时）
        if not use_custom_config and config.get("use_aliyun_embedding", False):
            # 使用自定义配置方式来支持阿里云API
            logger.bind(tag=TAG).info("使用自定义配置支持阿里云embedding")
            
            # 获取阿里云API密钥
            aliyun_api_key = config.get("aliyun_api_key", "")
            if not aliyun_api_key:
                logger.bind(tag=TAG).error("使用阿里云embedding时，必须提供aliyun_api_key")
                self.use_mem0 = False
                return
            
            mem0_config = {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": config.get("llm_model", "qwen-turbo"),  # 使用阿里云模型
                        "temperature": config.get("temperature", 0.1),
                        "max_tokens": config.get("max_tokens", 1000),
                        "api_key": aliyun_api_key,  # LLM也使用阿里云API密钥
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": config.get("embedding_model", "text-embedding-v4"),
                        "api_key": aliyun_api_key,  # embedding使用阿里云API密钥
                        # 注意：mem0ai可能不支持base_url参数，我们先尝试不使用
                    }
                },
                "vector_store": {
                    "provider": config.get("vector_store", "chroma"),
                    "config": {
                        "collection_name": config.get("collection_name", "xiaozhi_memory"),
                        "path": config.get("vector_store_path", "./memory_db")
                    }
                }
            }
            # 尝试设置环境变量来支持阿里云API
            import os
            os.environ["OPENAI_API_BASE"] = config.get("aliyun_base_url", "https://dashscope.aliyuncs.com/compatible-mode/v1")
            logger.bind(tag=TAG).info(f"配置阿里云LLM模型: {config.get('llm_model', 'qwen-turbo')}")
            logger.bind(tag=TAG).info(f"配置阿里云embedding: {config.get('embedding_model', 'text-embedding-v4')}")
            logger.bind(tag=TAG).info(f"设置阿里云base_url环境变量: {config.get('aliyun_base_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1')}")

        try:
            # 使用持久化目录确保记忆保持
            if mem0_config["vector_store"]["provider"] == "chroma":
                import time
                import os
                
                # 使用固定的持久化目录，而不是临时目录
                persistent_dir = config.get("vector_store_path", "./memory_db")
                # 如果路径是相对路径，确保它在当前工作目录下
                if not os.path.isabs(persistent_dir):
                    persistent_dir = os.path.join(os.getcwd(), persistent_dir)
                
                # 添加用户标识符以支持多用户记忆隔离
                user_memory_dir = os.path.join(persistent_dir, f"user_{self.role_id}")
                mem0_config["vector_store"]["config"]["path"] = user_memory_dir
                
                # 确保目录存在
                os.makedirs(user_memory_dir, exist_ok=True)
                logger.bind(tag=TAG).info(f"使用持久化记忆目录: {user_memory_dir}")
                
                # 彻底清理ChromaDB全局状态
                self._force_clear_chromadb_state()
                
                # 等待一点时间确保清理完成
                import time
                time.sleep(0.1)
            
            # 修补mem0的create_col方法，让它自动创建集合
            self._patch_mem0_create_col()
            
            # 使用本地Memory类而不是云端MemoryClient
            self.client = Memory.from_config(mem0_config)
            logger.bind(tag=TAG).info("成功初始化本地 Mem0 服务")
        except Exception as e:
            logger.bind(tag=TAG).error(f"初始化本地 Mem0 服务时发生错误: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            self.use_mem0 = False

    def _patch_mem0_create_col(self):
        """修补mem0的create_col方法，让它在集合不存在时自动创建"""
        try:
            import mem0.vector_stores.chroma as chroma_module
            
            # 查找正确的类名
            chroma_class = None
            for attr_name in dir(chroma_module):
                attr = getattr(chroma_module, attr_name)
                if (hasattr(attr, 'create_col') and 
                    hasattr(attr, '__init__') and 
                    attr_name != 'create_col'):  # 排除方法本身
                    chroma_class = attr
                    logger.bind(tag=TAG).info(f"找到ChromaDB类: {attr_name}")
                    break
            
            if chroma_class is None:
                logger.bind(tag=TAG).warning("未找到ChromaDB向量存储类")
                return
            
            # 保存原始方法
            original_create_col = chroma_class.create_col
            
            def patched_create_col(self, name):
                """修补后的create_col方法，会自动创建不存在的集合"""
                try:
                    # 尝试获取集合
                    collection_info = self.client.get_collection(name=name)
                    return self.client.get_collection(name=name)
                except ValueError as e:
                    if "does not exist" in str(e):
                        # 集合不存在，自动创建
                        logger.bind(tag=TAG).info(f"集合不存在，自动创建: {name}")
                        return self.client.create_collection(
                            name=name,
                            metadata={"description": f"Auto-created collection {name}"}
                        )
                    else:
                        raise e
                except Exception as e:
                    # 其他错误，尝试创建集合
                    logger.bind(tag=TAG).warning(f"获取集合失败，尝试创建: {str(e)}")
                    try:
                        return self.client.create_collection(
                            name=name,
                            metadata={"description": f"Auto-created collection {name}"}
                        )
                    except Exception as create_e:
                        logger.bind(tag=TAG).error(f"创建集合也失败: {str(create_e)}")
                        raise create_e
            
            # 应用补丁
            chroma_class.create_col = patched_create_col
            logger.bind(tag=TAG).info(f"成功修补mem0的{chroma_class.__name__}.create_col方法")
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"修补mem0失败: {str(e)}")

    def _safe_create_collection(self, db_path, collection_name):
        """安全地预创建ChromaDB集合，避免与mem0冲突"""
        try:
            import chromadb
            import os
            
            # 确保路径存在
            os.makedirs(db_path, exist_ok=True)
            
            # 使用最简单的方式创建客户端
            client = chromadb.PersistentClient(path=db_path)
            
            try:
                # 检查集合是否存在
                client.get_collection(name=collection_name)
                logger.bind(tag=TAG).info(f"集合已存在: {collection_name}")
            except ValueError:
                # 集合不存在，创建它
                client.create_collection(
                    name=collection_name,
                    metadata={"description": "XiaoZhi Memory Collection"}
                )
                logger.bind(tag=TAG).info(f"安全创建集合: {collection_name}")
            
            # 立即关闭客户端连接，释放资源
            del client
            
            # 再次清理状态
            import gc
            gc.collect()
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"安全创建集合失败: {str(e)}")
            # 不抛出异常，让mem0自己尝试

    def _force_clear_chromadb_state(self):
        """彻底清理ChromaDB的全局状态和缓存"""
        try:
            import chromadb
            import gc
            import sys
            
            # 清理所有可能的全局状态
            modules_to_clear = []
            for module_name in sys.modules:
                if 'chromadb' in module_name:
                    modules_to_clear.append(module_name)
            
            # 清理ChromaDB的各种全局缓存
            for attr_name in ['_instances', '_clients', '_systems']:
                if hasattr(chromadb.api.client, attr_name):
                    cache = getattr(chromadb.api.client, attr_name)
                    if hasattr(cache, 'clear'):
                        cache.clear()
                        logger.bind(tag=TAG).debug(f"清理ChromaDB.{attr_name}")
                
                if hasattr(chromadb.api.client, 'SharedSystemClient'):
                    if hasattr(chromadb.api.client.SharedSystemClient, attr_name):
                        cache = getattr(chromadb.api.client.SharedSystemClient, attr_name)
                        if hasattr(cache, 'clear'):
                            cache.clear()
                            logger.bind(tag=TAG).debug(f"清理SharedSystemClient.{attr_name}")
            
            # 强制垃圾回收
            gc.collect()
            logger.bind(tag=TAG).info("彻底清理ChromaDB全局状态完成")
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"清理ChromaDB全局状态时出现警告: {str(e)}")

    def _create_collection_with_mem0_settings(self, vector_config):
        """使用与mem0完全相同的设置预先创建集合"""
        try:
            import chromadb
            import os
            
            # 获取配置
            collection_name = vector_config.get("collection_name", "xiaozhi_memory")
            db_path = vector_config.get("path", "./memory_db")
            
            # 确保目录存在
            os.makedirs(db_path, exist_ok=True)
            
            # 模拟mem0的设置创建ChromaDB客户端
            settings = chromadb.config.Settings(
                persist_directory=db_path,
                is_persistent=True
            )
            
            # 使用与mem0相同的客户端创建方式
            client = chromadb.Client(settings)
            
            try:
                # 检查集合是否已存在
                collection = client.get_collection(name=collection_name)
                logger.bind(tag=TAG).info(f"找到现有集合: {collection_name}")
            except ValueError:
                # 集合不存在，创建新集合
                logger.bind(tag=TAG).info(f"使用mem0设置创建新集合: {collection_name}")
                collection = client.create_collection(
                    name=collection_name,
                    metadata={"description": "XiaoZhi Memory Collection for Mem0"}
                )
                logger.bind(tag=TAG).info(f"成功创建集合: {collection_name}")
            
            # 显式关闭客户端连接
            del client
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"使用mem0设置预创建集合失败: {str(e)}")
            logger.bind(tag=TAG).debug(f"详细错误: {traceback.format_exc()}")

    def _clear_chromadb_global_state(self):
        """清理ChromaDB的全局状态，避免实例冲突"""
        try:
            import chromadb
            import gc
            
            # 清理ChromaDB的全局客户端缓存
            if hasattr(chromadb.api.client, '_instances'):
                chromadb.api.client._instances.clear()
                logger.bind(tag=TAG).info("清理ChromaDB全局实例缓存")
            
            # 清理SharedSystemClient的全局状态
            if hasattr(chromadb.api.client, 'SharedSystemClient'):
                if hasattr(chromadb.api.client.SharedSystemClient, '_instances'):
                    chromadb.api.client.SharedSystemClient._instances.clear()
                    logger.bind(tag=TAG).info("清理SharedSystemClient全局实例缓存")
            
            # 强制垃圾回收
            gc.collect()
            logger.bind(tag=TAG).info("完成ChromaDB全局状态清理")
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"清理ChromaDB全局状态时出现警告: {str(e)}")

    def _create_chroma_collection_for_mem0(self, vector_config):
        """为mem0预先创建ChromaDB集合，避免初始化时找不到集合"""
        try:
            import chromadb
            import os
            
            # 获取配置
            collection_name = vector_config.get("collection_name", "xiaozhi_memory")
            db_path = vector_config.get("path", "./memory_db")
            
            # 确保目录存在
            os.makedirs(db_path, exist_ok=True)
            
            # 创建PersistentClient（与mem0使用相同的客户端类型）
            client = chromadb.PersistentClient(path=db_path)
            
            try:
                # 检查集合是否已存在
                collection = client.get_collection(name=collection_name)
                logger.bind(tag=TAG).info(f"找到现有集合: {collection_name}")
            except ValueError:
                # 集合不存在，创建新集合
                logger.bind(tag=TAG).info(f"为mem0创建新集合: {collection_name}")
                collection = client.create_collection(
                    name=collection_name,
                    metadata={"description": "XiaoZhi Memory Collection for Mem0"}
                )
                logger.bind(tag=TAG).info(f"成功为mem0创建集合: {collection_name}")
            
            # 显式关闭客户端连接，让mem0可以重新连接
            del client
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"预创建mem0集合失败（可能不影响后续使用）: {str(e)}")

    def _ensure_chroma_collection(self, vector_config):
        """确保ChromaDB集合存在，如果不存在则创建"""
        try:
            import chromadb
            import os
            import shutil
            
            # 获取配置
            collection_name = vector_config.get("collection_name", "xiaozhi_memory")
            db_path = vector_config.get("path", "./memory_db")
            
            # 确保目录存在
            os.makedirs(db_path, exist_ok=True)
            
            try:
                # 创建ChromaDB客户端
                client = chromadb.PersistentClient(path=db_path)
                
                try:
                    # 尝试获取集合
                    collection = client.get_collection(name=collection_name)
                    logger.bind(tag=TAG).info(f"找到现有集合: {collection_name}")
                except Exception:
                    # 集合不存在，创建新集合
                    logger.bind(tag=TAG).info(f"创建新集合: {collection_name}")
                    collection = client.create_collection(
                        name=collection_name,
                        metadata={"description": "XiaoZhi Memory Collection"}
                    )
                    logger.bind(tag=TAG).info(f"成功创建集合: {collection_name}")
                    
            except ValueError as ve:
                if "different settings" in str(ve):
                    logger.bind(tag=TAG).warning(f"ChromaDB实例冲突，尝试使用备用路径")
                    # 使用带时间戳的备用路径
                    import time
                    backup_path = f"{db_path}_backup_{int(time.time())}"
                    logger.bind(tag=TAG).info(f"使用备用路径: {backup_path}")
                    
                    # 更新配置中的路径
                    vector_config["path"] = backup_path
                    os.makedirs(backup_path, exist_ok=True)
                    
                    # 使用新路径重新创建客户端
                    client = chromadb.PersistentClient(path=backup_path)
                    collection = client.create_collection(
                        name=collection_name,
                        metadata={"description": "XiaoZhi Memory Collection (Backup)"}
                    )
                    logger.bind(tag=TAG).info(f"在备用路径成功创建集合: {collection_name}")
                else:
                    raise ve
                
        except Exception as e:
            logger.bind(tag=TAG).warning(f"预创建集合失败（可能不影响后续使用）: {str(e)}")

    async def save_memory(self, msgs):
        if not self.use_mem0:
            return None
        if not msgs or len(msgs) < 2:
            return None

        try:
            # Format the content as a message list for mem0
            messages = []
            for message in msgs:
                if hasattr(message, 'role') and hasattr(message, 'content'):
                    if message.role != "system" and message.content:
                        messages.append({
                            "role": message.role, 
                            "content": str(message.content)
                        })
            
            if not messages:
                logger.bind(tag=TAG).debug("没有有效的消息需要保存")
                return None
                
            # 本地版本的add方法
            result = self.client.add(messages, user_id=self.role_id)
            logger.bind(tag=TAG).debug(f"Save memory result: {result}")
            return result
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存记忆失败: {str(e)}")
            logger.bind(tag=TAG).debug(f"保存记忆失败详细错误: {traceback.format_exc()}")
            return None

    async def query_memory(self, query: str) -> str:
        if not self.use_mem0:
            return ""
        try:
            # 本地版本的search方法
            results = self.client.search(query, user_id=self.role_id, limit=10)
            
            if not results or "results" not in results:
                return ""

            # Format each memory entry
            memories = []
            for entry in results["results"]:
                memory = entry.get("memory", "")
                score = entry.get("score", 0)  # 本地版本可能有相似度分数
                
                if memory:
                    # 本地版本可能没有updated_at字段，使用created_at或其他时间字段
                    timestamp = entry.get("created_at", entry.get("timestamp", ""))
                    if timestamp:
                        try:
                            # Parse and reformat the timestamp
                            if isinstance(timestamp, str):
                                dt = timestamp.split(".")[0]  # Remove milliseconds
                                formatted_time = dt.replace("T", " ")
                            else:
                                formatted_time = str(timestamp)
                        except:
                            formatted_time = str(timestamp)
                        memories.append((timestamp, f"[{formatted_time}] {memory}"))
                    else:
                        memories.append(("", memory))

            # Sort by timestamp if available
            if memories and memories[0][0]:
                memories.sort(key=lambda x: x[0], reverse=True)

            # Extract only the formatted strings
            if memories and memories[0][0]:  # 有时间戳
                memories_str = "\n".join(f"- {memory[1]}" for memory in memories)
            else:  # 无时间戳
                memories_str = "\n".join(f"- {memory[1] if isinstance(memory, tuple) else memory}" for memory in memories)
                
            logger.bind(tag=TAG).debug(f"Query results: {memories_str}")
            return memories_str
        except Exception as e:
            logger.bind(tag=TAG).error(f"查询记忆失败: {str(e)}")
            return ""

    async def get_all_memories(self) -> dict:
        """获取用户的所有记忆记录"""
        if not self.use_mem0:
            return {"memories": [], "total": 0}
        
        try:
            # 使用mem0的get_all方法获取所有记忆
            results = self.client.get_all(user_id=self.role_id)
            
            if not results:
                return {"memories": [], "total": 0}
            
            memories = []
            for entry in results:
                memory_info = {
                    "id": entry.get("id", ""),
                    "memory": entry.get("memory", ""),
                    "created_at": entry.get("created_at", ""),
                    "updated_at": entry.get("updated_at", ""),
                    "metadata": entry.get("metadata", {})
                }
                memories.append(memory_info)
            
            # 按创建时间排序（最新的在前面）
            memories.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            logger.bind(tag=TAG).info(f"成功获取 {len(memories)} 条记忆记录")
            return {"memories": memories, "total": len(memories)}
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取所有记忆失败: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            return {"memories": [], "total": 0}

    async def delete_memory(self, memory_id: str) -> bool:
        """删除指定的记忆记录"""
        if not self.use_mem0:
            return False
        
        try:
            # 使用mem0的delete方法删除记忆
            result = self.client.delete(memory_id=memory_id)
            logger.bind(tag=TAG).info(f"成功删除记忆记录: {memory_id}")
            return True
        except Exception as e:
            logger.bind(tag=TAG).error(f"删除记忆失败: {str(e)}")
            return False

    async def clear_all_memories(self) -> bool:
        """清空用户的所有记忆记录"""
        if not self.use_mem0:
            return False
        
        try:
            # 先获取所有记忆
            all_memories = await self.get_all_memories()
            if all_memories["total"] == 0:
                logger.bind(tag=TAG).info("没有记忆记录需要清空")
                return True
            
            # 逐个删除
            deleted_count = 0
            for memory in all_memories["memories"]:
                if memory.get("id"):
                    success = await self.delete_memory(memory["id"])
                    if success:
                        deleted_count += 1
            
            logger.bind(tag=TAG).info(f"成功清空 {deleted_count} 条记忆记录")
            return deleted_count == all_memories["total"]
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"清空记忆失败: {str(e)}")
            return False 