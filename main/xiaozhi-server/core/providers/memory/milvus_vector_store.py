"""
Milvus向量存储适配器
用于替换ChromaDB，提供向量存储和检索功能
"""
import logging
import traceback
from typing import List, Dict, Any, Optional
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import numpy as np
import json
import time

logger = logging.getLogger(__name__)

class MilvusVectorStore:
    """Milvus向量存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Milvus向量存储
        
        Args:
            config: 配置字典，包含host, port, collection_name等
        """
        self.host = config.get("host", "localhost")
        self.port = config.get("port", 19530)
        self.collection_name = config.get("collection_name", "xiaozhi_memory")
        self.dimension = config.get("dimension", 1536)  # 默认OpenAI embedding维度
        self.metric_type = config.get("metric_type", "COSINE")
        self.index_type = config.get("index_type", "HNSW")
        
        # 连接参数
        self.connection_alias = f"milvus_{int(time.time())}"
        
        # 初始化连接和集合
        self._connect()
        self._ensure_collection()
        
    def _connect(self):
        """连接到Milvus服务器"""
        try:
            connections.connect(
                alias=self.connection_alias,
                host=self.host,
                port=self.port
            )
            logger.info(f"成功连接到Milvus服务器 {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接Milvus失败: {str(e)}")
            raise
    
    def _ensure_collection(self):
        """确保集合存在，如果不存在则创建"""
        try:
            # 检查集合是否存在
            if utility.has_collection(self.collection_name, using=self.connection_alias):
                logger.info(f"集合 {self.collection_name} 已存在")
                self.collection = Collection(self.collection_name, using=self.connection_alias)
            else:
                # 创建新集合
                self._create_collection()
                
            # 加载集合到内存
            self.collection.load()
            logger.info(f"集合 {self.collection_name} 已加载到内存")
            
        except Exception as e:
            logger.error(f"确保集合存在失败: {str(e)}")
            raise
    
    def _create_collection(self):
        """创建新的集合"""
        try:
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.dimension),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="user_id", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="timestamp", dtype=DataType.INT64)
            ]
            
            # 创建集合schema
            schema = CollectionSchema(
                fields=fields,
                description=f"XiaoZhi memory collection for vector storage"
            )
            
            # 创建集合
            self.collection = Collection(
                name=self.collection_name,
                schema=schema,
                using=self.connection_alias
            )
            
            # 创建索引
            index_params = {
                "metric_type": self.metric_type,
                "index_type": self.index_type,
                "params": {"M": 16, "efConstruction": 256}
            }
            
            self.collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            logger.info(f"成功创建集合 {self.collection_name}")
            
        except Exception as e:
            logger.error(f"创建集合失败: {str(e)}")
            raise
    
    def add_vectors(self, vectors: List[List[float]], texts: List[str], 
                   metadatas: List[Dict] = None, ids: List[str] = None, 
                   user_id: str = "default") -> List[str]:
        """
        添加向量到集合
        
        Args:
            vectors: 向量列表
            texts: 文本列表
            metadatas: 元数据列表
            ids: ID列表，如果为None则自动生成
            user_id: 用户ID
            
        Returns:
            插入的ID列表
        """
        try:
            if not vectors or not texts:
                raise ValueError("向量和文本不能为空")
            
            if len(vectors) != len(texts):
                raise ValueError("向量和文本数量不匹配")
            
            # 生成ID
            if ids is None:
                ids = [f"{user_id}_{int(time.time() * 1000000)}_{i}" for i in range(len(vectors))]
            
            # 处理元数据
            if metadatas is None:
                metadatas = [{}] * len(vectors)
            
            # 准备数据
            entities = [
                ids,
                vectors,
                texts,
                [json.dumps(meta, ensure_ascii=False) for meta in metadatas],
                [user_id] * len(vectors),
                [int(time.time() * 1000)] * len(vectors)
            ]
            
            # 插入数据
            insert_result = self.collection.insert(entities)
            
            # 刷新以确保数据持久化
            self.collection.flush()
            
            logger.info(f"成功插入 {len(vectors)} 个向量")
            return ids
            
        except Exception as e:
            logger.error(f"添加向量失败: {str(e)}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            raise
    
    def search_vectors(self, query_vector: List[float], top_k: int = 10, 
                      user_id: str = None) -> List[Dict]:
        """
        搜索相似向量
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            user_id: 用户ID过滤
            
        Returns:
            搜索结果列表
        """
        try:
            # 构建搜索参数
            search_params = {
                "metric_type": self.metric_type,
                "params": {"ef": 64}
            }
            
            # 构建过滤表达式
            expr = None
            if user_id:
                expr = f'user_id == "{user_id}"'
            
            # 执行搜索
            results = self.collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["text", "metadata", "user_id", "timestamp"]
            )
            
            # 处理结果
            search_results = []
            for hits in results:
                for hit in hits:
                    result = {
                        "id": hit.id,
                        "score": hit.score,
                        "text": hit.entity.get("text"),
                        "metadata": json.loads(hit.entity.get("metadata", "{}")),
                        "user_id": hit.entity.get("user_id"),
                        "timestamp": hit.entity.get("timestamp")
                    }
                    search_results.append(result)
            
            logger.info(f"搜索返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {str(e)}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            raise
    
    def delete_vectors(self, ids: List[str]) -> bool:
        """
        删除向量
        
        Args:
            ids: 要删除的ID列表
            
        Returns:
            是否成功
        """
        try:
            if not ids:
                return True
            
            # 构建删除表达式
            id_list = "', '".join(ids)
            expr = f"id in ['{id_list}']"
            
            # 执行删除
            self.collection.delete(expr)
            
            # 刷新以确保删除生效
            self.collection.flush()
            
            logger.info(f"成功删除 {len(ids)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"删除向量失败: {str(e)}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def update_vector(self, vector_id: str, vector: List[float] = None, 
                     text: str = None, metadata: Dict = None) -> bool:
        """
        更新向量（通过删除后重新插入实现）
        
        Args:
            vector_id: 向量ID
            vector: 新向量
            text: 新文本
            metadata: 新元数据
            
        Returns:
            是否成功
        """
        try:
            # 先获取原始数据
            results = self.collection.query(
                expr=f'id == "{vector_id}"',
                output_fields=["vector", "text", "metadata", "user_id", "timestamp"]
            )
            
            if not results:
                logger.warning(f"未找到ID为 {vector_id} 的向量")
                return False
            
            original = results[0]
            
            # 准备更新数据
            new_vector = vector if vector is not None else original["vector"]
            new_text = text if text is not None else original["text"]
            new_metadata = metadata if metadata is not None else json.loads(original["metadata"])
            user_id = original["user_id"]
            
            # 删除原向量
            self.delete_vectors([vector_id])
            
            # 插入新向量
            self.add_vectors(
                vectors=[new_vector],
                texts=[new_text],
                metadatas=[new_metadata],
                ids=[vector_id],
                user_id=user_id
            )
            
            logger.info(f"成功更新向量 {vector_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新向量失败: {str(e)}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def get_collection_stats(self) -> Dict:
        """获取集合统计信息"""
        try:
            stats = self.collection.num_entities
            return {
                "collection_name": self.collection_name,
                "total_entities": stats,
                "dimension": self.dimension,
                "metric_type": self.metric_type,
                "index_type": self.index_type
            }
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {str(e)}")
            return {}
    
    def close(self):
        """关闭连接"""
        try:
            connections.disconnect(self.connection_alias)
            logger.info("Milvus连接已关闭")
        except Exception as e:
            logger.error(f"关闭Milvus连接失败: {str(e)}")
