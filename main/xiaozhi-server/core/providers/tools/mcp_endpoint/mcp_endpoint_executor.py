"""MCP接入点工具执行器"""

import json
from typing import Dict, Any
from ..base import ToolType, ToolDefinition, ToolExecutor
from plugins_func.register import Action, ActionResponse
from .mcp_endpoint_handler import call_mcp_endpoint_tool


class MCPEndpointExecutor(ToolExecutor):
    """MCP接入点工具执行器"""

    def __init__(self, conn):
        self.conn = conn
        self._dynamic_tools = {}  # 用于动态注册的工具

    async def execute(
        self, conn, tool_name: str, arguments: Dict[str, Any]
    ) -> ActionResponse:
        """执行MCP接入点工具"""
        from config.logger import setup_logging
        logger = setup_logging()

        # 详细诊断日志
        logger.info(f"🔧 MCP Endpoint执行器诊断:")
        logger.info(f"  - 工具名称: {tool_name}")
        logger.info(f"  - 参数: {arguments}")
        logger.info(f"  - hasattr(conn, 'mcp_endpoint_client'): {hasattr(conn, 'mcp_endpoint_client')}")

        if hasattr(conn, "mcp_endpoint_client"):
            logger.info(f"  - conn.mcp_endpoint_client is not None: {conn.mcp_endpoint_client is not None}")
            if conn.mcp_endpoint_client:
                is_ready = await conn.mcp_endpoint_client.is_ready()
                logger.info(f"  - mcp_endpoint_client.is_ready(): {is_ready}")
                logger.info(f"  - websocket存在: {conn.mcp_endpoint_client.websocket is not None}")

        if not hasattr(conn, "mcp_endpoint_client") or not conn.mcp_endpoint_client:
            logger.error(f"❌ MCP接入点客户端未初始化 - 工具: {tool_name}")
            return ActionResponse(
                action=Action.ERROR,
                response="MCP接入点客户端未初始化",
            )

        if not await conn.mcp_endpoint_client.is_ready():
            logger.error(f"❌ MCP接入点客户端未准备就绪 - 工具: {tool_name}")
            return ActionResponse(
                action=Action.RESPONSE,
                response="MCP接入点客户端未准备就绪",
            )

        # 检查工具是否存在
        # if not conn.mcp_endpoint_client.has_tool(tool_name):
        #     logger.error(f"❌ MCP接入点工具不存在: {tool_name}")
        #     return ActionResponse(
        #         action=Action.RESPONSE,
        #         response="暂时没有学习这个技能，请先让我学习此技能",
        #     )

        try:
            # 直接传递字典参数，不转换为JSON字符串
            logger.info(f"🚀 开始调用MCP接入点工具: {tool_name}, 参数: {arguments}")

            # 调用MCP接入点工具，直接传递字典参数
            result = await call_mcp_endpoint_tool(
                conn.mcp_endpoint_client, tool_name, arguments
            )

            logger.info(f"✅ MCP接入点工具调用完成: {tool_name}, 结果类型: {type(result)}")

            resultJson = None
            if isinstance(result, str):
                try:
                    resultJson = json.loads(result)
                    logger.info(f"📄 解析结果JSON成功: {resultJson}")
                except Exception as e:
                    logger.info(f"📄 结果不是JSON格式，作为字符串处理: {result[:200]}...")

            # 视觉大模型不经过二次LLM处理
            if (
                resultJson is not None
                and isinstance(resultJson, dict)
                and "action" in resultJson
            ):
                logger.info(f"🎯 返回直接动作: {resultJson['action']}")
                return ActionResponse(
                    action=Action[resultJson["action"]],
                    response=resultJson.get("response", ""),
                )

            logger.info(f"🔄 返回REQLLM动作，结果: {str(result)[:200]}...")
            return ActionResponse(action=Action.REQLLM, result=str(result))

        except ValueError as e:
            # ValueError通常表示工具不存在或参数错误，直接返回给用户
            error_msg = str(e)
            logger.error(f"❌ ValueError in MCP endpoint tool {tool_name}: {error_msg}")
            if "暂时没有学习这个技能" in error_msg:
                # 工具不存在，直接返回给用户，不让LLM重试
                return ActionResponse(action=Action.RESPONSE, response=error_msg)
            else:
                # 参数错误，让LLM重试
                return ActionResponse(action=Action.REQLLM, result=error_msg)
        except Exception as e:
            # 检查是否是"暂时没有学习这个技能"的错误
            error_msg = str(e)
            if "暂时没有学习这个技能" in error_msg:
                logger.error(f"❌ MCP endpoint tool {tool_name} not available: {error_msg}")
                # 工具不存在，直接返回给用户，避免LLM重复调用
                return ActionResponse(action=Action.RESPONSE, response=error_msg)
            else:
                # 其他异常（如网络错误、超时等），可能是临时性的，让LLM重试
                logger.error(f"❌ Exception in MCP endpoint tool {tool_name}: {e}")
                return ActionResponse(action=Action.REQLLM, result=f"工具调用出错：{str(e)}")

    def get_tools(self) -> Dict[str, ToolDefinition]:
        """获取所有MCP接入点工具（只返回动态注册的工具）"""
        print(f"[MCPEndpointExecutor] get_tools返回: {list(self._dynamic_tools.keys())}")
        return self._dynamic_tools.copy()

    def has_tool(self, tool_name: str) -> bool:
        return tool_name in self._dynamic_tools

    def clear_tools(self):
        """清空所有动态注册的MCP工具"""
        self._dynamic_tools = {}

    def register_tools(self, tools: list):
        """注册一批MCP工具（tools为OpenAI function call格式的列表）"""
        from config.logger import setup_logging
        logger = setup_logging()

        self._dynamic_tools = {}
        logger.info(f"🔧 开始注册 {len(tools)} 个MCP接入点工具")

        for i, tool in enumerate(tools):
            # 兼容两种格式：OpenAI function call格式和带function字段的格式
            if "function" in tool:
                func_def = tool["function"]
            else:
                func_def = tool
            tool_name = func_def.get("fullName", "")

            if tool_name:
                # 统一description格式
                if "function" in tool:
                    desc = tool
                else:
                    desc = {
                        "type": "function",
                        "function": func_def
                    }
                self._dynamic_tools[tool_name] = ToolDefinition(
                    name=tool_name, description=desc, tool_type=ToolType.MCP_ENDPOINT
                )
                logger.debug(f"✅ 注册工具 #{i+1}: {tool_name}")
            else:
                logger.warning(f"⚠️ 工具 #{i+1} 缺少fullName字段，跳过注册: {func_def}")

        logger.info(f"📋 MCP接入点工具注册完成，共注册 {len(self._dynamic_tools)} 个工具: {list(self._dynamic_tools.keys())}")

    def append_tools(self, tools: list):
        """追加MCP工具（不清空现有工具）"""
        from config.logger import setup_logging
        logger = setup_logging()

        existing_count = len(self._dynamic_tools)
        new_tools_added = 0

        logger.info(f"🔧 开始追加 {len(tools)} 个MCP接入点工具，当前已有 {existing_count} 个工具")

        for i, tool in enumerate(tools):
            # 兼容两种格式：OpenAI function call格式和带function字段的格式
            if "function" in tool:
                func_def = tool["function"]
            else:
                func_def = tool
            tool_name = func_def.get("fullName", "")

            if tool_name:
                if tool_name not in self._dynamic_tools:
                    # 统一description格式
                    if "function" in tool:
                        desc = tool
                    else:
                        desc = {
                            "type": "function",
                            "function": func_def
                        }
                    self._dynamic_tools[tool_name] = ToolDefinition(
                        name=tool_name, description=desc, tool_type=ToolType.MCP_ENDPOINT
                    )
                    new_tools_added += 1
                    logger.debug(f"✅ 追加新工具 #{i+1}: {tool_name}")
                else:
                    logger.debug(f"⏭️ 工具 #{i+1} 已存在，跳过: {tool_name}")
            else:
                logger.warning(f"⚠️ 工具 #{i+1} 缺少fullName字段，跳过追加: {func_def}")

        logger.info(f"📋 MCP接入点工具追加完成，新增 {new_tools_added} 个工具，总计 {len(self._dynamic_tools)} 个工具: {list(self._dynamic_tools.keys())}")

    def clear_tools(self):
        """清空所有动态工具"""
        from config.logger import setup_logging
        logger = setup_logging()

        old_count = len(self._dynamic_tools)
        self._dynamic_tools = {}
        logger.info(f"🧹 清空MCP接入点工具，已清除 {old_count} 个工具")
