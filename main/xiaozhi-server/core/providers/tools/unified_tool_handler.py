"""统一工具处理器"""

import json
from typing import Dict, List, Any, Optional
from config.logger import setup_logging
from plugins_func.loadplugins import auto_import_modules
from mcp_rag.main_simple import query_tools_json
from .base import ToolType
from plugins_func.register import Action, ActionResponse
from .unified_tool_manager import ToolManager
from .server_plugins import ServerPluginExecutor
from .server_mcp import ServerMCPExecutor
from .device_iot import DeviceIoTExecutor
from .device_mcp import DeviceMCPExecutor
from .mcp_endpoint import MCPEndpointExecutor
# 导入向量管理API
from mcp_rag.src.core.api.vector_management import VectorManagementAPI
from mcp_rag.src.core.api.vector_management import ToolSearchRequest


class UnifiedToolHandler:
    """统一工具处理器"""

    def __init__(self, conn):
        self.conn = conn
        self.config = conn.config
        self.logger = setup_logging()

        # 创建工具管理器
        self.tool_manager = ToolManager(conn)

        # 创建各类执行器
        self.server_plugin_executor = ServerPluginExecutor(conn)
        self.server_mcp_executor = ServerMCPExecutor(conn)
        self.device_iot_executor = DeviceIoTExecutor(conn)
        self.device_mcp_executor = DeviceMCPExecutor(conn)
        self.mcp_endpoint_executor = MCPEndpointExecutor(conn)

        # 注册执行器
        self.tool_manager.register_executor(
            ToolType.SERVER_PLUGIN, self.server_plugin_executor
        )
        self.tool_manager.register_executor(
            ToolType.SERVER_MCP, self.server_mcp_executor
        )
        self.tool_manager.register_executor(
            ToolType.DEVICE_IOT, self.device_iot_executor
        )
        self.tool_manager.register_executor(
            ToolType.DEVICE_MCP, self.device_mcp_executor
        )
        self.tool_manager.register_executor(
            ToolType.MCP_ENDPOINT, self.mcp_endpoint_executor
        )

        # 初始化标志
        self.finish_init = False

    async def _initialize(self):
        """异步初始化"""
        try:
            # 自动导入插件模块
            auto_import_modules("plugins_func.functions")

            # 检查工作模式，只有在干活模式下才初始化 MCP 相关功能
            work_mode = getattr(self.conn, 'work_mode', 'work')

            if work_mode == 'work':
                self.logger.info("工作模式：干活模式，初始化完整工具功能")

                # 初始化服务端MCP
                await self.server_mcp_executor.initialize()

                # 初始化MCP接入点
                await self._initialize_mcp_endpoint()

                # 初始化Home Assistant（如果需要）
                self._initialize_home_assistant()
            else:
                self.logger.info("工作模式：聊天模式，跳过MCP接入点初始化")

            self.finish_init = True
            self.logger.info("统一工具处理器初始化完成")

            # 输出当前支持的所有工具列表
            self.current_support_functions()

        except Exception as e:
            self.logger.error(f"统一工具处理器初始化失败: {e}")

    async def _initialize_mcp_endpoint(self):
        """初始化MCP接入点"""
        try:
            from .mcp_endpoint import connect_mcp_endpoint

            # 从配置中获取MCP接入点URL
            mcp_endpoint_url = self.config.get("mcp_endpoint", "")
            if (
                    mcp_endpoint_url
                    and "你的" not in mcp_endpoint_url
                    and mcp_endpoint_url != "null"
            ):
                self.logger.info(f"正在初始化MCP接入点: {mcp_endpoint_url}")
                mcp_endpoint_client = await connect_mcp_endpoint(
                    mcp_endpoint_url, self.conn
                )

                if mcp_endpoint_client:
                    # 将MCP接入点客户端保存到连接对象中
                    self.conn.mcp_endpoint_client = mcp_endpoint_client
                    self.logger.info("MCP接入点初始化成功")
                else:
                    self.logger.warning("MCP接入点初始化失败")

        except Exception as e:
            self.logger.error(f"初始化MCP接入点失败: {e}")

    def _initialize_home_assistant(self):
        """初始化Home Assistant提示词"""
        try:
            from plugins_func.functions.hass_init import append_devices_to_prompt

            append_devices_to_prompt(self.conn)
        except ImportError:
            pass  # 忽略导入错误
        except Exception as e:
            self.logger.error(f"初始化Home Assistant失败: {e}")

    def get_functions(self) -> List[Dict[str, Any]]:
        """获取所有工具的函数描述"""
        return self.tool_manager.get_function_descriptions()

    def current_support_functions(self) -> List[str]:
        """获取当前支持的函数名称列表"""
        func_names = self.tool_manager.get_supported_tool_names()

        # 详细记录工具分类信息
        self._log_tool_classification()

        self.logger.info(f"当前支持的函数列表: {func_names}")
        return func_names

    def _log_tool_classification(self):
        """记录工具分类详细信息，帮助理解工具合并情况"""
        try:
            all_tools = self.tool_manager.get_all_tools()

            # 按工具类型分类统计
            tool_stats = {}
            mcp_endpoint_tools = []
            other_tools = []

            for tool_name, tool_def in all_tools.items():
                tool_type = tool_def.tool_type.name
                if tool_type not in tool_stats:
                    tool_stats[tool_type] = 0
                tool_stats[tool_type] += 1

                if tool_type == 'MCP_ENDPOINT':
                    mcp_endpoint_tools.append(tool_name)
                else:
                    other_tools.append(tool_name)

            # 输出统计信息
            self.logger.info(f"🔧 工具分类统计:")
            for tool_type, count in tool_stats.items():
                self.logger.info(f"  - {tool_type}: {count} 个工具")

            # 输出MCP接入点工具（用户自己装的）
            if mcp_endpoint_tools:
                self.logger.info(f"📦 MCP接入点工具 ({len(mcp_endpoint_tools)} 个): {mcp_endpoint_tools[:3]}{'...' if len(mcp_endpoint_tools) > 3 else ''}")

            # 输出其他工具（包括检索到的默认工具）
            if other_tools:
                self.logger.info(f"🔍 其他工具 ({len(other_tools)} 个): {other_tools[:3]}{'...' if len(other_tools) > 3 else ''}")

        except Exception as e:
            self.logger.error(f"记录工具分类信息失败: {e}")

    def upload_functions_desc(self):
        """刷新函数描述列表"""
        self.tool_manager.refresh_tools()
        self.logger.info("函数描述列表已刷新")

    def has_tool(self, tool_name: str) -> bool:
        """检查是否有指定工具"""
        return self.tool_manager.has_tool(tool_name)

    async def handle_llm_function_call(
            self, conn, function_call_data: Dict[str, Any]
    ) -> Optional[ActionResponse]:
        """处理LLM函数调用"""
        try:
            function_name = function_call_data.get("name", "unknown")
            self.logger.info(f"🔧 收到function call请求: {function_name}")

            # 处理多函数调用
            if "function_calls" in function_call_data:
                self.logger.info(f"📋 处理多函数调用，数量: {len(function_call_data['function_calls'])}")
                responses = []
                for call in function_call_data["function_calls"]:
                    self.logger.info(f"🚀 执行工具: {call['name']}")
                    result = await self.tool_manager.execute_tool(
                        call["name"], call.get("arguments", {})
                    )
                    self.logger.info(f"✅ 工具执行完成: {call['name']}, 结果: {result}")
                    responses.append(result)
                return self._combine_responses(responses)

            # 处理单函数调用
            function_name = function_call_data["name"]
            arguments = function_call_data.get("arguments", {})

            # 如果arguments是字符串，尝试解析为JSON
            if isinstance(arguments, str):
                try:
                    parsed_args = json.loads(arguments) if arguments else {}
                    # 如果解析结果仍然是字符串，再次尝试解析（处理双重编码）
                    if isinstance(parsed_args, str):
                        try:
                            parsed_args = json.loads(parsed_args)
                            self.logger.info(f"📝 双重解析参数成功: {parsed_args}")
                        except json.JSONDecodeError:
                            self.logger.info(f"📝 单次解析参数成功（字符串结果）: {parsed_args}")
                    else:
                        self.logger.info(f"📝 解析参数成功: {parsed_args}")
                    arguments = parsed_args
                except json.JSONDecodeError as e:
                    # 将解析错误返回给大模型，让大模型重新尝试
                    return ActionResponse(
                        action=Action.REQLLM,
                        result=f"参数解析失败：{str(e)}，请检查参数格式",
                    )
            else:
                self.logger.info(f"📝 参数已经是字典类型: {arguments}")

            self.logger.info(f"🚀 执行单个工具: {function_name}, 参数: {arguments}")

            # 执行工具调用
            result = await self.tool_manager.execute_tool(function_name, arguments)
            self.logger.info(
                f"✅ 工具执行完成: {function_name}, 结果类型: {type(result)}, 动作: {getattr(result, 'action', 'unknown')}")
            return result

        except Exception as e:
            self.logger.error(f"❌ 处理function call错误: {e}")
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")
            return ActionResponse(action=Action.ERROR, response=str(e))

    def _combine_responses(self, responses: List[ActionResponse]) -> ActionResponse:
        """合并多个函数调用的响应"""
        if not responses:
            return ActionResponse(action=Action.NONE, response="无响应")

        # 如果有任何错误，返回第一个错误
        for response in responses:
            if response.action == Action.ERROR:
                return response

        # 合并所有成功的响应
        contents = []
        responses_text = []

        for response in responses:
            if response.content:
                contents.append(response.content)
            if response.response:
                responses_text.append(response.response)

        # 确定最终的动作类型
        final_action = Action.RESPONSE
        for response in responses:
            if response.action == Action.REQLLM:
                final_action = Action.REQLLM
                break

        return ActionResponse(
            action=final_action,
            result="; ".join(contents) if contents else None,
            response="; ".join(responses_text) if responses_text else None,
        )

    async def register_iot_tools(self, descriptors: List[Dict[str, Any]]):
        """注册IoT设备工具"""
        self.device_iot_executor.register_iot_tools(descriptors)
        self.tool_manager.refresh_tools()
        self.logger.info(f"注册了{len(descriptors)}个IoT设备的工具")

    def get_tool_statistics(self) -> Dict[str, int]:
        """获取工具统计信息"""
        return self.tool_manager.get_tool_statistics()

    async def cleanup(self):
        """清理资源"""
        try:
            await self.server_mcp_executor.cleanup()

            # 清理MCP接入点连接
            if (
                    hasattr(self.conn, "mcp_endpoint_client")
                    and self.conn.mcp_endpoint_client
            ):
                await self.conn.mcp_endpoint_client.close()

            self.logger.info("工具处理器清理完成")
        except Exception as e:
            self.logger.error(f"工具处理器清理失败: {e}")

    def _create_dummy_parameters(self) -> Dict[str, Any]:
        """创建默认的参数结构"""
        return {
            "type": "object",
            "properties": {
                "dummy": {
                    "type": "string",
                    "description": "占位参数，无实际意义"
                }
            },
            "required": []
        }

    def _patch_tool_parameters(self, tool: Dict[str, Any]) -> None:
        """补全单个工具的parameters"""
        func = tool.get("function")
        if not func:
            return

        params = func.get("parameters")
        tool_name = func.get('name', 'unknown')

        # 如果parameters不存在或不是dict，补全
        if not isinstance(params, dict):
            func["parameters"] = self._create_dummy_parameters()
            self.logger.debug(f"为工具 {tool_name} 添加了完整的parameters结构")
            return

        # 确保基本字段存在
        if "type" not in params:
            params["type"] = "object"

        # 如果properties不存在或为空
        if not params.get("properties"):
            params["properties"] = {
                "dummy": {
                    "type": "string",
                    "description": "占位参数，无实际意义"
                }
            }
            self.logger.debug(f"为工具 {tool_name} 添加了dummy参数")

        # 确保required字段存在
        if "required" not in params:
            params["required"] = []

    def _patch_tools_parameters(self, tools: list) -> list:
        """补全所有function的parameters，确保properties非空"""
        for tool in tools:
            self._patch_tool_parameters(tool)
        return tools

    async def before_llm_inference(self, query: str):
        """
        每次用户query前调用，向量检索服务返回相关MCP工具，动态追加到ToolManager。
        注意：MCP接入点工具（用户自己装的）和检索工具（默认接入点）会智能合并，避免重复。
        """
        try:
            # 检查工作模式，聊天模式下跳过工具检索
            work_mode = getattr(self.conn, 'work_mode', 'work')
            if work_mode == 'chat':
                self.logger.debug("聊天模式：跳过工具检索")
                return

            # 记录检索前的工具状态
            current_tools_before = self.tool_manager.get_supported_tool_names()
            self.logger.info(f"🔧 检索前已有工具数量: {len(current_tools_before)}")

            relevant_tools = await self.call_vector_search_service(query)
            self.logger.debug(f"向量检索返回 {len(relevant_tools)} 个工具")

            # 打印所有检索到的工具详情
            self.logger.info(f"🔍 向量检索结果 (共 {len(relevant_tools)} 个工具):")
            for i, tool in enumerate(relevant_tools):
                tool_name = tool.get('name', 'unknown')
                similarity_score = tool.get('similarity_score', 0)
                description = tool.get('description', 'No description')
                self.logger.info(f"  [{i + 1}] {tool_name} (相似度: {similarity_score:.3f}) - {description}")

            # 只保留 similarity_score > 0.3 的工具
            filtered_tools = [
                tool for tool in relevant_tools
                if tool.get('similarity_score', 0) > 0.3
            ]

            if not filtered_tools:
                self.logger.debug("没有满足相似度阈值的工具")
                return

            # 打印过滤后的工具列表
            self.logger.info(f"📋 过滤后的工具列表 (相似度 > 0.3, 共 {len(filtered_tools)} 个):")
            for i, tool in enumerate(filtered_tools):
                tool_name = tool.get('name', 'unknown')
                similarity_score = tool.get('similarity_score', 0)
                self.logger.info(f"  ✅ [{i + 1}] {tool_name} (相似度: {similarity_score:.3f})")

            # 补全parameters，确保所有function的parameters.properties非空
            filtered_tools = self._patch_tools_parameters(filtered_tools)

            # 使用智能追加模式，自动处理与MCP接入点工具的去重
            self.tool_manager.append_mcp_tools(filtered_tools)

            # 记录检索后的工具状态
            current_tools_after = self.tool_manager.get_supported_tool_names()
            added_count = len(current_tools_after) - len(current_tools_before)

            self.logger.info(f"🎯 工具检索完成: 检索到 {len(filtered_tools)} 个相关工具，实际新增 {added_count} 个工具")
            self.logger.info(f"📊 当前工具总数: {len(current_tools_after)} 个")

            # 输出当前工具列表概览（仅显示前几个）
            if current_tools_after:
                tool_preview = current_tools_after[:5]
                preview_text = ', '.join(tool_preview)
                if len(current_tools_after) > 5:
                    preview_text += f" ... (还有{len(current_tools_after) - 5}个)"
                self.logger.info(f"🛠️ 当前可用工具预览: {preview_text}")

        except Exception as e:
            self.logger.error(f"before_llm_inference内部异常: {e}")
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")



    async def call_vector_search_service(self, query: str) -> list:
        """
        实际调用向量检索，返回OpenAI function call格式的工具列表。
        """
        try:
            self.logger.info(f"🔍 开始调用向量检索服务，查询: {query}")
            self.logger.info(f"🔍 开始调用向量检索服务，查询: {query}")
            # 创建向量管理API实例（如果还没有的话）
            if not hasattr(self, '_vector_api'):
                # 创建向量管理API实例（不需要传递配置参数）
                self._vector_api = VectorManagementAPI()
            # 构建搜索请求
            search_request = ToolSearchRequest(
                query=query,
                top_k=12,
                filters=None,
                user_id=None
            )

            # 直接调用搜索方法
            api_response = await self._vector_api.search_similar_tools_method(search_request)
            if api_response.code == 0:
                # 提取搜索结果
                data = api_response.data.get("results", [])
                print("工具data=",data)
                self.logger.info(f"✅ 向量检索服务调用成功，返回 {len(data)} 个工具")
                return data
            else:
                self.logger.error(f"❌ 向量搜索失败: {api_response.msg}")
                return []
        except Exception as e:
            self.logger.error(f"❌ 向量检索服务调用失败: {e}")
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")
            return []