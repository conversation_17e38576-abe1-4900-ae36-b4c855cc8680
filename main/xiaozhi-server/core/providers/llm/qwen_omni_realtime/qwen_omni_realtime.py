import os
import json
import time
import base64
import asyncio
import websockets
from typing import Optional, Callable, Dict, Any, AsyncGenerator
from enum import Enum
from config.logger import setup_logging
from core.providers.llm.base import LLMProviderBase

TAG = __name__
logger = setup_logging()


class TurnDetectionMode(Enum):
    SERVER_VAD = "server_vad"
    MANUAL = "manual"


class LLMProvider(LLMProviderBase):
    """
    Qwen-Omni实时多模态LLM提供者
    作为标准LLM提供者，替代传统的文本LLM
    支持音频流式输入/输出、图像输入、实时对话

    注意：Qwen-Omni使用单次连接模式，每次对话完成后WebSocket连接会被服务器关闭
    这是正常的API行为，不是连接问题
    """
    
    def __init__(self, config):
        # 处理配置，过滤空字符串
        def get_config_value(key, default):
            value = config.get(key, default)
            # 如果值是空字符串，使用默认值
            if value == "" or value is None:
                return default
            return value

        self.api_key = get_config_value("api_key", None)
        self.model_name = get_config_value("model_name", "qwen-omni-turbo-realtime")
        self.base_url = get_config_value("base_url", "wss://dashscope.aliyuncs.com/api-ws/v1/realtime")

        # 语音配置
        self.voice = get_config_value("voice", "Ethan")  # Chelsie, Serena, Ethan, Cherry
        self.input_audio_format = get_config_value("input_audio_format", "pcm16")
        self.output_audio_format = get_config_value("output_audio_format", "pcm16")

        # 检测模式配置
        turn_detection = get_config_value("turn_detection_mode", "server_vad")
        self.turn_detection_mode = TurnDetectionMode.SERVER_VAD if turn_detection == "server_vad" else TurnDetectionMode.MANUAL

        # VAD参数配置
        self.vad_threshold = float(get_config_value("vad_threshold", 0.5))
        self.vad_prefix_padding_ms = int(get_config_value("vad_prefix_padding_ms", 300))
        self.vad_silence_duration_ms = int(get_config_value("vad_silence_duration_ms", 800))

        # 生成参数
        temperature_val = get_config_value("temperature", 0.8)
        self.temperature = float(temperature_val) if temperature_val != "" else 0.8
        self.max_response_output_tokens = get_config_value("max_response_output_tokens", "inf")
        
        # 连接状态
        self.ws = None
        self.session_id = None
        self.is_connected = False
        self._current_response_id = None
        self._is_responding = False
        self._last_activity = time.time()
        self._connection_lock = asyncio.Lock()
        self._connection_task = None
        self._keep_alive_task = None
        
        # 回调函数
        self.on_text_delta = None
        self.on_audio_delta = None
        self.on_input_transcript = None
        self.on_output_transcript = None
        self.on_interrupt = None
        self.on_response_done = None
        
        # 验证API Key
        if not self.api_key or not self.api_key.startswith("sk-"):
            logger.bind(tag=TAG).error("QwenOmniRealtime API Key格式不正确，应以sk-开头")

        # Qwen-Omni使用单次连接模式，每次对话后连接会关闭
        logger.bind(tag=TAG).info("Qwen-Omni LLM提供者初始化完成（单次连接模式）")

    async def connect(self) -> bool:
        """建立WebSocket连接"""
        try:
            # 构建带认证的URL
            url = f"{self.base_url}?model={self.model_name}"

            logger.bind(tag=TAG).info(f"连接到Qwen-Omni实时API: {url}")

            # 尝试不同的连接方式
            try:
                # 方式1: 使用extra_headers (新版本websockets)
                headers = {"Authorization": f"Bearer {self.api_key}"}
                self.ws = await websockets.connect(url, extra_headers=headers)
            except TypeError:
                # 方式2: 使用additional_headers (旧版本websockets)
                try:
                    headers = {"Authorization": f"Bearer {self.api_key}"}
                    self.ws = await websockets.connect(url, additional_headers=headers)
                except TypeError:
                    # 方式3: 在URL中包含认证信息
                    auth_url = f"{url}&authorization=Bearer%20{self.api_key}"
                    self.ws = await websockets.connect(auth_url)
            
            # 配置会话
            await self._configure_session()
            self.is_connected = True
            
            logger.bind(tag=TAG).info("Qwen-Omni实时连接建立成功")
            return True

        except Exception as e:
            logger.bind(tag=TAG).error(f"连接Qwen-Omni实时API失败: {e}")
            self.is_connected = False
            return False

    async def _ensure_connection(self):
        """确保WebSocket连接"""
        # 如果连接正常，直接返回
        if self.is_connected and self.ws:
            try:
                # 检查WebSocket是否仍然打开
                if hasattr(self.ws, 'closed') and not self.ws.closed:
                    self._last_activity = time.time()
                    logger.bind(tag=TAG).debug("连接状态正常，复用现有连接")
                    return True
                else:
                    logger.bind(tag=TAG).debug("WebSocket连接已关闭，这是Qwen-Omni API的正常行为")
                    self.is_connected = False
                    self.ws = None
            except Exception as e:
                logger.bind(tag=TAG).warning(f"连接状态检查失败: {e}")
                self.is_connected = False
                self.ws = None

        # 需要建立新连接
        async with self._connection_lock:
            # 双重检查，防止并发时重复连接
            if self.is_connected and self.ws:
                try:
                    if hasattr(self.ws, 'closed') and not self.ws.closed:
                        self._last_activity = time.time()
                        logger.bind(tag=TAG).debug("并发检查：连接仍然有效")
                        return True
                except:
                    pass

            logger.bind(tag=TAG).debug("建立新的Qwen-Omni连接...")
            success = await self.connect()
            if success:
                self._last_activity = time.time()
                # Qwen-Omni使用单次连接，不需要保活任务
                logger.bind(tag=TAG).debug("Qwen-Omni连接建立成功（单次使用）")
            return success

    async def _configure_session(self):
        """配置会话参数"""
        session_config = {
            "modalities": ["text", "audio"],
            "voice": self.voice,
            "input_audio_format": self.input_audio_format,
            "output_audio_format": self.output_audio_format,
            "input_audio_transcription": {
                "model": "gummy-realtime-v1"
            },
            "temperature": self.temperature,
            "max_response_output_tokens": self.max_response_output_tokens
        }
        
        # 配置VAD或手动模式 - 按照官方文档格式
        if self.turn_detection_mode == TurnDetectionMode.SERVER_VAD:
            session_config["turn_detection"] = {
                "type": "server_vad",
                "threshold": 0.5,  # 使用官方推荐值
                "prefix_padding_ms": 300,  # 使用官方推荐值
                "silence_duration_ms": 800,  # 使用官方推荐值
                "create_response": True,
                "interrupt_response": True
            }
        else:
            session_config["turn_detection"] = None

        logger.bind(tag=TAG).info(f"配置会话参数: {session_config}")

        await self._send_event({
            "type": "session.update",
            "session": session_config
        })

    async def _send_event(self, event: Dict[str, Any]):
        """发送事件到服务器"""
        if not self.ws:
            raise RuntimeError("WebSocket连接未建立")
        
        event['event_id'] = f"event_{int(time.time() * 1000)}"
        event_json = json.dumps(event, ensure_ascii=False)
        logger.bind(tag=TAG).debug(f"发送事件: {event['type']}")
        logger.bind(tag=TAG).info(f"WebSocket发送内容: {event_json[:300]}...")
        await self.ws.send(event_json)

    async def stream_audio(self, audio_chunk: bytes):
        """流式发送音频数据"""
        if not self.is_connected:
            raise RuntimeError("连接未建立")
        
        audio_b64 = base64.b64encode(audio_chunk).decode()
        await self._send_event({
            "type": "input_audio_buffer.append",
            "audio": audio_b64
        })

    async def append_image(self, image_data: bytes):
        """添加图像数据"""
        if not self.is_connected:
            raise RuntimeError("连接未建立")
        
        image_b64 = base64.b64encode(image_data).decode()
        await self._send_event({
            "type": "input_image_buffer.append",
            "image": image_b64
        })

    async def commit_audio_buffer(self):
        """提交音频缓冲区（Manual模式）"""
        await self._send_event({
            "type": "input_audio_buffer.commit"
        })

    async def create_response(self):
        """创建响应（Manual模式）"""
        await self._send_event({
            "type": "response.create",
            "response": {
                "modalities": ["text", "audio"]
            }
        })

    async def cancel_response(self):
        """取消当前响应"""
        await self._send_event({
            "type": "response.cancel"
        })

    async def handle_interruption(self):
        """处理用户打断"""
        if not self._is_responding:
            return
        
        logger.bind(tag=TAG).info("处理用户打断")
        if self._current_response_id:
            await self.cancel_response()
        
        self._is_responding = False
        self._current_response_id = None
        
        if self.on_interrupt:
            self.on_interrupt()

    async def handle_messages(self) -> AsyncGenerator[Dict[str, Any], None]:
        """处理服务器消息"""
        try:
            async for message in self.ws:
                event = json.loads(message)
                event_type = event.get("type")
                
                # 记录非音频增量事件
                if event_type != "response.audio.delta":
                    logger.bind(tag=TAG).debug(f"收到事件: {event_type}")
                
                # 处理各种事件
                if event_type == "error":
                    logger.bind(tag=TAG).error(f"服务器错误: {event.get('error')}")
                    yield {"type": "error", "data": event.get('error')}
                    
                elif event_type == "session.created":
                    self.session_id = event.get("session", {}).get("id")
                    logger.bind(tag=TAG).info(f"会话创建: {self.session_id}")
                    yield {"type": "session_created", "data": event}
                    
                elif event_type == "response.created":
                    self._current_response_id = event.get("response", {}).get("id")
                    self._is_responding = True
                    yield {"type": "response_started", "data": event}
                    
                elif event_type == "response.done":
                    self._is_responding = False
                    self._current_response_id = None
                    if self.on_response_done:
                        self.on_response_done(event)
                    yield {"type": "response_done", "data": event}
                    
                elif event_type == "input_audio_buffer.speech_started":
                    logger.bind(tag=TAG).info("检测到语音开始")
                    if self._is_responding:
                        await self.handle_interruption()
                    yield {"type": "speech_started", "data": event}
                    
                elif event_type == "input_audio_buffer.speech_stopped":
                    logger.bind(tag=TAG).info("检测到语音结束")
                    yield {"type": "speech_stopped", "data": event}
                    
                elif event_type == "response.text.delta":
                    text_delta = event.get("delta", "")
                    if self.on_text_delta:
                        self.on_text_delta(text_delta)
                    yield {"type": "text_delta", "data": text_delta}
                    
                elif event_type == "response.audio.delta":
                    audio_b64 = event.get("delta", "")
                    audio_bytes = base64.b64decode(audio_b64)
                    if self.on_audio_delta:
                        self.on_audio_delta(audio_bytes)
                    yield {"type": "audio_delta", "data": audio_bytes}
                    
                elif event_type == "conversation.item.input_audio_transcription.completed":
                    transcript = event.get("transcript", "")
                    if self.on_input_transcript:
                        self.on_input_transcript(transcript)
                    yield {"type": "input_transcript", "data": transcript}
                    
                elif event_type == "response.audio_transcript.delta":
                    transcript_delta = event.get("delta", "")
                    if self.on_output_transcript:
                        self.on_output_transcript(transcript_delta)
                    yield {"type": "output_transcript", "data": transcript_delta}
                    
                else:
                    # 其他事件
                    yield {"type": event_type, "data": event}
                    
        except websockets.exceptions.ConnectionClosed:
            logger.bind(tag=TAG).info("WebSocket连接已关闭")
            self.is_connected = False
        except Exception as e:
            logger.bind(tag=TAG).error(f"消息处理错误: {e}")
            self.is_connected = False

    async def close(self):
        """关闭连接"""
        if self.ws:
            await self.ws.close()
            self.is_connected = False
            logger.bind(tag=TAG).info("Qwen-Omni实时连接已关闭")

    # 实现基类方法
    def response(self, session_id, dialogue, **kwargs):
        """
        标准LLM响应方法
        将对话转换为Qwen-Omni的实时交互
        """
        try:
            # 使用同步方式处理异步调用
            import threading
            import queue

            result_queue = queue.Queue()
            error_queue = queue.Queue()

            def run_async():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    async def collect_response():
                        chunks = []
                        async for chunk in self._async_response(session_id, dialogue, **kwargs):
                            chunks.append(chunk)
                        return chunks

                    chunks = loop.run_until_complete(collect_response())
                    result_queue.put(chunks)
                    loop.close()
                except Exception as e:
                    error_queue.put(e)

            thread = threading.Thread(target=run_async)
            thread.start()
            thread.join(timeout=30)  # 30秒超时

            if not error_queue.empty():
                error = error_queue.get()
                logger.bind(tag=TAG).error(f"异步处理错误: {error}")
                yield f"【Qwen-Omni处理错误: {error}】"
                return

            if not result_queue.empty():
                chunks = result_queue.get()
                for chunk in chunks:
                    yield chunk
            else:
                yield "【Qwen-Omni响应超时】"

        except Exception as e:
            logger.bind(tag=TAG).error(f"Qwen-Omni响应生成错误: {e}")
            yield "【Qwen-Omni服务响应异常】"

    def response_with_functions(self, session_id, dialogue, functions=None):
        """
        带工具调用的响应方法
        Qwen-Omni实时模型不支持传统工具调用，使用提示词模拟
        """
        logger.bind(tag=TAG).warning("Qwen-Omni实时模型的工具调用支持有限")

        # 检查是否真的需要工具调用
        user_message = ""
        for msg in reversed(dialogue):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        # 工具调用关键词
        tool_keywords = [
            "打开", "启动", "运行", "执行",
            "创建", "新建", "生成", "制作",
            "搜索", "查找", "查询", "检索",
            "计算", "算", "统计", "分析",
            "文件", "文档", "表格", "图片",
            "发送", "邮件", "消息", "通知",
            "设置", "配置", "调整", "修改"
        ]

        needs_tools = any(keyword in user_message for keyword in tool_keywords)

        if needs_tools and functions:
            # 构建工具调用提示词
            tool_descriptions = []
            for func in functions:
                if isinstance(func, dict) and "function" in func:
                    func_info = func["function"]
                    name = func_info.get('name', 'unknown')
                    desc = func_info.get('description', '')
                    tool_descriptions.append(f"- {name}: {desc}")

            if tool_descriptions:
                # 修改对话，添加工具调用指导
                modified_dialogue = dialogue.copy()
                tool_prompt = f"""
当用户需要执行操作时，请按以下格式回复：
<tool_call>
工具名称: 具体工具名
参数: 相关参数
</tool_call>

可用工具：
{chr(10).join(tool_descriptions)}

请根据用户需求选择合适的工具。如果不需要工具，直接正常回复。
"""

                # 查找系统消息并更新
                for i, msg in enumerate(modified_dialogue):
                    if msg.get("role") == "system":
                        modified_dialogue[i]["content"] += "\n\n" + tool_prompt
                        break
                else:
                    # 如果没有系统消息，添加一个
                    modified_dialogue.insert(0, {"role": "system", "content": tool_prompt})

                # 使用修改后的对话
                for chunk in self.response(session_id, modified_dialogue):
                    yield chunk, None
                return

        # 没有工具需求时，使用普通响应
        for chunk in self.response(session_id, dialogue):
            yield chunk, None

    def set_callbacks(self, 
                     on_text_delta: Optional[Callable[[str], None]] = None,
                     on_audio_delta: Optional[Callable[[bytes], None]] = None,
                     on_input_transcript: Optional[Callable[[str], None]] = None,
                     on_output_transcript: Optional[Callable[[str], None]] = None,
                     on_interrupt: Optional[Callable[[], None]] = None,
                     on_response_done: Optional[Callable[[Dict], None]] = None):
        """设置回调函数"""
        self.on_text_delta = on_text_delta
        self.on_audio_delta = on_audio_delta
        self.on_input_transcript = on_input_transcript
        self.on_output_transcript = on_output_transcript
        self.on_interrupt = on_interrupt
        self.on_response_done = on_response_done

    def get_supported_voices(self):
        """获取支持的语音列表"""
        return ["Chelsie", "Serena", "Ethan", "Cherry"]

    def is_realtime_model(self):
        """标识这是一个实时模型"""
        return True

    async def _async_response(self, session_id, dialogue, **kwargs):
        """异步响应生成器"""
        try:
            # 确保连接
            if not await self._ensure_connection():
                yield "【Qwen-Omni连接失败】"
                return

            # 合并系统消息和用户消息
            # Qwen-Omni实时API可能不支持独立的系统消息，需要合并到用户消息中
            system_content = ""
            user_messages = []

            for msg in dialogue:
                role = msg.get("role")
                content = msg.get("content", "")

                if role == "system":
                    system_content += content + "\n\n"
                elif role == "user":
                    user_messages.append(content)
                elif role == "assistant":
                    # 暂时跳过助手消息，因为实时API可能不支持
                    pass

            # 获取最后一条用户消息
            if not user_messages:
                yield "【未找到用户消息】"
                return

            last_user_message = user_messages[-1]

            # 如果有系统消息，将其合并到用户消息前，使用更直接的格式
            if system_content.strip():
                # 使用更直接的角色扮演格式
                if "哆啦A梦" in system_content:
                    combined_message = f"请以哆啦A梦的身份回答：{last_user_message}"
                else:
                    combined_message = f"{system_content.strip()}\n\n{last_user_message}"
                logger.bind(tag=TAG).info(f"发送角色扮演消息")
                logger.bind(tag=TAG).info(f"完整消息内容: {combined_message}")  # 改为INFO级别，看看到底发送了什么
            else:
                combined_message = last_user_message
                logger.bind(tag=TAG).info(f"发送普通用户消息: {last_user_message}")

            # 发送合并后的消息
            logger.bind(tag=TAG).info(f"即将发送到WebSocket的消息: {combined_message}")
            await self._send_conversation_item(combined_message)

            # 等待一小段时间确保消息被处理
            await asyncio.sleep(0.1)

            # 创建响应，按照官方文档格式传递系统指令
            system_instructions = "You are a helpful assistant."

            # 提取系统消息作为指令
            for msg in dialogue:
                if msg.get("role") == "system":
                    content = msg.get("content", "")
                    if content.strip():
                        # 简化系统指令，只保留核心身份信息
                        if "哆啦A梦" in content:
                            system_instructions = "You are Doraemon, a cat-type robot from the 22nd century. Please respond in Chinese."
                        elif "助手" in content:
                            system_instructions = "You are a helpful AI assistant. Please respond in Chinese."
                        else:
                            # 截取前200个字符避免过长
                            system_instructions = content[:200] + ("..." if len(content) > 200 else "")
                    break

            logger.bind(tag=TAG).info("请求Qwen-Omni生成响应")
            logger.bind(tag=TAG).debug(f"系统指令: {system_instructions}")

            await self._send_event({
                "type": "response.create",
                "response": {
                    "instructions": system_instructions,
                    "modalities": ["text", "audio"]
                }
            })

            # 处理响应
            response_text = ""
            audio_chunks = []
            has_error = False

            # 设置超时
            timeout_seconds = 20
            start_time = time.time()

            async for message in self.ws:
                # 检查超时
                if time.time() - start_time > timeout_seconds:
                    logger.bind(tag=TAG).warning("Qwen-Omni响应超时")
                    if not response_text.strip():
                        yield "【响应超时，请稍后重试】"
                    break

                event = json.loads(message)
                event_type = event.get("type")

                # 添加详细的事件日志
                logger.bind(tag=TAG).debug(f"收到WebSocket事件: {event_type}")

                if event_type == "error":
                    error_info = event.get('error', {})
                    logger.bind(tag=TAG).error(f"服务器错误: {error_info}")
                    has_error = True
                    yield f"【服务器错误: {error_info}】"
                    break

                elif event_type == "response.text.delta":
                    text_delta = event.get("delta", "")
                    response_text += text_delta
                    logger.bind(tag=TAG).debug(f"收到文本增量: '{text_delta}'")
                    yield text_delta

                elif event_type == "response.audio_transcript.delta":
                    # Qwen-Omni发送的是音频转录增量
                    text_delta = event.get("delta", "")
                    response_text += text_delta
                    logger.bind(tag=TAG).debug(f"收到音频转录增量: '{text_delta}'")
                    yield text_delta

                elif event_type == "response.audio.delta":
                    # 音频数据可以传递给TTS系统
                    audio_b64 = event.get("delta", "")
                    if audio_b64:
                        audio_bytes = base64.b64decode(audio_b64)
                        audio_chunks.append(audio_bytes)
                        logger.bind(tag=TAG).debug(f"收到音频数据: {len(audio_bytes)} 字节")

                elif event_type == "response.done":
                    logger.bind(tag=TAG).info(f"Qwen-Omni响应完成，总文本长度: {len(response_text)}")
                    break

                elif event_type == "session.created":
                    self.session_id = event.get("session", {}).get("id")
                    logger.bind(tag=TAG).info(f"会话创建: {self.session_id}")

                else:
                    # 记录其他类型的事件
                    logger.bind(tag=TAG).debug(f"收到其他事件: {event_type}")

            # 只有在没有错误且没有收到文本响应时才提供默认响应
            if not has_error and not response_text.strip():
                logger.bind(tag=TAG).warning("Qwen-Omni返回了空响应，可能是API配额或权限问题")
                yield "【Qwen-Omni返回空响应，请检查API配额和权限】"

        except Exception as e:
            logger.bind(tag=TAG).error(f"异步响应处理错误: {e}")
            yield f"【响应处理错误: {e}】"

    async def _send_conversation_item(self, content: str):
        """发送用户对话项目"""
        await self._send_event({
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": content
                    }
                ]
            }
        })

    async def _send_system_message(self, content: str):
        """发送系统消息"""
        await self._send_event({
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "system",
                "content": [
                    {
                        "type": "input_text",
                        "text": content
                    }
                ]
            }
        })

    async def _send_assistant_message(self, content: str):
        """发送助手消息"""
        await self._send_event({
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "assistant",
                "content": [
                    {
                        "type": "text",
                        "text": content
                    }
                ]
            }
        })

    def _start_connection_task(self):
        """启动异步连接任务"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                self._connection_task = asyncio.create_task(self._maintain_connection())
                logger.bind(tag=TAG).info("已启动Qwen-Omni连接维护任务")
            else:
                # 如果没有事件循环，稍后再尝试
                logger.bind(tag=TAG).info("事件循环未运行，将在首次使用时建立连接")
        except RuntimeError:
            # 没有事件循环，稍后再尝试
            logger.bind(tag=TAG).info("无事件循环，将在首次使用时建立连接")

    async def _maintain_connection(self):
        """维护WebSocket连接"""
        while True:
            try:
                if not self.is_connected:
                    logger.bind(tag=TAG).info("开始建立Qwen-Omni连接...")
                    await self.connect()
                    if self.is_connected:
                        logger.bind(tag=TAG).info("Qwen-Omni连接已建立，开始保活")
                        # 启动保活任务
                        if self._keep_alive_task:
                            self._keep_alive_task.cancel()
                        self._keep_alive_task = asyncio.create_task(self._keep_alive())

                # 每30秒检查一次连接状态
                await asyncio.sleep(30)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.bind(tag=TAG).error(f"连接维护任务异常: {e}")
                await asyncio.sleep(10)  # 出错后等待10秒再重试

    async def _keep_alive(self):
        """保持连接活跃"""
        while self.is_connected and self.ws:
            try:
                # 每60秒发送一次ping
                await asyncio.sleep(60)
                if self.ws:
                    await self.ws.ping()
                    logger.bind(tag=TAG).debug("发送保活ping")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.bind(tag=TAG).warning(f"保活ping失败: {e}")
                self.is_connected = False
                break

    def __del__(self):
        """析构函数，确保连接关闭"""
        try:
            if self._connection_task:
                self._connection_task.cancel()
            if self._keep_alive_task:
                self._keep_alive_task.cancel()
            if self.is_connected and self.ws:
                asyncio.run(self.close())
        except:
            pass
