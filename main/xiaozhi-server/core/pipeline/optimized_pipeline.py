"""
优化的语音处理Pipeline，实现并行处理
Optimized Voice Processing Pipeline with Parallel Processing
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Tuple, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import queue

from core.utils.dialogue_compression import DialogueCompressor, OptimizedDialogueManager
from mcp_rag.src.cache.enhanced_vector_cache import EnhancedVectorCache

logger = logging.getLogger(__name__)


@dataclass
class PipelineStats:
    """Pipeline性能统计"""
    total_requests: int = 0
    asr_time: float = 0.0
    vector_time: float = 0.0
    llm_time: float = 0.0
    tts_time: float = 0.0
    total_time: float = 0.0
    parallel_time_saved: float = 0.0


class OptimizedVoicePipeline:
    """优化的语音处理Pipeline"""
    
    def __init__(self,
                 asr_provider,
                 vector_store,
                 llm_provider,
                 tts_provider,
                 vector_cache: Optional[EnhancedVectorCache] = None,
                 dialogue_compressor: Optional[DialogueCompressor] = None):
        """
        初始化优化的语音处理Pipeline
        
        Args:
            asr_provider: ASR提供者
            vector_store: 向量存储
            llm_provider: LLM提供者
            tts_provider: TTS提供者
            vector_cache: 向量缓存
            dialogue_compressor: 对话压缩器
        """
        self.asr = asr_provider
        self.vector_store = vector_store
        self.llm = llm_provider
        self.tts = tts_provider
        
        # 优化组件
        self.vector_cache = vector_cache or EnhancedVectorCache()
        self.dialogue_compressor = dialogue_compressor or DialogueCompressor()
        self.dialogue_manager = OptimizedDialogueManager(self.dialogue_compressor)
        
        # 性能统计
        self.stats = PipelineStats()
        
        # 线程池用于CPU密集型任务
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def process_voice(self, 
                          audio_data: bytes,
                          session_id: str,
                          conn: Any) -> Dict[str, Any]:
        """
        处理语音输入的完整流程
        
        Args:
            audio_data: 音频数据
            session_id: 会话ID
            conn: 连接对象
            
        Returns:
            处理结果
        """
        start_time = time.time()
        self.stats.total_requests += 1
        
        try:
            # 1. ASR识别（必须先完成）
            asr_start = time.time()
            text = await self._process_asr(audio_data, session_id)
            asr_time = time.time() - asr_start
            self.stats.asr_time += asr_time
            
            if not text:
                return {"success": False, "error": "ASR failed"}
                
            logger.info(f"ASR完成 ({asr_time:.2f}s): {text}")
            
            # 2. 并行处理：向量检索 + LLM预处理 + TTS准备
            parallel_start = time.time()
            
            # 创建并行任务
            tasks = []
            
            # 向量检索任务
            vector_task = asyncio.create_task(
                self._process_vector_search(text)
            )
            tasks.append(("vector", vector_task))
            
            # LLM预处理任务（压缩对话历史）
            llm_prep_task = asyncio.create_task(
                self._prepare_llm_context(text, conn)
            )
            tasks.append(("llm_prep", llm_prep_task))
            
            # TTS预热任务
            tts_prep_task = asyncio.create_task(
                self._prepare_tts_session(session_id)
            )
            tasks.append(("tts_prep", tts_prep_task))
            
            # 等待所有并行任务完成
            results = {}
            for task_name, task in tasks:
                try:
                    results[task_name] = await task
                except Exception as e:
                    logger.error(f"Parallel task {task_name} failed: {e}")
                    results[task_name] = None
                    
            parallel_time = time.time() - parallel_start
            
            # 3. LLM生成（使用并行处理的结果）
            llm_start = time.time()
            tools = results.get("vector", [])
            compressed_dialogue = results.get("llm_prep", [])
            
            # 流式生成并立即开始TTS
            response_text = ""
            async for chunk in self._process_llm_stream(
                text, compressed_dialogue, tools, session_id
            ):
                response_text += chunk
                
                # 立即发送到TTS（如果TTS已准备好）
                if results.get("tts_prep", False):
                    await self._send_to_tts(chunk, session_id)
                    
            llm_time = time.time() - llm_start
            self.stats.llm_time += llm_time
            
            # 4. 完成TTS（如果还有剩余文本）
            tts_start = time.time()
            await self._finalize_tts(session_id)
            tts_time = time.time() - tts_start
            self.stats.tts_time += tts_time
            
            # 计算总时间和并行节省的时间
            total_time = time.time() - start_time
            self.stats.total_time += total_time
            
            # 串行时间 = ASR + 向量 + LLM + TTS
            # 并行时间 = ASR + max(向量, LLM准备, TTS准备) + LLM + TTS
            serial_time = asr_time + results.get("vector_time", 0) + llm_time + tts_time
            actual_time = total_time
            time_saved = serial_time - actual_time
            self.stats.parallel_time_saved += max(time_saved, 0)
            
            logger.info(f"Pipeline完成: 总时间={total_time:.2f}s, 节省={time_saved:.2f}s")
            
            return {
                "success": True,
                "text": text,
                "response": response_text,
                "timings": {
                    "asr": asr_time,
                    "vector": results.get("vector_time", 0),
                    "llm": llm_time,
                    "tts": tts_time,
                    "total": total_time,
                    "saved": time_saved
                }
            }
            
        except Exception as e:
            logger.error(f"Pipeline error: {e}")
            return {"success": False, "error": str(e)}
            
    async def _process_asr(self, audio_data: bytes, session_id: str) -> Optional[str]:
        """处理ASR识别"""
        try:
            # 这里假设ASR已经处理好了音频流
            text, _ = await self.asr.speech_to_text([audio_data], session_id, "opus")
            return text
        except Exception as e:
            logger.error(f"ASR error: {e}")
            return None
            
    async def _process_vector_search(self, query: str) -> List[Dict[str, Any]]:
        """处理向量检索"""
        start_time = time.time()
        
        try:
            # 使用向量缓存
            async def compute_and_search(q):
                # 这里调用实际的向量检索
                return await self.vector_store.search_similar_tools(q, top_k=5)
                
            # 先尝试从缓存获取
            cache_key = f"search:{query}"
            cached_result = await self.vector_cache.get_vector(cache_key)
            
            if cached_result:
                # 缓存命中，直接返回
                return cached_result
                
            # 缓存未命中，执行检索
            results = await compute_and_search(query)
            
            # 保存到缓存（简化：将结果作为"向量"保存）
            if results:
                await self.vector_cache.save_vector(cache_key, results)
                
            return results
            
        except Exception as e:
            logger.error(f"Vector search error: {e}")
            return []
        finally:
            # 记录时间
            vector_time = time.time() - start_time
            
    async def _prepare_llm_context(self, 
                                  current_query: str,
                                  conn: Any) -> List[Dict[str, Any]]:
        """准备LLM上下文（压缩对话历史）"""
        try:
            # 获取对话历史
            dialogue_history = conn.dialogue.dialogue if hasattr(conn, 'dialogue') else []
            
            # 压缩对话
            compressed = self.dialogue_compressor.compress_dialogue(
                dialogue_history,
                current_query
            )
            
            return compressed
            
        except Exception as e:
            logger.error(f"LLM context preparation error: {e}")
            return []
            
    async def _prepare_tts_session(self, session_id: str) -> bool:
        """准备TTS会话"""
        try:
            # 启动TTS会话
            await self.tts.start_session(session_id)
            return True
        except Exception as e:
            logger.error(f"TTS preparation error: {e}")
            return False
            
    async def _process_llm_stream(self,
                                 query: str,
                                 dialogue: List[Dict[str, Any]],
                                 tools: List[Dict[str, Any]],
                                 session_id: str):
        """处理LLM流式生成"""
        try:
            # 添加用户消息
            dialogue.append({"role": "user", "content": query})
            
            # 构建functions（如果有工具）
            functions = None
            if tools:
                functions = self._build_functions_from_tools(tools)
                
            # 流式生成
            if functions:
                responses = self.llm.response_with_functions(
                    session_id,
                    dialogue,
                    functions=functions
                )
            else:
                responses = self.llm.response(session_id, dialogue)
                
            # 流式返回
            buffer = ""
            for response in responses:
                if isinstance(response, tuple):
                    content, _ = response
                else:
                    content = response
                    
                if content:
                    buffer += content
                    
                    # 按句子切分，便于TTS处理
                    sentences = self._split_sentences(buffer)
                    if len(sentences) > 1:
                        # 返回完整的句子
                        for sentence in sentences[:-1]:
                            yield sentence + "。"
                        buffer = sentences[-1]
                        
            # 返回剩余的内容
            if buffer:
                yield buffer
                
        except Exception as e:
            logger.error(f"LLM streaming error: {e}")
            yield f"抱歉，处理您的请求时出现了错误。"
            
    async def _send_to_tts(self, text: str, session_id: str):
        """发送文本到TTS"""
        try:
            # 过滤空文本
            if not text or not text.strip():
                return
                
            # 发送到TTS队列
            await self.tts.text_to_speak(text, None)
            
        except Exception as e:
            logger.error(f"TTS send error: {e}")
            
    async def _finalize_tts(self, session_id: str):
        """完成TTS会话"""
        try:
            await self.tts.finish_session(session_id)
        except Exception as e:
            logger.error(f"TTS finalize error: {e}")
            
    def _build_functions_from_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从工具构建functions格式"""
        functions = []
        
        for tool in tools[:3]:  # 只取前3个最相关的
            tool_data = tool.get("tool_data", {})
            function = {
                "type": "function",
                "function": {
                    "name": tool_data.get("name", ""),
                    "description": tool_data.get("description", ""),
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
            functions.append(function)
            
        return functions
        
    def _split_sentences(self, text: str) -> List[str]:
        """简单的句子分割"""
        # 按标点符号分割
        import re
        sentences = re.split(r'[。！？]', text)
        return [s.strip() for s in sentences if s.strip()]
        
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if self.stats.total_requests == 0:
            return {}
            
        return {
            "total_requests": self.stats.total_requests,
            "avg_asr_time": self.stats.asr_time / self.stats.total_requests,
            "avg_vector_time": self.stats.vector_time / self.stats.total_requests,
            "avg_llm_time": self.stats.llm_time / self.stats.total_requests,
            "avg_tts_time": self.stats.tts_time / self.stats.total_requests,
            "avg_total_time": self.stats.total_time / self.stats.total_requests,
            "avg_time_saved": self.stats.parallel_time_saved / self.stats.total_requests,
            "vector_cache_stats": self.vector_cache.get_stats(),
            "dialogue_compression_stats": self.dialogue_compressor.get_compression_stats()
        }
        
    def print_stats(self):
        """打印性能统计"""
        stats = self.get_stats()
        if not stats:
            print("📊 Pipeline统计: 暂无数据")
            return
            
        print("\n📊 优化Pipeline统计:")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   平均时间:")
        print(f"     ASR: {stats['avg_asr_time']:.2f}s")
        print(f"     向量检索: {stats['avg_vector_time']:.2f}s")
        print(f"     LLM: {stats['avg_llm_time']:.2f}s")
        print(f"     TTS: {stats['avg_tts_time']:.2f}s")
        print(f"     总计: {stats['avg_total_time']:.2f}s")
        print(f"   并行优化:")
        print(f"     平均节省时间: {stats['avg_time_saved']:.2f}s")
        
        # 打印子组件统计
        print("\n向量缓存统计:")
        self.vector_cache.print_stats()
        
        print("\n对话压缩统计:")
        self.dialogue_compressor.print_stats()