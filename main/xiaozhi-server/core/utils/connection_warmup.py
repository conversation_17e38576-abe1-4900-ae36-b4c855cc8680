"""
连接预热管理器，减少首次连接延迟
Connection Warmup Manager for Reduced Initial Latency
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import websockets
from websockets.protocol import State

logger = logging.getLogger(__name__)


@dataclass
class WarmupStats:
    """预热统计信息"""
    total_warmups: int = 0
    successful_warmups: int = 0
    failed_warmups: int = 0
    total_warmup_time: float = 0.0
    last_warmup_time: Optional[datetime] = None


class ConnectionWarmupManager:
    """连接预热管理器，用于ASR、TTS等服务的连接预热"""
    
    def __init__(self, warmup_interval: int = 300):  # 默认5分钟预热一次
        """
        初始化连接预热管理器
        
        Args:
            warmup_interval: 预热间隔（秒）
        """
        self.warmup_interval = warmup_interval
        self.warmup_tasks: Dict[str, asyncio.Task] = {}
        self.stats: Dict[str, WarmupStats] = {}
        self.is_running = False
        
    async def start(self, services: Dict[str, Dict[str, Any]]):
        """
        启动预热服务
        
        Args:
            services: 需要预热的服务配置
            {
                "asr": {"url": "wss://...", "headers": {...}},
                "tts": {"url": "wss://...", "headers": {...}},
                ...
            }
        """
        self.is_running = True
        logger.info(f"Starting connection warmup for {len(services)} services")
        
        # 为每个服务启动预热任务
        for service_name, config in services.items():
            self.stats[service_name] = WarmupStats()
            task = asyncio.create_task(self._warmup_loop(service_name, config))
            self.warmup_tasks[service_name] = task
            
        # 立即执行一次预热
        await self.warmup_all(services)
        
    async def stop(self):
        """停止预热服务"""
        self.is_running = False
        
        # 取消所有预热任务
        for task in self.warmup_tasks.values():
            if not task.done():
                task.cancel()
                
        # 等待所有任务结束
        if self.warmup_tasks:
            await asyncio.gather(*self.warmup_tasks.values(), return_exceptions=True)
            
        self.warmup_tasks.clear()
        logger.info("Connection warmup stopped")
        
    async def _warmup_loop(self, service_name: str, config: Dict[str, Any]):
        """单个服务的预热循环"""
        while self.is_running:
            try:
                # 等待下次预热时间
                await asyncio.sleep(self.warmup_interval)
                
                if not self.is_running:
                    break
                    
                # 执行预热
                await self._warmup_service(service_name, config)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Warmup loop error for {service_name}: {e}")
                await asyncio.sleep(10)  # 错误后短暂等待
                
    async def _warmup_service(self, service_name: str, config: Dict[str, Any]):
        """预热单个服务"""
        start_time = time.time()
        stats = self.stats[service_name]
        stats.total_warmups += 1
        
        try:
            if service_name == "asr":
                await self._warmup_asr(config)
            elif service_name == "tts":
                await self._warmup_tts(config)
            elif service_name == "llm":
                await self._warmup_llm(config)
            elif service_name == "vector":
                await self._warmup_vector(config)
            else:
                logger.warning(f"Unknown service type: {service_name}")
                return
                
            # 更新成功统计
            stats.successful_warmups += 1
            stats.last_warmup_time = datetime.now()
            elapsed = time.time() - start_time
            stats.total_warmup_time += elapsed
            
            logger.info(f"✅ {service_name} warmup successful in {elapsed:.2f}s")
            
        except Exception as e:
            stats.failed_warmups += 1
            logger.error(f"❌ {service_name} warmup failed: {e}")
            
    async def _warmup_asr(self, config: Dict[str, Any]):
        """预热ASR连接"""
        url = config.get("url")
        headers = config.get("headers", {})
        
        # 建立WebSocket连接
        ws = await websockets.connect(
            url,
            additional_headers=headers,
            max_size=1000000000,
            ping_interval=None,  # 禁用ping，因为Doubao ASR有自定义协议
            ping_timeout=None,
            close_timeout=5
        )
        
        try:
            # 对于Doubao ASR，不发送ping，直接测试连接建立
            # 等待一小段时间确认连接建立
            await asyncio.sleep(0.5)
            
            # 可选：发送初始化请求
            if config.get("init_message"):
                await ws.send(config["init_message"])
                response = await asyncio.wait_for(ws.recv(), timeout=5.0)
                logger.debug(f"ASR init response: {response[:100]}...")
                
        finally:
            await ws.close()
            
    async def _warmup_tts(self, config: Dict[str, Any]):
        """预热TTS连接"""
        url = config.get("url")
        headers = config.get("headers", {})
        
        # 建立WebSocket连接
        ws = await websockets.connect(
            url,
            additional_headers=headers,
            max_size=1000000000,
            ping_interval=None,  # 禁用ping，因为Doubao TTS有自定义协议
            ping_timeout=None,
            close_timeout=5
        )
        
        try:
            # 对于Doubao TTS，不发送ping，直接测试连接建立
            # 等待一小段时间确认连接建立
            await asyncio.sleep(0.5)
            
            # 可选：发送测试文本
            if config.get("test_text"):
                # 这里根据具体的TTS协议发送测试请求
                pass
                
        finally:
            await ws.close()
            
    async def _warmup_llm(self, config: Dict[str, Any]):
        """预热LLM连接"""
        import aiohttp
        
        url = config.get("url")
        headers = config.get("headers", {})
        
        # 发送测试请求
        async with aiohttp.ClientSession() as session:
            test_payload = {
                "model": config.get("model"),
                "messages": [{"role": "user", "content": "Hi"}],
                "max_tokens": 1,
                "stream": False
            }
            
            async with session.post(
                url,
                headers=headers,
                json=test_payload,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status != 200:
                    raise Exception(f"LLM warmup failed with status {response.status}")
                    
    async def _warmup_vector(self, config: Dict[str, Any]):
        """预热向量服务"""
        import aiohttp
        
        url = config.get("url")
        headers = config.get("headers", {})
        
        # 发送测试向量化请求
        async with aiohttp.ClientSession() as session:
            test_payload = {
                "model": config.get("model", "text-embedding-v4"),
                "input": ["test"],
                "encoding_format": "float"
            }
            
            async with session.post(
                url,
                headers=headers,
                json=test_payload,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status != 200:
                    raise Exception(f"Vector warmup failed with status {response.status}")
                    
    async def warmup_all(self, services: Dict[str, Dict[str, Any]]):
        """立即预热所有服务"""
        tasks = []
        for service_name, config in services.items():
            task = asyncio.create_task(self._warmup_service(service_name, config))
            tasks.append(task)
            
        # 并行执行所有预热
        await asyncio.gather(*tasks, return_exceptions=True)
        
    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取预热统计信息"""
        result = {}
        
        for service_name, stats in self.stats.items():
            total = stats.total_warmups
            if total == 0:
                continue
                
            result[service_name] = {
                "total_warmups": total,
                "successful_warmups": stats.successful_warmups,
                "failed_warmups": stats.failed_warmups,
                "success_rate": stats.successful_warmups / total,
                "avg_warmup_time": stats.total_warmup_time / total,
                "last_warmup": stats.last_warmup_time.isoformat() if stats.last_warmup_time else None
            }
            
        return result
        
    def print_stats(self):
        """打印预热统计信息"""
        stats = self.get_stats()
        if not stats:
            print("📊 连接预热统计: 暂无数据")
            return
            
        print("\n📊 连接预热统计:")
        for service_name, service_stats in stats.items():
            print(f"\n   {service_name}:")
            print(f"     总预热次数: {service_stats['total_warmups']}")
            print(f"     成功次数: {service_stats['successful_warmups']}")
            print(f"     失败次数: {service_stats['failed_warmups']}")
            print(f"     成功率: {service_stats['success_rate']:.1%}")
            print(f"     平均预热时间: {service_stats['avg_warmup_time']:.2f}s")
            print(f"     最后预热: {service_stats['last_warmup']}")


class ServiceConnectionPool:
    """服务连接池，管理预热的连接"""
    
    def __init__(self, max_idle_time: int = 60):
        """
        初始化连接池
        
        Args:
            max_idle_time: 最大空闲时间（秒）
        """
        self.max_idle_time = max_idle_time
        self.connections: Dict[str, List[Any]] = {}
        self.last_used: Dict[str, datetime] = {}
        self._cleanup_task = None
        
    async def start(self):
        """启动连接池清理任务"""
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
    async def stop(self):
        """停止连接池"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        # 关闭所有连接
        for service_conns in self.connections.values():
            for conn in service_conns:
                try:
                    if hasattr(conn, 'close'):
                        await conn.close()
                except Exception as e:
                    logger.error(f"Error closing connection: {e}")
                    
        self.connections.clear()
        
    async def _cleanup_loop(self):
        """定期清理空闲连接"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                now = datetime.now()
                for service_name in list(self.connections.keys()):
                    last_used = self.last_used.get(service_name)
                    if last_used and (now - last_used).seconds > self.max_idle_time:
                        # 清理空闲连接
                        await self._close_service_connections(service_name)
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                
    async def _close_service_connections(self, service_name: str):
        """关闭特定服务的所有连接"""
        if service_name in self.connections:
            for conn in self.connections[service_name]:
                try:
                    if hasattr(conn, 'close'):
                        await conn.close()
                except Exception as e:
                    logger.error(f"Error closing {service_name} connection: {e}")
                    
            del self.connections[service_name]
            if service_name in self.last_used:
                del self.last_used[service_name]