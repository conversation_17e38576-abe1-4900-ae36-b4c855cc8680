"""
语音管道时间统计器
Voice Pipeline Timer

为语音处理管道的每个阶段添加详细的时间统计：
1. 语音输入 (ASR)
2. 检索 (Vector Search)  
3. 大模型处理 (LLM)
4. 语音输出 (TTS)
"""

import time
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from config.logger import setup_logging

logger = setup_logging()


@dataclass
class StageTimer:
    """单个阶段的计时器"""
    name: str
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: float = 0.0
    status: str = "pending"  # pending, running, completed, failed
    details: Dict[str, Any] = field(default_factory=dict)
    
    def start(self, details: Optional[Dict[str, Any]] = None):
        """开始计时"""
        self.start_time = time.time()
        self.status = "running"
        if details:
            self.details.update(details)
        
        start_datetime = datetime.fromtimestamp(self.start_time)
        logger.info(f"🎯 [{self.name}] 开始 - {start_datetime.strftime('%H:%M:%S.%f')[:-3]}")
        
    def end(self, status: str = "completed", details: Optional[Dict[str, Any]] = None):
        """结束计时"""
        self.end_time = time.time()
        self.status = status
        if details:
            self.details.update(details)
            
        if self.start_time:
            self.duration = self.end_time - self.start_time
            
        end_datetime = datetime.fromtimestamp(self.end_time)
        status_emoji = "✅" if status == "completed" else "❌" if status == "failed" else "⚠️"
        
        logger.info(f"{status_emoji} [{self.name}] 结束 - {end_datetime.strftime('%H:%M:%S.%f')[:-3]} | 耗时: {self.duration:.3f}s")
        
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "name": self.name,
            "duration": self.duration,
            "status": self.status,
            "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
            "end_time": datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None,
            "details": self.details
        }


@dataclass
class PipelineTimer:
    """完整管道计时器"""
    session_id: str
    total_start_time: Optional[float] = None
    total_end_time: Optional[float] = None
    total_duration: float = 0.0
    stages: Dict[str, StageTimer] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化标准阶段"""
        self.stages = {
            "asr": StageTimer("语音识别(ASR)"),
            "intent": StageTimer("意图分析"),
            "vector": StageTimer("向量检索"),
            "llm": StageTimer("大模型处理"),
            "tts": StageTimer("语音合成(TTS)"),
        }
    
    def start_pipeline(self):
        """开始整个管道计时"""
        self.total_start_time = time.time()
        start_datetime = datetime.fromtimestamp(self.total_start_time)
        logger.info(f"🚀 [完整管道] 开始 - Session: {self.session_id} | 时间: {start_datetime.strftime('%H:%M:%S.%f')[:-3]}")
        
    def end_pipeline(self):
        """结束整个管道计时"""
        self.total_end_time = time.time()
        if self.total_start_time:
            self.total_duration = self.total_end_time - self.total_start_time
            
        end_datetime = datetime.fromtimestamp(self.total_end_time)
        logger.info(f"🏁 [完整管道] 结束 - Session: {self.session_id} | 时间: {end_datetime.strftime('%H:%M:%S.%f')[:-3]} | 总耗时: {self.total_duration:.3f}s")
        
        # 输出详细统计
        self._print_detailed_summary()
        
    def start_stage(self, stage_name: str, details: Optional[Dict[str, Any]] = None):
        """开始某个阶段"""
        if stage_name in self.stages:
            self.stages[stage_name].start(details)
        else:
            # 动态添加新阶段
            self.stages[stage_name] = StageTimer(stage_name)
            self.stages[stage_name].start(details)
            
    def end_stage(self, stage_name: str, status: str = "completed", details: Optional[Dict[str, Any]] = None):
        """结束某个阶段"""
        if stage_name in self.stages:
            self.stages[stage_name].end(status, details)
            
    def get_stage_duration(self, stage_name: str) -> float:
        """获取某个阶段的耗时"""
        if stage_name in self.stages:
            return self.stages[stage_name].duration
        return 0.0
        
    def _print_detailed_summary(self):
        """打印详细统计摘要"""
        # 已禁用详细日志输出
        pass
        
    def _analyze_performance(self):
        """性能分析和建议"""
        logger.info("\n🔍 [性能分析]")
        
        # 找出最耗时的阶段
        max_duration = 0
        slowest_stage = None
        for stage in self.stages.values():
            if stage.duration > max_duration:
                max_duration = stage.duration
                slowest_stage = stage
                
        if slowest_stage:
            percentage = (max_duration / self.total_duration * 100) if self.total_duration > 0 else 0
            logger.info(f"⚠️  最耗时阶段: {slowest_stage.name} ({max_duration:.3f}s, {percentage:.1f}%)")
            
        # 性能评估
        if self.total_duration > 2.0:
            logger.warning(f"⚠️  管道耗时较长 ({self.total_duration:.3f}s)，建议优化")
        elif self.total_duration > 1.0:
            logger.info(f"📊 管道耗时正常 ({self.total_duration:.3f}s)")
        else:
            logger.info(f"🚀 管道耗时优秀 ({self.total_duration:.3f}s)")
            
        # 各阶段建议
        asr_time = self.get_stage_duration("asr")
        if asr_time > 0.5:
            logger.warning("💡 ASR耗时较长，建议检查网络连接或使用本地模型")
            
        llm_time = self.get_stage_duration("llm")
        if llm_time > 1.5:
            logger.warning("💡 LLM耗时较长，建议检查模型配置或网络延迟")
            
        tts_time = self.get_stage_duration("tts")
        if tts_time > 0.3:
            logger.warning("💡 TTS耗时较长，建议启用连接预热或使用流式TTS")
            
    def get_full_summary(self) -> Dict[str, Any]:
        """获取完整统计摘要"""
        return {
            "session_id": self.session_id,
            "total_duration": self.total_duration,
            "total_start_time": datetime.fromtimestamp(self.total_start_time).isoformat() if self.total_start_time else None,
            "total_end_time": datetime.fromtimestamp(self.total_end_time).isoformat() if self.total_end_time else None,
            "stages": {name: stage.get_summary() for name, stage in self.stages.items()},
            "performance_grade": self._get_performance_grade()
        }
        
    def _get_performance_grade(self) -> str:
        """获取性能等级"""
        if self.total_duration <= 0.8:
            return "A+ (优秀)"
        elif self.total_duration <= 1.2:
            return "A (良好)"
        elif self.total_duration <= 1.8:
            return "B (一般)"
        elif self.total_duration <= 2.5:
            return "C (较慢)"
        else:
            return "D (需要优化)"


class VoicePipelineTimerManager:
    """语音管道计时器管理器"""
    
    def __init__(self):
        self.active_timers: Dict[str, PipelineTimer] = {}
        self.completed_timers: List[PipelineTimer] = []
        self.stats = {
            "total_sessions": 0,
            "avg_duration": 0.0,
            "fastest_session": float('inf'),
            "slowest_session": 0.0,
            "stage_stats": {}
        }
        
    def create_timer(self, session_id: str) -> PipelineTimer:
        """创建新的管道计时器"""
        timer = PipelineTimer(session_id)
        self.active_timers[session_id] = timer
        return timer
        
    def get_timer(self, session_id: str) -> Optional[PipelineTimer]:
        """获取管道计时器"""
        return self.active_timers.get(session_id)
        
    def complete_timer(self, session_id: str):
        """完成管道计时"""
        if session_id in self.active_timers:
            timer = self.active_timers.pop(session_id)
            timer.end_pipeline()
            self.completed_timers.append(timer)
            self._update_stats(timer)
            
    def _update_stats(self, timer: PipelineTimer):
        """更新统计信息"""
        self.stats["total_sessions"] += 1
        
        # 更新总体统计
        total_durations = [t.total_duration for t in self.completed_timers]
        self.stats["avg_duration"] = sum(total_durations) / len(total_durations)
        self.stats["fastest_session"] = min(total_durations)
        self.stats["slowest_session"] = max(total_durations)
        
        # 更新各阶段统计
        for stage_name, stage in timer.stages.items():
            if stage.duration > 0:
                if stage_name not in self.stats["stage_stats"]:
                    self.stats["stage_stats"][stage_name] = {
                        "count": 0,
                        "total_duration": 0.0,
                        "avg_duration": 0.0,
                        "max_duration": 0.0,
                        "min_duration": float('inf')
                    }
                    
                stage_stats = self.stats["stage_stats"][stage_name]
                stage_stats["count"] += 1
                stage_stats["total_duration"] += stage.duration
                stage_stats["avg_duration"] = stage_stats["total_duration"] / stage_stats["count"]
                stage_stats["max_duration"] = max(stage_stats["max_duration"], stage.duration)
                stage_stats["min_duration"] = min(stage_stats["min_duration"], stage.duration)
                
    def print_global_stats(self):
        """打印全局统计信息"""
        logger.info("\n📈 [全局管道统计]")
        logger.info("=" * 80)
        logger.info(f"总会话数: {self.stats['total_sessions']}")
        logger.info(f"平均耗时: {self.stats['avg_duration']:.3f}s")
        logger.info(f"最快会话: {self.stats['fastest_session']:.3f}s")
        logger.info(f"最慢会话: {self.stats['slowest_session']:.3f}s")
        
        logger.info("\n📊 [各阶段统计]")
        for stage_name, stats in self.stats["stage_stats"].items():
            logger.info(f"{stage_name}:")
            logger.info(f"  平均: {stats['avg_duration']:.3f}s")
            logger.info(f"  最快: {stats['min_duration']:.3f}s")
            logger.info(f"  最慢: {stats['max_duration']:.3f}s")
            logger.info(f"  次数: {stats['count']}")
            
    def get_recent_performance(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的性能数据"""
        recent = self.completed_timers[-count:] if len(self.completed_timers) >= count else self.completed_timers
        return [timer.get_full_summary() for timer in recent]
        
    def cleanup_old_timers(self, max_keep: int = 100):
        """清理旧的计时器"""
        if len(self.completed_timers) > max_keep:
            self.completed_timers = self.completed_timers[-max_keep:]


# 全局计时器管理器实例
voice_timer_manager = VoicePipelineTimerManager()


def get_voice_timer(session_id: str) -> PipelineTimer:
    """获取或创建语音管道计时器"""
    timer = voice_timer_manager.get_timer(session_id)
    if timer is None:
        timer = voice_timer_manager.create_timer(session_id)
    return timer


def complete_voice_timer(session_id: str):
    """完成语音管道计时"""
    voice_timer_manager.complete_timer(session_id)