"""
性能监控工具，用于跟踪和优化系统性能
Performance Monitoring Tools for System Optimization
"""

import time
import asyncio
import functools
import logging
from typing import Dict, Any, Callable, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import json
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    function_name: str
    call_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    last_call_time: Optional[datetime] = None
    errors: int = 0
    error_details: list = field(default_factory=list)


class PerformanceMonitor:
    """全局性能监控器"""
    
    def __init__(self, log_dir: str = "performance_logs"):
        """
        初始化性能监控器
        
        Args:
            log_dir: 日志目录
        """
        self.metrics: Dict[str, PerformanceMetrics] = {}
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.start_time = time.time()
        
    def record_call(self, 
                   function_name: str,
                   execution_time: float,
                   error: Optional[Exception] = None):
        """
        记录函数调用
        
        Args:
            function_name: 函数名
            execution_time: 执行时间
            error: 错误信息（如果有）
        """
        if function_name not in self.metrics:
            self.metrics[function_name] = PerformanceMetrics(function_name)
            
        metric = self.metrics[function_name]
        metric.call_count += 1
        metric.total_time += execution_time
        metric.min_time = min(metric.min_time, execution_time)
        metric.max_time = max(metric.max_time, execution_time)
        metric.avg_time = metric.total_time / metric.call_count
        metric.last_call_time = datetime.now()
        
        if error:
            metric.errors += 1
            metric.error_details.append({
                "time": datetime.now().isoformat(),
                "error": str(error),
                "type": type(error).__name__
            })
            
    def get_metrics(self, function_name: Optional[str] = None) -> Union[Dict[str, Any], PerformanceMetrics]:
        """获取性能指标"""
        if function_name:
            return self.metrics.get(function_name)
        return self.metrics
        
    def print_summary(self):
        """打印性能摘要"""
        uptime = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("📊 性能监控摘要")
        print("="*60)
        print(f"运行时间: {uptime:.1f}s")
        print(f"监控函数数: {len(self.metrics)}")
        
        # 按总时间排序
        sorted_metrics = sorted(
            self.metrics.values(),
            key=lambda x: x.total_time,
            reverse=True
        )
        
        print("\n函数性能统计:")
        print("-"*60)
        print(f"{'函数名':<30} {'调用次数':<10} {'平均时间':<10} {'总时间':<10}")
        print("-"*60)
        
        for metric in sorted_metrics[:10]:  # 只显示前10个
            print(f"{metric.function_name:<30} "
                  f"{metric.call_count:<10} "
                  f"{metric.avg_time:<10.3f} "
                  f"{metric.total_time:<10.3f}")
                  
        # 错误统计
        error_functions = [m for m in self.metrics.values() if m.errors > 0]
        if error_functions:
            print("\n错误统计:")
            print("-"*60)
            for metric in error_functions:
                print(f"{metric.function_name}: {metric.errors} 错误")
                
    def save_report(self, filename: Optional[str] = None):
        """保存性能报告"""
        if not filename:
            filename = f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
        filepath = self.log_dir / filename
        
        report = {
            "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
            "uptime": time.time() - self.start_time,
            "metrics": {}
        }
        
        for name, metric in self.metrics.items():
            report["metrics"][name] = {
                "call_count": metric.call_count,
                "total_time": metric.total_time,
                "avg_time": metric.avg_time,
                "min_time": metric.min_time,
                "max_time": metric.max_time,
                "errors": metric.errors,
                "error_details": metric.error_details,
                "last_call": metric.last_call_time.isoformat() if metric.last_call_time else None
            }
            
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Performance report saved to {filepath}")


# 全局性能监控器实例
global_monitor = PerformanceMonitor()


def monitor_performance(name: Optional[str] = None, 
                       log_args: bool = False,
                       log_result: bool = False):
    """
    性能监控装饰器
    
    Args:
        name: 自定义函数名（默认使用函数名）
        log_args: 是否记录参数
        log_result: 是否记录结果
        
    Usage:
        @monitor_performance()
        async def my_async_function():
            pass
            
        @monitor_performance(name="custom_name")
        def my_sync_function():
            pass
    """
    def decorator(func):
        func_name = name or func.__name__
        
        if asyncio.iscoroutinefunction(func):
            # 异步函数
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                error = None
                result = None
                
                try:
                    # 记录参数
                    if log_args:
                        logger.debug(f"{func_name} called with args={args}, kwargs={kwargs}")
                        
                    result = await func(*args, **kwargs)
                    
                    # 记录结果
                    if log_result:
                        logger.debug(f"{func_name} returned: {result}")
                        
                    return result
                    
                except Exception as e:
                    error = e
                    logger.error(f"{func_name} error: {e}")
                    raise
                    
                finally:
                    execution_time = time.time() - start_time
                    global_monitor.record_call(func_name, execution_time, error)
                    
                    # 记录详细日志
                    if execution_time > 1.0:  # 慢查询
                        logger.warning(f"{func_name} slow execution: {execution_time:.3f}s")
                    else:
                        logger.debug(f"{func_name} executed in {execution_time:.3f}s")
                        
            return async_wrapper
            
        else:
            # 同步函数
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                error = None
                result = None
                
                try:
                    # 记录参数
                    if log_args:
                        logger.debug(f"{func_name} called with args={args}, kwargs={kwargs}")
                        
                    result = func(*args, **kwargs)
                    
                    # 记录结果
                    if log_result:
                        logger.debug(f"{func_name} returned: {result}")
                        
                    return result
                    
                except Exception as e:
                    error = e
                    logger.error(f"{func_name} error: {e}")
                    raise
                    
                finally:
                    execution_time = time.time() - start_time
                    global_monitor.record_call(func_name, execution_time, error)
                    
                    # 记录详细日志
                    if execution_time > 1.0:  # 慢查询
                        logger.warning(f"{func_name} slow execution: {execution_time:.3f}s")
                    else:
                        logger.debug(f"{func_name} executed in {execution_time:.3f}s")
                        
            return sync_wrapper
            
    return decorator


def monitor_class_methods(cls):
    """
    类方法监控装饰器
    自动监控类中所有公共方法
    
    Usage:
        @monitor_class_methods
        class MyClass:
            def method1(self):
                pass
    """
    for attr_name in dir(cls):
        # 跳过私有方法和特殊方法
        if attr_name.startswith('_'):
            continue
            
        attr = getattr(cls, attr_name)
        
        # 只装饰可调用的方法
        if callable(attr):
            # 使用类名.方法名作为监控名称
            monitor_name = f"{cls.__name__}.{attr_name}"
            decorated = monitor_performance(name=monitor_name)(attr)
            setattr(cls, attr_name, decorated)
            
    return cls


class PerformanceContext:
    """
    性能监控上下文管理器
    
    Usage:
        async with PerformanceContext("my_operation"):
            # 执行操作
            pass
    """
    
    def __init__(self, name: str):
        self.name = name
        self.start_time = None
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        error = exc_val if exc_type else None
        global_monitor.record_call(self.name, execution_time, error)
        
    async def __aenter__(self):
        self.start_time = time.time()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        error = exc_val if exc_type else None
        global_monitor.record_call(self.name, execution_time, error)


# 专门的语音处理监控装饰器
def monitor_voice_pipeline(stage: str):
    """
    语音处理Pipeline专用监控
    
    Args:
        stage: 处理阶段 (ASR/Vector/LLM/TTS)
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 记录详细的阶段信息
            stage_name = f"voice_pipeline.{stage}"
            
            async with PerformanceContext(stage_name):
                result = await func(*args, **kwargs)
                
                # 特殊处理：记录ASR识别的文本
                if stage == "ASR" and result:
                    logger.info(f"ASR识别结果: {result}")
                    
                # 特殊处理：记录向量检索的结果数
                elif stage == "Vector" and isinstance(result, list):
                    logger.info(f"向量检索返回 {len(result)} 个结果")
                    
                return result
                
        return wrapper
    return decorator


# 使用示例
if __name__ == "__main__":
    # 测试监控装饰器
    @monitor_performance(log_args=True, log_result=True)
    async def test_async_function(x, y):
        await asyncio.sleep(0.1)
        return x + y
        
    @monitor_performance()
    def test_sync_function(x):
        time.sleep(0.1)
        if x < 0:
            raise ValueError("x must be positive")
        return x * 2
        
    # 运行测试
    async def run_tests():
        # 测试异步函数
        result = await test_async_function(1, 2)
        print(f"Async result: {result}")
        
        # 测试同步函数
        try:
            result = test_sync_function(5)
            print(f"Sync result: {result}")
            
            # 触发错误
            test_sync_function(-1)
        except ValueError:
            pass
            
        # 打印性能摘要
        global_monitor.print_summary()
        
        # 保存报告
        global_monitor.save_report()
        
    asyncio.run(run_tests())