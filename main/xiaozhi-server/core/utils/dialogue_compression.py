"""
对话历史压缩模块，减少Token消耗，提升LLM响应速度
Dialogue Compression Module for Token Optimization
"""

import json
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class DialogueStats:
    """对话统计信息"""
    total_messages: int = 0
    compressed_messages: int = 0
    total_tokens_before: int = 0
    total_tokens_after: int = 0
    compression_time: float = 0.0


class DialogueCompressor:
    """对话历史压缩器，优化LLM输入"""
    
    def __init__(self, 
                 max_messages: int = 20,
                 max_tokens: int = 2000,
                 keep_system_messages: bool = True):
        """
        初始化对话压缩器
        
        Args:
            max_messages: 最大保留消息数
            max_tokens: 最大Token数限制
            keep_system_messages: 是否保留系统消息
        """
        self.max_messages = max_messages
        self.max_tokens = max_tokens
        self.keep_system_messages = keep_system_messages
        self.stats = DialogueStats()
        
    def compress_dialogue(self, dialogue: List[Dict[str, Any]], 
                         current_query: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        压缩对话历史
        
        Args:
            dialogue: 原始对话历史
            current_query: 当前用户查询（用于生成更相关的摘要）
            
        Returns:
            压缩后的对话历史
        """
        import time
        start_time = time.time()
        
        # 更新统计
        self.stats.total_messages += len(dialogue)
        self.stats.total_tokens_before += self._estimate_tokens(dialogue)
        
        # 1. 分离不同类型的消息
        system_messages = []
        conversation = []
        tool_messages = []
        
        for msg in dialogue:
            if msg.get("role") == "system":
                system_messages.append(msg)
            elif msg.get("role") == "tool" or msg.get("tool_calls"):
                tool_messages.append(msg)
            else:
                conversation.append(msg)
                
        # 2. 如果对话不长，直接返回
        if len(conversation) <= self.max_messages:
            compressed = dialogue
        else:
            # 3. 需要压缩
            compressed = []
            
            # 保留系统消息
            if self.keep_system_messages:
                compressed.extend(system_messages)
                
            # 压缩旧对话
            old_messages = conversation[:-self.max_messages]
            recent_messages = conversation[-self.max_messages:]
            
            # 生成摘要
            if old_messages:
                summary = self._generate_summary(old_messages, current_query)
                if summary:
                    compressed.append({
                        "role": "system",
                        "content": f"[历史对话摘要] {summary}"
                    })
                    
            # 保留最近的对话
            compressed.extend(recent_messages)
            
            # 智能筛选工具调用消息
            compressed.extend(self._filter_tool_messages(tool_messages))
            
        # 4. Token限制检查
        compressed = self._enforce_token_limit(compressed)
        
        # 更新统计
        self.stats.compressed_messages += len(compressed)
        self.stats.total_tokens_after += self._estimate_tokens(compressed)
        self.stats.compression_time += time.time() - start_time
        
        logger.debug(f"Dialogue compressed: {len(dialogue)} -> {len(compressed)} messages")
        
        return compressed
        
    def _generate_summary(self, messages: List[Dict[str, Any]], 
                         current_query: Optional[str] = None) -> str:
        """
        生成对话摘要
        
        Args:
            messages: 需要摘要的消息
            current_query: 当前查询，用于生成更相关的摘要
            
        Returns:
            摘要文本
        """
        # 提取关键信息
        topics = set()
        key_points = []
        user_intents = []
        
        for msg in messages:
            content = msg.get("content", "")
            role = msg.get("role", "")
            
            if role == "user":
                # 提取用户意图
                if "播放" in content or "音乐" in content:
                    topics.add("音乐控制")
                elif "灯" in content or "开关" in content:
                    topics.add("设备控制")
                elif "天气" in content:
                    topics.add("天气查询")
                elif "时间" in content or "几点" in content:
                    topics.add("时间查询")
                    
                # 保存重要的用户请求
                if len(content) < 50:  # 短请求更可能是重要的
                    user_intents.append(content)
                    
            elif role == "assistant":
                # 提取助手的关键回复
                if "已" in content or "成功" in content or "完成" in content:
                    key_points.append(content[:50])
                    
        # 构建摘要
        summary_parts = []
        
        if topics:
            summary_parts.append(f"用户讨论了: {', '.join(topics)}")
            
        if user_intents:
            recent_intents = user_intents[-3:]  # 最近3个意图
            summary_parts.append(f"最近请求: {'; '.join(recent_intents)}")
            
        if key_points:
            recent_points = key_points[-2:]  # 最近2个关键点
            summary_parts.append(f"已完成: {'; '.join(recent_points)}")
            
        # 如果有当前查询，添加相关性提示
        if current_query and topics:
            for topic in topics:
                if topic in current_query:
                    summary_parts.append(f"注意: 用户之前也询问过{topic}相关内容")
                    
        return " | ".join(summary_parts) if summary_parts else "用户进行了一些日常对话"
        
    def _filter_tool_messages(self, tool_messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        智能筛选工具调用消息
        只保留最近的和重要的工具调用
        """
        if len(tool_messages) <= 5:
            return tool_messages
            
        # 保留最近的3个和所有失败的调用
        recent = tool_messages[-3:]
        failed = [msg for msg in tool_messages[:-3] 
                 if "error" in msg.get("content", "").lower() 
                 or "失败" in msg.get("content", "")]
                 
        return failed + recent
        
    def _enforce_token_limit(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        强制执行Token限制
        从最旧的非系统消息开始删除
        """
        current_tokens = self._estimate_tokens(messages)
        
        if current_tokens <= self.max_tokens:
            return messages
            
        # 需要进一步压缩
        result = []
        system_msgs = [msg for msg in messages if msg.get("role") == "system"]
        other_msgs = [msg for msg in messages if msg.get("role") != "system"]
        
        # 保留系统消息
        result.extend(system_msgs)
        remaining_tokens = self.max_tokens - self._estimate_tokens(system_msgs)
        
        # 从后往前添加其他消息
        for msg in reversed(other_msgs):
            msg_tokens = self._estimate_tokens([msg])
            if remaining_tokens >= msg_tokens:
                result.insert(len(system_msgs), msg)
                remaining_tokens -= msg_tokens
            else:
                break
                
        return result
        
    def _estimate_tokens(self, messages: List[Dict[str, Any]]) -> int:
        """
        估算消息的Token数
        简单估算: 中文约1.5字符/token, 英文约4字符/token
        """
        total_chars = 0
        for msg in messages:
            content = msg.get("content", "")
            # 工具调用的参数也要计算
            if "tool_calls" in msg:
                content += json.dumps(msg["tool_calls"])
            total_chars += len(content)
            
        # 混合内容，平均2.5字符/token
        return int(total_chars / 2.5)
        
    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        if self.stats.total_messages == 0:
            return {}
            
        return {
            "total_messages": self.stats.total_messages,
            "compressed_messages": self.stats.compressed_messages,
            "compression_ratio": 1 - (self.stats.compressed_messages / self.stats.total_messages),
            "tokens_before": self.stats.total_tokens_before,
            "tokens_after": self.stats.total_tokens_after,
            "token_reduction": 1 - (self.stats.total_tokens_after / max(self.stats.total_tokens_before, 1)),
            "avg_compression_time": self.stats.compression_time / max(self.stats.total_messages, 1)
        }
        
    def print_stats(self):
        """打印压缩统计信息"""
        stats = self.get_compression_stats()
        if not stats:
            print("📊 对话压缩统计: 暂无数据")
            return
            
        print("\n📊 对话压缩统计:")
        print(f"   处理消息总数: {stats['total_messages']}")
        print(f"   压缩后消息数: {stats['compressed_messages']}")
        print(f"   压缩率: {stats['compression_ratio']:.1%}")
        print(f"   Token减少: {stats['tokens_before']} -> {stats['tokens_after']} ({stats['token_reduction']:.1%})")
        print(f"   平均压缩时间: {stats['avg_compression_time']:.3f}s")


class OptimizedDialogueManager:
    """优化的对话管理器，集成压缩功能"""
    
    def __init__(self, compressor: Optional[DialogueCompressor] = None):
        """
        初始化对话管理器
        
        Args:
            compressor: 对话压缩器实例
        """
        self.compressor = compressor or DialogueCompressor()
        self.dialogue_history = []
        self.system_prompt = None
        
    def set_system_prompt(self, prompt: str):
        """设置系统提示词"""
        self.system_prompt = prompt
        
    def add_message(self, role: str, content: str, **kwargs):
        """添加消息到对话历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        self.dialogue_history.append(message)
        
    def get_compressed_dialogue(self, current_query: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取压缩后的对话历史
        
        Args:
            current_query: 当前用户查询
            
        Returns:
            压缩后的对话历史
        """
        # 构建完整对话
        dialogue = []
        
        # 添加系统提示
        if self.system_prompt:
            dialogue.append({
                "role": "system",
                "content": self.system_prompt
            })
            
        # 添加历史对话
        dialogue.extend(self.dialogue_history)
        
        # 压缩并返回
        return self.compressor.compress_dialogue(dialogue, current_query)
        
    def clear_history(self):
        """清空对话历史"""
        self.dialogue_history.clear()
        logger.info("Dialogue history cleared")