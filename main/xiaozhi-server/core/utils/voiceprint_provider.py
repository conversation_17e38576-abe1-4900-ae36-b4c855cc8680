import asyncio
import json
import time
import aiohttp
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Tuple
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class VoiceprintAccessResult:
    """声纹权限验证结果"""
    def __init__(self, allowed: bool, speaker_name: str = None, reason: str = None, confidence: float = 0.0):
        self.allowed = allowed  # 是否允许访问
        self.speaker_name = speaker_name  # 识别的说话人
        self.reason = reason  # 拒绝原因
        self.confidence = confidence  # 识别置信度


class VoiceprintProvider:
    """声纹识别服务提供者"""
    
    def __init__(self, config: dict):
        self.original_url = config.get("url", "")
        self.speakers = config.get("speakers", [])
        self.speaker_map = self._parse_speakers()
        # 新增：全局声纹打断开关
        self.global_interrupt_enabled = config.get("voiceprintInterruptEnabled", True)
        
        # 权限控制配置
        self.access_control = config.get("access_control", {})
        self.access_enabled = self.access_control.get("enabled", False)
        self.access_mode = self.access_control.get("mode", "whitelist")
        self.allowed_speakers = set(self.access_control.get("allowed_speakers", []))
        self.blocked_speakers = set(self.access_control.get("blocked_speakers", []))
        self.unknown_speaker_action = self.access_control.get("unknown_speaker_action", "allow")
        self.confidence_threshold = self.access_control.get("confidence_threshold", 0.38)
        
        # 提示消息
        self.access_denied_message = self.access_control.get("access_denied_message", "抱歉，您没有使用权限")
        self.unknown_speaker_message = self.access_control.get("unknown_speaker_message", "无法识别您的身份")
        self.low_confidence_message = self.access_control.get("low_confidence_message", "声纹识别置信度较低")
        
        # 解析API地址和密钥
        self.api_url = None
        self.api_key = None
        self.speaker_ids = []
        
        if not self.original_url:
            logger.bind(tag=TAG).warning("声纹识别URL未配置，声纹识别将被禁用")
            self.enabled = False
        else:
            # 解析URL和key
            parsed_url = urlparse(self.original_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}/v1"
            
            # 从查询参数中提取key
            query_params = parse_qs(parsed_url.query)
            self.api_key = query_params.get('key', [''])[0]
            
            if not self.api_key:
                logger.bind(tag=TAG).error("URL中未找到key参数，声纹识别将被禁用")
                self.enabled = False
            else:
                # 构造identify接口地址
                self.api_url = f"{base_url}/voiceprint/identify"
                
                # 提取speaker_ids，兼容字符串和dict两种格式
                self.speaker_ids = []
                for speaker in self.speakers:
                    if isinstance(speaker, dict):
                        speaker_id = speaker.get("id")
                        if speaker_id:
                            self.speaker_ids.append(speaker_id)
                    elif isinstance(speaker, str):
                        parts = speaker.split(",", 2)
                        if len(parts) >= 1:
                            speaker_id = parts[0].strip()
                            if speaker_id:
                                self.speaker_ids.append(speaker_id)
                # 检查是否有有效的说话人配置
                if not self.speaker_ids:
                    logger.bind(tag=TAG).warning("未配置有效的说话人，声纹识别将被禁用")
                    self.enabled = False
                else:
                    self.enabled = True
                    logger.bind(tag=TAG).info(f"声纹识别已配置: API={self.api_url}, 说话人={len(self.speaker_ids)}个")
                    if self.access_enabled:
                        logger.bind(tag=TAG).info(f"声纹权限控制已启用: 模式={self.access_mode}, 允许说话人={self.allowed_speakers}, 阻止说话人={self.blocked_speakers}")
    
    def _parse_speakers(self) -> Dict[str, Dict[str, str]]:
        """解析说话人配置，兼容 dict 和字符串格式"""
        speaker_map = {}
        for speaker in self.speakers:
            if isinstance(speaker, dict):
                speaker_id = speaker.get("id")
                name = speaker.get("name")
                description = speaker.get("description", "")
                if speaker_id and name:
                    speaker_map[speaker_id] = {
                        "name": name,
                        "description": description
                    }
            elif isinstance(speaker, str):
                parts = speaker.split(",", 2)
                if len(parts) >= 3:
                    speaker_id, name, description = parts[0].strip(), parts[1].strip(), parts[2].strip()
                    speaker_map[speaker_id] = {
                        "name": name,
                        "description": description
                    }
        return speaker_map

    def _check_access_permission(self, speaker_name: str, confidence: float) -> VoiceprintAccessResult:
        # 只判断全局开关和白名单
        if not self.global_interrupt_enabled:
            logger.bind(tag=TAG).info("全局声纹打断未开启，允许打断")
            return VoiceprintAccessResult(allowed=True, speaker_name=speaker_name, confidence=confidence)
        found = None
        for sid, info in self.speaker_map.items():
            if info["name"] == speaker_name:
                found = info
                break
        if not found:
            logger.bind(tag=TAG).warning(f"说话人 {speaker_name} 不在白名单")
            return VoiceprintAccessResult(allowed=False, speaker_name=speaker_name, reason="不在白名单", confidence=confidence)
        if speaker_name and confidence >= self.confidence_threshold:
            logger.bind(tag=TAG).info(f"识别出说话人且置信度达标，允许访问: {speaker_name} (置信度: {confidence:.3f})")
            return VoiceprintAccessResult(allowed=True, speaker_name=speaker_name, confidence=confidence)
        else:
            logger.bind(tag=TAG).warning(f"未通过声纹权限验证，拒绝访问 (speaker_name={speaker_name}, confidence={confidence:.3f}, 阈值={self.confidence_threshold})")
            return VoiceprintAccessResult(
                allowed=False,
                speaker_name=speaker_name,
                reason=self.unknown_speaker_message,
                confidence=confidence
            )
        
        # 兜底逻辑：默认允许
        logger.bind(tag=TAG).info(f"兜底策略，允许访问: {speaker_name}")
        return VoiceprintAccessResult(allowed=True, speaker_name=speaker_name, confidence=confidence)

    async def identify_speaker_with_access_control(self, audio_data: bytes, session_id: str) -> VoiceprintAccessResult:
        """始终进行声纹识别，allowed 只受全局开关和白名单影响"""
        # 如果未配置声纹识别服务，直接返回无说话人
        if not self.enabled:
            logger.bind(tag=TAG).warning("声纹识别服务未配置，跳过识别")
            return VoiceprintAccessResult(allowed=True, speaker_name=None, confidence=0.0)
        try:
            speaker_name, confidence = await self._identify_speaker_with_confidence(audio_data, session_id)
            # 允许打断的判断只在 _check_access_permission
            access_result = self._check_access_permission(speaker_name, confidence)
            return access_result
        except Exception as e:
            logger.bind(tag=TAG).error(f"声纹识别异常: {e}")
            return VoiceprintAccessResult(allowed=True, speaker_name=None, confidence=0.0)

    async def _identify_speaker_with_confidence(self, audio_data: bytes, session_id: str) -> Tuple[Optional[str], float]:
        logger.info(f"请求声纹API: speaker_ids={list(self.speaker_map.keys())}, file大小={len(audio_data)}")
        """识别说话人并返回置信度"""
        if not self.enabled or not self.api_url or not self.api_key:
            logger.bind(tag=TAG).debug("声纹识别功能已禁用或未配置，跳过识别")
            return None, 0.0
            
        try:
            api_start_time = time.monotonic()
            
            # 准备请求头
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Accept': 'application/json'
            }
            
            # 准备multipart/form-data数据
            data = aiohttp.FormData()
            data.add_field('speaker_ids', ','.join(self.speaker_ids))
            data.add_field('file', audio_data, filename='audio.wav', content_type='audio/wav')
            
            timeout = aiohttp.ClientTimeout(total=10)
            
            # 网络请求
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, headers=headers, data=data) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        speaker_id = result.get("speaker_id")
                        score = result.get("score", 0)
                        total_elapsed_time = time.monotonic() - api_start_time
                        
                        logger.bind(tag=TAG).info(f"声纹识别耗时: {total_elapsed_time:.3f}s")
                        
                        # 详细日志：显示API返回的原始数据
                        logger.bind(tag=TAG).info(f"声纹识别API返回: speaker_id={speaker_id}, score={score:.3f}")
                        logger.bind(tag=TAG).info(f"当前配置的说话人: {list(self.speaker_map.keys())}")
                        
                        # 置信度检查 - 如果置信度太低，不相信识别结果
                        if score < self.confidence_threshold:
                            logger.bind(tag=TAG).warning(f"声纹识别置信度过低: {score:.3f}，不相信识别结果")
                            return None, score
                        
                        if speaker_id and speaker_id in self.speaker_map:
                            result_name = self.speaker_map[speaker_id]["name"]
                            logger.bind(tag=TAG).info(f"匹配到说话人: {speaker_id} -> {result_name}")
                            return result_name, score
                        else:
                            logger.bind(tag=TAG).warning(f"未识别的说话人ID: {speaker_id}")
                            return None, score
                    else:
                        logger.bind(tag=TAG).error(f"声纹识别API错误: HTTP {response.status}")
                        return None, 0.0
                        
        except asyncio.TimeoutError:
            elapsed = time.monotonic() - api_start_time
            logger.bind(tag=TAG).error(f"声纹识别超时: {elapsed:.3f}s")
            return None, 0.0
        except Exception as e:
            elapsed = time.monotonic() - api_start_time
            logger.bind(tag=TAG).error(f"声纹识别失败: {e}")
            return None, 0.0
    
    async def identify_speaker(self, audio_data: bytes, session_id: str) -> Optional[str]:
        """识别说话人"""
        if not self.enabled or not self.api_url or not self.api_key:
            logger.bind(tag=TAG).debug("声纹识别功能已禁用或未配置，跳过识别")
            return None
            
        try:
            api_start_time = time.monotonic()
            
            # 准备请求头
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Accept': 'application/json'
            }
            
            # 准备multipart/form-data数据
            data = aiohttp.FormData()
            data.add_field('speaker_ids', ','.join(self.speaker_ids))
            data.add_field('file', audio_data, filename='audio.wav', content_type='audio/wav')
            
            timeout = aiohttp.ClientTimeout(total=10)
            
            # 网络请求
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, headers=headers, data=data) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        speaker_id = result.get("speaker_id")
                        score = result.get("score", 0)
                        total_elapsed_time = time.monotonic() - api_start_time
                        
                        logger.bind(tag=TAG).info(f"声纹识别耗时: {total_elapsed_time:.3f}s")
                        
                        # 详细日志：显示API返回的原始数据
                        logger.bind(tag=TAG).info(f"声纹识别API返回: speaker_id={speaker_id}, score={score:.3f}")
                        logger.bind(tag=TAG).info(f"当前配置的说话人: {list(self.speaker_map.keys())}")
                        
                        # 置信度检查 - 如果置信度太低，不相信识别结果
                        if score < self.confidence_threshold:
                            logger.bind(tag=TAG).warning(f"声纹识别置信度较低: {score:.3f}")
                            return None
                        
                        if speaker_id and speaker_id in self.speaker_map:
                            result_name = self.speaker_map[speaker_id]["name"]
                            logger.bind(tag=TAG).info(f"匹配到说话人: {speaker_id} -> {result_name}")
                            return result_name
                        else:
                            logger.bind(tag=TAG).warning(f"未识别的说话人ID: {speaker_id}")
                            return None
                    else:
                        logger.bind(tag=TAG).error(f"声纹识别API错误: HTTP {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            elapsed = time.monotonic() - api_start_time
            logger.bind(tag=TAG).error(f"声纹识别超时: {elapsed:.3f}s")
            return None
        except Exception as e:
            elapsed = time.monotonic() - api_start_time
            logger.bind(tag=TAG).error(f"声纹识别失败: {e}")
            return None
