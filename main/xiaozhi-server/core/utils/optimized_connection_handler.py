"""
优化的连接处理器
Optimized Connection Handler

将Connection.chat方法中的串行操作改为并行处理，显著提升性能
"""

import asyncio
import time
from typing import Optional, Dict, Any
from config.logger import setup_logging

logger = setup_logging()


class OptimizedConnectionHandler:
    """优化的连接处理器"""
    
    def __init__(self):
        self.performance_stats = {
            "mcp_registration_times": [],
            "memory_query_times": [], 
            "dialogue_compression_times": [],
            "llm_processing_times": [],
            "function_call_times": [],
            "total_chat_times": []
        }
    
    async def optimized_chat(self, conn, query: str, tool_call: bool = False) -> bool:
        """
        优化的chat方法 - 并行处理版本
        
        Args:
            conn: 连接对象
            query: 用户查询
            tool_call: 是否是工具调用
            
        Returns:
            bool: 处理是否成功
        """
        start_time = time.time()
        logger.info(f"开始优化的对话处理: {query}")
        
        try:
            # 阶段1: 并行执行预处理任务
            tasks = []
            
            # 1.1 MCP工具注册（异步）
            if hasattr(conn, "func_handler") and hasattr(conn.func_handler, "before_llm_inference"):
                mcp_task = asyncio.create_task(
                    self._handle_mcp_registration(conn, query)
                )
                tasks.append(("mcp_registration", mcp_task))
            
            # 1.2 内存查询（异步）
            if conn.memory is not None:
                memory_task = asyncio.create_task(
                    self._handle_memory_query(conn, query)
                )
                tasks.append(("memory_query", memory_task))
            
            # 1.3 对话压缩准备（异步）
            compression_task = asyncio.create_task(
                self._prepare_dialogue_compression(conn, query)
            )
            tasks.append(("dialogue_compression", compression_task))
            
            # 阶段2: 等待预处理任务完成
            task_results = {}
            for task_name, task in tasks:
                try:
                    task_results[task_name] = await task
                except Exception as e:
                    logger.error(f"预处理任务失败 {task_name}: {e}")
                    task_results[task_name] = None
            
            # 阶段3: 更新对话状态
            if not tool_call:
                from core.handle.dto import Message
                conn.dialogue.put(Message(role="user", content=query))
            
            # 阶段4: 准备函数调用
            functions = None
            if conn.intent_type == "function_call" and hasattr(conn, "func_handler"):
                functions = conn.func_handler.get_functions()
                if functions:
                    func_names = self._extract_function_names(functions)
                    logger.info(f"可用function names: {func_names}")
            
            # 阶段5: 执行LLM处理
            llm_start = time.time()
            success = await self._process_llm_with_optimization(
                conn, query, functions, task_results
            )
            llm_time = time.time() - llm_start
            self.performance_stats["llm_processing_times"].append(llm_time)
            
            # 记录总时间
            total_time = time.time() - start_time
            self.performance_stats["total_chat_times"].append(total_time)
            
            if total_time > 1.0:  # 慢查询阈值
                logger.warning(f"优化后的对话处理仍然较慢: {total_time:.3f}s")
            else:
                logger.info(f"优化的对话处理完成，总耗时: {total_time:.3f}s")
            
            return success
            
        except Exception as e:
            logger.error(f"优化的对话处理失败: {e}")
            return False
    
    async def _handle_mcp_registration(self, conn, query: str) -> bool:
        """处理MCP工具注册"""
        start_time = time.time()
        
        try:
            await asyncio.wait_for(
                conn.func_handler.before_llm_inference(query),
                timeout=3.0  # 3秒超时
            )
            
            processing_time = time.time() - start_time
            self.performance_stats["mcp_registration_times"].append(processing_time)
            logger.debug(f"MCP工具注册完成，耗时: {processing_time:.3f}s")
            return True
            
        except asyncio.TimeoutError:
            logger.warning("MCP工具注册超时")
            return False
        except Exception as e:
            logger.error(f"MCP工具注册异常: {e}")
            return False
    
    async def _handle_memory_query(self, conn, query: str) -> Optional[str]:
        """处理内存查询"""
        start_time = time.time()
        
        try:
            memory_str = await asyncio.wait_for(
                conn.memory.query_memory(query),
                timeout=1.0  # 1秒超时
            )
            
            processing_time = time.time() - start_time
            self.performance_stats["memory_query_times"].append(processing_time)
            logger.debug(f"内存查询完成，耗时: {processing_time:.3f}s")
            return memory_str
            
        except asyncio.TimeoutError:
            logger.warning("内存查询超时")
            return None
        except Exception as e:
            logger.error(f"内存查询异常: {e}")
            return None
    
    async def _prepare_dialogue_compression(self, conn, query: str) -> Optional[Any]:
        """准备对话压缩"""
        start_time = time.time()
        
        try:
            # 这里可以预处理一些对话压缩的准备工作
            # 具体实现取决于dialogue_compressor的接口
            await asyncio.sleep(0.01)  # 模拟少量预处理工作
            
            processing_time = time.time() - start_time
            self.performance_stats["dialogue_compression_times"].append(processing_time)
            logger.debug(f"对话压缩准备完成，耗时: {processing_time:.3f}s")
            return True
            
        except Exception as e:
            logger.error(f"对话压缩准备异常: {e}")
            return None
    
    def _extract_function_names(self, functions) -> list:
        """提取函数名称"""
        func_names = []
        for f in functions:
            if isinstance(f, dict):
                if 'name' in f:
                    func_names.append(f['name'])
                elif 'function' in f and 'name' in f['function']:
                    func_names.append(f['function']['name'])
        return func_names
    
    async def _process_llm_with_optimization(
        self, 
        conn, 
        query: str, 
        functions: Optional[list],
        task_results: Dict[str, Any]
    ) -> bool:
        """优化的LLM处理"""
        try:
            # 使用预处理结果
            memory_str = task_results.get("memory_query")
            
            # 执行对话压缩
            if hasattr(conn, 'dialogue_compressor'):
                compressed_dialogue = conn.dialogue_compressor.compress_dialogue(
                    conn.dialogue.get_llm_dialogue_with_memory(memory_str),
                    current_query=query
                )
            else:
                compressed_dialogue = conn.dialogue.get_llm_dialogue_with_memory(memory_str)
            
            # 准备响应消息列表
            response_message = []
            
            # 执行LLM推理（这部分可能需要根据实际实现调整）
            if hasattr(conn, 'llm_provider'):
                # 假设存在LLM提供者
                response = await conn.llm_provider.chat_completion(
                    messages=compressed_dialogue,
                    functions=functions,
                    stream=True
                )
                
                # 处理流式响应
                async for chunk in response:
                    # 处理响应块
                    response_message.append(chunk)
                    
                    # 检查函数调用
                    if self._should_handle_function_call(chunk):
                        await self._handle_function_call_async(conn, chunk)
            
            return True
            
        except Exception as e:
            logger.error(f"LLM处理异常: {e}")
            return False
    
    def _should_handle_function_call(self, chunk) -> bool:
        """检查是否需要处理函数调用"""
        # 这里需要根据实际的响应格式来判断
        return False
    
    async def _handle_function_call_async(self, conn, function_call_data):
        """异步处理函数调用"""
        start_time = time.time()
        
        try:
            # 异步执行函数调用
            if hasattr(conn, 'func_handler'):
                result = await conn.func_handler.handle_llm_function_call(
                    conn, function_call_data
                )
                
                processing_time = time.time() - start_time
                self.performance_stats["function_call_times"].append(processing_time)
                logger.debug(f"函数调用完成，耗时: {processing_time:.3f}s")
                return result
            
        except Exception as e:
            logger.error(f"函数调用异常: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {}
        
        for stage, times in self.performance_stats.items():
            if times:
                stats[stage] = {
                    "count": len(times),
                    "avg": sum(times) / len(times),
                    "min": min(times),
                    "max": max(times),
                    "total": sum(times),
                    "p95": sorted(times)[int(len(times) * 0.95)] if len(times) > 1 else times[0]
                }
            else:
                stats[stage] = {
                    "count": 0,
                    "avg": 0,
                    "min": 0,
                    "max": 0,
                    "total": 0,
                    "p95": 0
                }
        
        return stats
    
    def reset_stats(self):
        """重置统计数据"""
        for key in self.performance_stats:
            self.performance_stats[key].clear()
    
    def print_performance_summary(self):
        """打印性能摘要"""
        stats = self.get_performance_stats()
        
        print("\n🚀 优化连接处理器性能报告:")
        print("=" * 50)
        
        for stage, data in stats.items():
            if data["count"] > 0:
                print(f"\n📊 {stage}:")
                print(f"  调用次数: {data['count']}")
                print(f"  平均耗时: {data['avg']:.3f}s")
                print(f"  最小耗时: {data['min']:.3f}s")
                print(f"  最大耗时: {data['max']:.3f}s")
                print(f"  P95耗时: {data['p95']:.3f}s")
                print(f"  总耗时: {data['total']:.3f}s")
        
        # 计算优化效果
        total_stats = stats.get("total_chat_times", {})
        if total_stats.get("count", 0) > 0:
            avg_time = total_stats["avg"]
            improvement = max(0, (1.461 - avg_time) / 1.461 * 100)
            print(f"\n⚡ 性能提升:")
            print(f"  原始平均耗时: 1.461s")
            print(f"  优化后平均耗时: {avg_time:.3f}s")
            print(f"  性能提升: {improvement:.1f}%")


# 全局优化处理器实例
optimized_handler = OptimizedConnectionHandler()