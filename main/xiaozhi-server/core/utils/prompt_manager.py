"""
系统提示词管理器模块
负责管理和更新系统提示词，包括快速初始化和异步增强功能
"""

import os
import cnlunar
from typing import Dict, Any
from config.logger import setup_logging
from jinja2 import Template

TAG = __name__

WEEKDAY_MAP = {
    "Monday": "星期一",
    "Tuesday": "星期二",
    "Wednesday": "星期三",
    "Thursday": "星期四",
    "Friday": "星期五",
    "Saturday": "星期六",
    "Sunday": "星期日",
}

EMOJI_List = [
    "😶",
    "🙂",
    "😆",
    "😂",
    "😔",
    "😠",
    "😭",
    "😍",
    "😳",
    "😲",
    "😱",
    "🤔",
    "😉",
    "😎",
    "😌",
    "🤤",
    "😘",
    "😏",
    "😴",
    "😜",
    "🙄",
]


class PromptManager:
    """系统提示词管理器，负责管理和更新系统提示词"""

    def __init__(self, config: Dict[str, Any], logger=None):
        self.config = config
        self.logger = logger or setup_logging()
        self.base_prompt_template = None
        self.text_prompt_template = None  # 文本聊天专用模板
        self.text_chat_prompt_template = None  # 文本聊天模式专用模板
        self.base_chat_prompt_template = None  # 基础聊天模式专用模板
        self.last_update_time = 0

        # 导入全局缓存管理器
        from core.utils.cache.manager import cache_manager, CacheType

        self.cache_manager = cache_manager
        self.CacheType = CacheType

        self._load_base_template()
        self._load_text_template()
        self._load_text_chat_template()
        self._load_base_chat_template()

    def _load_base_template(self):
        """加载基础提示词模板"""
        try:
            template_path = "agent-base-prompt.txt"
            cache_key = f"prompt_template:{template_path}"

            # 先从缓存获取
            cached_template = self.cache_manager.get(self.CacheType.CONFIG, cache_key)
            if cached_template is not None:
                self.base_prompt_template = cached_template
                self.logger.bind(tag=TAG).debug("从缓存加载基础提示词模板")
                return

            # 缓存未命中，从文件读取
            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    template_content = f.read()

                # 存入缓存（CONFIG类型默认不自动过期，需要手动失效）
                self.cache_manager.set(
                    self.CacheType.CONFIG, cache_key, template_content
                )
                self.base_prompt_template = template_content
                self.logger.bind(tag=TAG).debug("成功加载基础提示词模板并缓存")
            else:
                self.logger.bind(tag=TAG).warning("未找到agent-base-prompt.txt文件")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载提示词模板失败: {e}")

    def _load_text_template(self):
        """加载文本聊天专用提示词模板"""
        try:
            template_path = "agent-text-prompt.txt"
            cache_key = f"text_prompt_template:{template_path}"

            # 先从缓存获取
            cached_template = self.cache_manager.get(self.CacheType.CONFIG, cache_key)
            if cached_template is not None:
                self.text_prompt_template = cached_template
                self.logger.bind(tag=TAG).debug("从缓存加载文本提示词模板")
                return

            # 缓存未命中，从文件读取
            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    template_content = f.read()

                # 存入缓存（CONFIG类型默认不自动过期，需要手动失效）
                self.cache_manager.set(
                    self.CacheType.CONFIG, cache_key, template_content
                )
                self.text_prompt_template = template_content
                self.logger.bind(tag=TAG).debug("成功加载文本提示词模板并缓存")
            else:
                self.logger.bind(tag=TAG).warning("未找到agent-text-prompt.txt文件")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载文本提示词模板失败: {e}")

    def _load_text_chat_template(self):
        """加载文本聊天模式专用提示词模板"""
        try:
            template_path = "agent-text-chat-prompt.txt"
            cache_key = f"text_chat_prompt_template:{template_path}"

            # 先从缓存获取
            cached_template = self.cache_manager.get(self.CacheType.CONFIG, cache_key)
            if cached_template is not None:
                self.text_chat_prompt_template = cached_template
                self.logger.bind(tag=TAG).debug("从缓存加载文本聊天模式提示词模板")
                return

            # 缓存未命中，从文件读取
            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    template_content = f.read()

                # 存入缓存（CONFIG类型默认不自动过期，需要手动失效）
                self.cache_manager.set(
                    self.CacheType.CONFIG, cache_key, template_content
                )
                self.text_chat_prompt_template = template_content
                self.logger.bind(tag=TAG).debug("成功加载文本聊天模式提示词模板并缓存")
            else:
                self.logger.bind(tag=TAG).warning("未找到agent-text-chat-prompt.txt文件")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载文本聊天模式提示词模板失败: {e}")

    def _load_base_chat_template(self):
        """加载基础聊天模式提示词模板"""
        try:
            template_path = "agent-base-chat-prompt.txt"
            cache_key = f"base_chat_prompt_template:{template_path}"

            # 先从缓存获取
            cached_template = self.cache_manager.get(self.CacheType.CONFIG, cache_key)
            if cached_template is not None:
                self.base_chat_prompt_template = cached_template
                self.logger.bind(tag=TAG).debug("从缓存加载基础聊天模式提示词模板")
                return

            # 缓存未命中，从文件读取
            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    template_content = f.read()

                # 存入缓存（CONFIG类型默认不自动过期，需要手动失效）
                self.cache_manager.set(
                    self.CacheType.CONFIG, cache_key, template_content
                )
                self.base_chat_prompt_template = template_content
                self.logger.bind(tag=TAG).debug("成功加载基础聊天模式提示词模板并缓存")
            else:
                self.logger.bind(tag=TAG).warning("未找到agent-base-chat-prompt.txt文件")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载基础聊天模式提示词模板失败: {e}")

    def get_quick_prompt(self, user_prompt: str, device_id: str = None) -> str:
        """快速获取系统提示词（使用用户配置）"""
        device_cache_key = f"device_prompt:{device_id}"
        cached_device_prompt = self.cache_manager.get(
            self.CacheType.DEVICE_PROMPT, device_cache_key
        )
        if cached_device_prompt is not None:
            self.logger.bind(tag=TAG).debug(f"使用设备 {device_id} 的缓存提示词")
            return cached_device_prompt
        else:
            self.logger.bind(tag=TAG).debug(
                f"设备 {device_id} 无缓存提示词，使用传入的提示词"
            )

        # 使用传入的提示词并缓存（如果有设备ID）
        if device_id:
            device_cache_key = f"device_prompt:{device_id}"
            self.cache_manager.set(self.CacheType.CONFIG, device_cache_key, user_prompt)
            self.logger.bind(tag=TAG).debug(f"设备 {device_id} 的提示词已缓存")

        self.logger.bind(tag=TAG).info(f"使用快速提示词: {user_prompt[:50]}...")
        return user_prompt

    def get_text_prompt(self, user_prompt: str, device_id: str = None, client_ip: str = None, private_config: dict = None) -> str:
        """获取文本聊天专用提示词"""
        if not self.text_prompt_template:
            self.logger.bind(tag=TAG).warning("文本提示词模板未加载，使用原始提示词")
            return user_prompt

        try:
            # KV Cache优化：不再获取动态时间和天气信息，这些信息通过MCP工具提供
            # 获取最新的时间信息（不缓存）
            # current_time, today_date, today_weekday, lunar_date = (
            #     self._get_current_time_info()
            # )

            # 获取缓存的上下文信息（保留地区信息，因为它是系统已获取的）
            local_address = ""
            # weather_info = ""

            if client_ip:
                # 获取位置信息（从全局缓存）
                local_address = (
                    self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
                )

            #     # 获取天气信息（从全局缓存）
            #     if local_address:
            #         weather_info = (
            #             self.cache_manager.get(self.CacheType.WEATHER, local_address)
            #             or ""
            #         )

            # 处理计算机设备信息
            computer_device_info = self._get_computer_device_info(private_config)

            # 替换模板变量（KV Cache优化：保留地区信息，其他动态信息通过工具获取）
            template = Template(self.text_prompt_template)
            text_prompt = template.render(
                base_prompt=user_prompt,
                local_address=local_address,
                computer_device_info=computer_device_info,
            )

            self.logger.bind(tag=TAG).info(
                f"构建文本提示词成功，长度: {len(text_prompt)}"
            )
            return text_prompt

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"构建文本提示词失败: {e}")
            return user_prompt

    def get_text_chat_prompt(self, user_prompt: str, device_id: str = None, client_ip: str = None, private_config: dict = None) -> str:
        """获取文本聊天模式专用提示词"""
        if not self.text_chat_prompt_template:
            self.logger.bind(tag=TAG).warning("文本聊天模式提示词模板未加载，使用原始提示词")
            return user_prompt

        try:
            # KV Cache优化：不再获取动态时间和天气信息，这些信息通过MCP工具提供
            # 获取最新的时间信息（不缓存）
            # current_time, today_date, today_weekday, lunar_date = (
            #     self._get_current_time_info()
            # )

            # 获取缓存的上下文信息
            # local_address = ""
            # weather_info = ""

            # if client_ip:
            #     # 获取位置信息（从全局缓存）
            #     local_address = (
            #         self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
            #     )

            #     # 获取天气信息（从全局缓存）
            #     if local_address:
            #         weather_info = (
            #             self.cache_manager.get(self.CacheType.WEATHER, local_address)
            #             or ""
            #         )

            # 获取缓存的上下文信息（保留地区信息，因为它是系统已获取的）
            local_address = ""

            if client_ip:
                # 获取位置信息（从全局缓存）
                local_address = (
                    self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
                )

            # 替换模板变量（KV Cache优化：聊天模式保留地区信息，其他动态信息通过工具获取）
            template = Template(self.text_chat_prompt_template)
            text_chat_prompt = template.render(
                base_prompt=user_prompt,
                local_address=local_address,
            )

            self.logger.bind(tag=TAG).info(
                f"构建文本聊天模式提示词成功，长度: {len(text_chat_prompt)}"
            )
            return text_chat_prompt

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"构建文本聊天模式提示词失败: {e}")
            return user_prompt

    def _get_current_time_info(self) -> tuple:
        """获取当前时间信息"""
        from datetime import datetime

        now = datetime.now()
        current_time = now.strftime("%H:%M")
        today_date = now.strftime("%Y-%m-%d")
        today_weekday = WEEKDAY_MAP[now.strftime("%A")]
        today_lunar = cnlunar.Lunar(now, godType="8char")
        lunar_date = "%s年%s%s\n" % (
            today_lunar.lunarYearCn,
            today_lunar.lunarMonthCn[:-1],
            today_lunar.lunarDayCn,
        )

        return current_time, today_date, today_weekday, lunar_date

    def _get_location_info(self, client_ip: str) -> str:
        """获取位置信息"""
        try:
            # 先从缓存获取
            cached_location = self.cache_manager.get(self.CacheType.LOCATION, client_ip)
            if cached_location is not None:
                return cached_location

            # 缓存未命中，调用API获取
            from core.utils.util import get_ip_info

            ip_info = get_ip_info(client_ip, self.logger)
            city = ip_info.get("city", "未知位置")
            location = f"{city}"

            # 存入缓存
            self.cache_manager.set(self.CacheType.LOCATION, client_ip, location)
            return location
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"获取位置信息失败: {e}")
            return "未知位置"

    def _get_weather_info(self, conn, location: str) -> str:
        """获取天气信息"""
        try:
            # 检查插件配置是否存在
            if not hasattr(conn, 'config') or not conn.config.get("plugins", {}).get("get_weather"):
                self.logger.bind(tag=TAG).debug("天气插件配置未加载或不存在，跳过天气信息获取")
                return "天气信息配置未加载"
            
            # 先从缓存获取
            cached_weather = self.cache_manager.get(self.CacheType.WEATHER, location)
            if cached_weather is not None:
                return cached_weather

            # 缓存未命中，调用get_weather函数获取
            from plugins_func.functions.get_weather import get_weather
            from plugins_func.register import ActionResponse

            # 调用get_weather函数
            result = get_weather(conn, location=location, lang="zh_CN")
            if isinstance(result, ActionResponse):
                weather_report = result.result
                self.cache_manager.set(self.CacheType.WEATHER, location, weather_report)
                return weather_report
            return "天气信息获取失败"

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"获取天气信息失败: {e}")
            return "天气信息获取失败"

    def update_context_info(self, conn, client_ip: str):
        """同步更新上下文信息（KV Cache优化：不再预加载动态信息）"""
        try:
            # KV Cache优化：不再预加载位置和天气信息，这些信息通过MCP工具按需获取
            # 获取位置信息（使用全局缓存）
            # local_address = self._get_location_info(client_ip)
            # 获取天气信息（使用全局缓存）
            # self._get_weather_info(conn, local_address)
            self.logger.bind(tag=TAG).info(f"上下文信息更新完成（KV Cache优化模式）")

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"更新上下文信息失败: {e}")

    def build_enhanced_prompt(
        self, user_prompt: str, device_id: str, client_ip: str = None, private_config: dict = None
    ) -> str:
        """构建增强的系统提示词"""
        if not self.base_prompt_template:
            return user_prompt

        try:
            # KV Cache优化：不再获取动态时间和天气信息，这些信息通过MCP工具提供
            # 获取最新的时间信息（不缓存）
            # current_time, today_date, today_weekday, lunar_date = (
            #     self._get_current_time_info()
            # )

            # 获取缓存的上下文信息
            # local_address = ""
            # weather_info = ""

            # if client_ip:
            #     # 获取位置信息（从全局缓存）
            #     local_address = (
            #         self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
            #     )

            #     # 获取天气信息（从全局缓存）
            #     if local_address:
            #         weather_info = (
            #             self.cache_manager.get(self.CacheType.WEATHER, local_address)
            #             or ""
            #         )

            # 处理计算机设备信息
            computer_device_info = self._get_computer_device_info(private_config)

            # 获取缓存的上下文信息（保留地区信息，因为它是系统已获取的）
            local_address = ""

            if client_ip:
                # 获取位置信息（从全局缓存）
                local_address = (
                    self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
                )

            # 替换模板变量（KV Cache优化：保留地区信息，其他动态信息通过工具获取）
            template = Template(self.base_prompt_template)
            enhanced_prompt = template.render(
                base_prompt=user_prompt,
                local_address=local_address,
                computer_device_info=computer_device_info,
                emojiList=EMOJI_List,
            )
            device_cache_key = f"device_prompt:{device_id}"
            self.cache_manager.set(
                self.CacheType.DEVICE_PROMPT, device_cache_key, enhanced_prompt
            )
            self.logger.bind(tag=TAG).info(
                f"构建增强提示词成功，长度: {len(enhanced_prompt)}"
            )
            return enhanced_prompt

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"构建增强提示词失败: {e}")
            return user_prompt

    def build_base_chat_prompt(
        self, user_prompt: str, device_id: str, client_ip: str = None, private_config: dict = None
    ) -> str:
        """构建基础聊天模式的系统提示词"""
        if not self.base_chat_prompt_template:
            self.logger.bind(tag=TAG).warning("基础聊天模式提示词模板未加载，使用原始提示词")
            return user_prompt

        try:
            # KV Cache优化：不再获取动态时间和天气信息，这些信息通过MCP工具提供
            # 获取最新的时间信息（不缓存）
            # current_time, today_date, today_weekday, lunar_date = (
            #     self._get_current_time_info()
            # )

            # 获取缓存的上下文信息
            # local_address = ""
            # weather_info = ""

            # if client_ip:
            #     # 获取位置信息（从全局缓存）
            #     local_address = (
            #         self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
            #     )

            #     # 获取天气信息（从全局缓存）
            #     if local_address:
            #         weather_info = (
            #             self.cache_manager.get(self.CacheType.WEATHER, local_address)
            #             or ""
            #         )

            # 获取缓存的上下文信息（保留地区信息，因为它是系统已获取的）
            local_address = ""

            if client_ip:
                # 获取位置信息（从全局缓存）
                local_address = (
                    self.cache_manager.get(self.CacheType.LOCATION, client_ip) or ""
                )

            # 替换模板变量（KV Cache优化：聊天模式保留地区信息，其他动态信息通过工具获取）
            template = Template(self.base_chat_prompt_template)
            base_chat_prompt = template.render(
                base_prompt=user_prompt,
                local_address=local_address,
            )
            device_cache_key = f"device_chat_prompt:{device_id}"
            self.cache_manager.set(
                self.CacheType.DEVICE_PROMPT, device_cache_key, base_chat_prompt
            )
            self.logger.bind(tag=TAG).info(
                f"构建基础聊天模式提示词成功，长度: {len(base_chat_prompt)}"
            )
            return base_chat_prompt

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"构建基础聊天模式提示词失败: {e}")
            return user_prompt

    def _get_computer_device_info(self, private_config: dict) -> str:
        """获取计算机设备信息"""
        if not private_config or not private_config.get("computer_device"):
            return ""

        try:
            computer_device = private_config["computer_device"]
            os_name = computer_device.get("osName", "")
            os_arch = computer_device.get("osArch", "")
            user_home_dir_raw = computer_device.get("userHomeDir", "")

            # 解析user_home_dir JSON数组字符串
            home_dir_info = ""
            if user_home_dir_raw:
                try:
                    import json
                    user_dirs = json.loads(user_home_dir_raw)
                    if isinstance(user_dirs, list) and len(user_dirs) >= 3:
                        home_dir = user_dirs[0]  # 主目录
                        desktop_dir = user_dirs[1]  # 桌面目录
                        documents_dir = user_dirs[2]  # 文档目录
                        home_dir_info = f"主目录是 {home_dir}，桌面目录是 {desktop_dir}，文档目录是 {documents_dir}"
                    elif isinstance(user_dirs, list) and len(user_dirs) > 0:
                        # 如果数组长度不足3，至少显示主目录
                        home_dir = user_dirs[0]
                        home_dir_info = f"主目录是 {home_dir}"
                    else:
                        home_dir_info = f"主目录是 {user_home_dir_raw}"
                except (json.JSONDecodeError, TypeError, IndexError) as e:
                    # 如果解析失败，当作普通字符串处理
                    self.logger.bind(tag=TAG).warning(f"解析user_home_dir失败，当作字符串处理: {e}")
                    home_dir_info = f"主目录是 {user_home_dir_raw}"

            if os_name or os_arch or home_dir_info:
                device_info = f"\n\n- **我的计算机环境信息如下： ** 操作系统是{os_name}，系统架构是：{os_arch}，{home_dir_info}。**由于已经识别出计算机环境信息，无需再调用获取系统信息、目录信息等相关工具。**"
                self.logger.bind(tag=TAG).debug(f"添加计算机设备信息: {device_info}")
                return device_info

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理计算机设备信息失败: {e}")

        return ""
