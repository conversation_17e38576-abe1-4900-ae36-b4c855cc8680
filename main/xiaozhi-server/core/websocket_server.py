import asyncio
import websockets
import logging
from config.logger import setup_logging
from core.connection import ConnectionHandler
from config.config_loader import get_config_from_api
from core.utils.modules_initialize import initialize_modules
from core.utils.util import check_vad_update, check_asr_update

TAG = __name__


class WebSocketServer:
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging()
        self.config_lock = asyncio.Lock()

        # 设置websockets库的日志级别，减少握手失败的噪音日志
        websockets_logger = logging.getLogger('websockets')
        websockets_logger.setLevel(logging.WARNING)

        modules = initialize_modules(
            self.logger,
            self.config,
            "VAD" in self.config["selected_module"],
            "ASR" in self.config["selected_module"],
            "LLM" in self.config["selected_module"],
            False,
            "Memory" in self.config["selected_module"],
            "Intent" in self.config["selected_module"],
        )
        self._vad = modules["vad"] if "vad" in modules else None
        self._asr = modules["asr"] if "asr" in modules else None
        self._llm = modules["llm"] if "llm" in modules else None
        self._intent = modules["intent"] if "intent" in modules else None
        self._memory = modules["memory"] if "memory" in modules else None

        self.active_connections = set()

    async def _safe_handle_connection(self, websocket):
        """安全的连接处理包装器，捕获握手异常"""
        try:
            await self._handle_connection(websocket)
        except websockets.exceptions.InvalidMessage as e:
            # 握手失败，记录警告但不影响服务器运行
            client_info = "unknown"
            try:
                if hasattr(websocket, 'remote_address') and websocket.remote_address:
                    client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
            except:
                pass
            self.logger.bind(tag=TAG).warning(f"客户端 {client_info} WebSocket握手失败: {e}")
        except websockets.exceptions.ConnectionClosed as e:
            # 连接关闭，正常情况
            self.logger.bind(tag=TAG).debug(f"WebSocket连接正常关闭: {e}")
        except Exception as e:
            # 其他异常
            self.logger.bind(tag=TAG).error(f"WebSocket连接处理异常: {e}")

    async def start(self):
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("port", 8000))

        # 创建WebSocket服务器，添加更好的错误处理
        async with websockets.serve(
            self._safe_handle_connection,
            host,
            port,
            process_request=self._http_response,
            # 添加连接超时和大小限制
            close_timeout=10,
            max_size=1000000000,
            # 添加ping设置以检测连接状态
            ping_interval=30,
            ping_timeout=10
        ):
            self.logger.bind(tag=TAG).info(f"WebSocket服务器启动成功，监听 {host}:{port}")
            await asyncio.Future()

    async def _handle_connection(self, websocket):
        """处理新连接，每次创建独立的ConnectionHandler"""
        client_info = "unknown"
        try:
            # 获取客户端信息用于日志记录
            if hasattr(websocket, 'remote_address') and websocket.remote_address:
                client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
            elif hasattr(websocket, 'request') and websocket.request:
                client_info = f"{websocket.request.remote_addr}"

            self.logger.bind(tag=TAG).debug(f"新的WebSocket连接来自: {client_info}")

            # 创建ConnectionHandler时传入当前server实例
            handler = ConnectionHandler(
                self.config,
                self._vad,
                self._asr,
                self._llm,
                self._memory,
                self._intent,
                self,  # 传入server实例
            )
            self.active_connections.add(handler)

            try:
                await handler.handle_connection(websocket)
                self.logger.bind(tag=TAG).debug(f"连接处理完成: {client_info}")
            except websockets.exceptions.ConnectionClosed as e:
                self.logger.bind(tag=TAG).info(f"客户端 {client_info} 正常断开连接: {e}")
            except websockets.exceptions.InvalidMessage as e:
                self.logger.bind(tag=TAG).warning(f"客户端 {client_info} 发送无效消息: {e}")
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"处理连接 {client_info} 时出错: {e}")
            finally:
                # 确保从活动连接集合中移除
                self.active_connections.discard(handler)

        except Exception as outer_e:
            self.logger.bind(tag=TAG).error(f"连接处理外层异常 {client_info}: {outer_e}")
        finally:
            # 强制关闭连接（如果还没有关闭的话）
            try:
                # 安全地检查WebSocket状态并关闭
                if hasattr(websocket, "closed") and not websocket.closed:
                    await websocket.close()
                    self.logger.bind(tag=TAG).debug(f"强制关闭连接: {client_info}")
                elif hasattr(websocket, "state") and websocket.state.name != "CLOSED":
                    await websocket.close()
                    self.logger.bind(tag=TAG).debug(f"强制关闭连接: {client_info}")
            except Exception as close_error:
                self.logger.bind(tag=TAG).debug(f"关闭连接 {client_info} 时出错: {close_error}")

    async def _http_response(self, websocket, request_headers):
        """处理HTTP请求，区分WebSocket升级请求和普通HTTP请求"""
        try:
            # 获取客户端信息用于日志
            client_info = "unknown"
            if hasattr(websocket, 'remote_address') and websocket.remote_address:
                client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"

            # 记录请求详情用于调试
            connection_header = request_headers.headers.get("connection", "").lower()
            upgrade_header = request_headers.headers.get("upgrade", "").lower()

            self.logger.bind(tag=TAG).debug(
                f"HTTP请求来自 {client_info}: Connection={connection_header}, Upgrade={upgrade_header}"
            )

            # 检查是否为 WebSocket 升级请求
            if "upgrade" in connection_header and "websocket" in upgrade_header:
                # 如果是 WebSocket 请求，返回 None 允许握手继续
                self.logger.bind(tag=TAG).debug(f"允许WebSocket握手: {client_info}")
                return None
            else:
                # 如果是普通 HTTP 请求，返回 "server is running"
                self.logger.bind(tag=TAG).debug(f"响应HTTP请求: {client_info}")
                return websocket.respond(200, "Server is running\n")

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理HTTP请求时出错: {e}")
            # 出错时拒绝连接
            try:
                return websocket.respond(400, "Bad Request\n")
            except:
                return None

    async def update_config(self) -> bool:
        """更新服务器配置并重新初始化组件

        Returns:
            bool: 更新是否成功
        """
        try:
            async with self.config_lock:
                # 重新获取配置
                new_config = get_config_from_api(self.config)
                if new_config is None:
                    self.logger.bind(tag=TAG).error("获取新配置失败")
                    return False
                self.logger.bind(tag=TAG).info(f"获取新配置成功")
                # 检查 VAD 和 ASR 类型是否需要更新
                update_vad = check_vad_update(self.config, new_config)
                update_asr = check_asr_update(self.config, new_config)
                self.logger.bind(tag=TAG).info(
                    f"检查VAD和ASR类型是否需要更新: {update_vad} {update_asr}"
                )
                # 更新配置
                self.config = new_config
                # 重新初始化组件
                modules = initialize_modules(
                    self.logger,
                    new_config,
                    update_vad,
                    update_asr,
                    "LLM" in new_config["selected_module"],
                    False,
                    "Memory" in new_config["selected_module"],
                    "Intent" in new_config["selected_module"],
                )

                # 更新组件实例
                if "vad" in modules:
                    self._vad = modules["vad"]
                if "asr" in modules:
                    self._asr = modules["asr"]
                if "llm" in modules:
                    self._llm = modules["llm"]
                if "intent" in modules:
                    self._intent = modules["intent"]
                if "memory" in modules:
                    self._memory = modules["memory"]
                self.logger.bind(tag=TAG).info(f"更新配置任务执行完毕")
                return True
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"更新服务器配置失败: {str(e)}")
            return False
