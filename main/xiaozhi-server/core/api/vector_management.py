"""
向量管理API
提供向量的增删改查接口
"""
import json
import traceback
from typing import List, Dict, Any, Optional
from aiohttp import web
from loguru import logger
from core.providers.memory.milvus_vector_store import MilvusVectorStore
from core.providers.memory.mem0ai_local.mem0ai_local import MemoryProvider
import asyncio

TAG = "VectorAPI"

class VectorManagementAPI:
    """向量管理API类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化向量管理API
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.milvus_config = config.get("milvus", {
            "host": "localhost",
            "port": 19530,
            "collection_name": "xiaozhi_vectors",
            "dimension": 1536
        })
        
        # 初始化Milvus向量存储
        self.vector_store = None
        self._init_vector_store()
        
        # 初始化embedding模型（用于文本向量化）
        self.embedding_model = None
        self._init_embedding_model()
    
    def _init_vector_store(self):
        """初始化向量存储"""
        try:
            self.vector_store = MilvusVectorStore(self.milvus_config)
            logger.bind(tag=TAG).info("Milvus向量存储初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"初始化Milvus向量存储失败: {str(e)}")
            raise
    
    def _init_embedding_model(self):
        """初始化embedding模型"""
        try:
            # 这里可以根据配置选择不同的embedding模型
            from openai import OpenAI
            
            # 从配置中获取OpenAI API密钥
            api_key = self.config.get("openai_api_key", "")
            if not api_key:
                import os
                api_key = os.getenv("OPENAI_API_KEY", "")
            
            if api_key:
                self.embedding_model = OpenAI(api_key=api_key)
                logger.bind(tag=TAG).info("OpenAI embedding模型初始化成功")
            else:
                logger.bind(tag=TAG).warning("未配置OpenAI API密钥，文本向量化功能将不可用")
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"初始化embedding模型失败: {str(e)}")
    
    async def _text_to_vector(self, text: str) -> List[float]:
        """将文本转换为向量"""
        try:
            if not self.embedding_model:
                raise ValueError("Embedding模型未初始化")
            
            # 使用OpenAI embedding API
            response = self.embedding_model.embeddings.create(
                input=text,
                model="text-embedding-ada-002"
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"文本向量化失败: {str(e)}")
            raise
    
    async def add_vector(self, request):
        """
        添加向量接口
        POST /api/vectors/add
        
        请求体:
        {
            "text": "要向量化的文本",
            "vector": [可选，直接提供向量],
            "metadata": {"key": "value"},
            "user_id": "用户ID"
        }
        """
        try:
            data = await request.json()
            
            # 验证必需参数
            text = data.get("text")
            vector = data.get("vector")
            metadata = data.get("metadata", {})
            user_id = data.get("user_id", "default")
            
            if not text and not vector:
                return web.json_response({
                    "success": False,
                    "error": "必须提供text或vector参数"
                }, status=400)
            
            # 获取或生成向量
            if vector:
                if not isinstance(vector, list) or len(vector) != self.milvus_config["dimension"]:
                    return web.json_response({
                        "success": False,
                        "error": f"向量维度必须为{self.milvus_config['dimension']}"
                    }, status=400)
                final_vector = vector
            else:
                # 将文本转换为向量
                final_vector = await self._text_to_vector(text)
            
            # 添加到向量存储
            ids = self.vector_store.add_vectors(
                vectors=[final_vector],
                texts=[text or ""],
                metadatas=[metadata],
                user_id=user_id
            )
            
            return web.json_response({
                "success": True,
                "data": {
                    "id": ids[0],
                    "message": "向量添加成功"
                }
            })
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"添加向量失败: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            return web.json_response({
                "success": False,
                "error": f"添加向量失败: {str(e)}"
            }, status=500)
    
    async def delete_vector(self, request):
        """
        删除向量接口
        DELETE /api/vectors/delete
        
        请求体:
        {
            "ids": ["id1", "id2", ...]
        }
        """
        try:
            data = await request.json()
            
            # 验证参数
            ids = data.get("ids", [])
            if not ids or not isinstance(ids, list):
                return web.json_response({
                    "success": False,
                    "error": "必须提供有效的ids列表"
                }, status=400)
            
            # 删除向量
            success = self.vector_store.delete_vectors(ids)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": {
                        "deleted_count": len(ids),
                        "message": "向量删除成功"
                    }
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": "删除向量失败"
                }, status=500)
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"删除向量失败: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            return web.json_response({
                "success": False,
                "error": f"删除向量失败: {str(e)}"
            }, status=500)
    
    async def update_vector(self, request):
        """
        更新向量接口
        PUT /api/vectors/update
        
        请求体:
        {
            "id": "向量ID",
            "text": "新文本（可选）",
            "vector": "新向量（可选）",
            "metadata": {"key": "value"}
        }
        """
        try:
            data = await request.json()
            
            # 验证参数
            vector_id = data.get("id")
            if not vector_id:
                return web.json_response({
                    "success": False,
                    "error": "必须提供向量ID"
                }, status=400)
            
            text = data.get("text")
            vector = data.get("vector")
            metadata = data.get("metadata")
            
            # 如果提供了新文本，生成新向量
            if text and not vector:
                vector = await self._text_to_vector(text)
            
            # 更新向量
            success = self.vector_store.update_vector(
                vector_id=vector_id,
                vector=vector,
                text=text,
                metadata=metadata
            )
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": {
                        "id": vector_id,
                        "message": "向量更新成功"
                    }
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": "更新向量失败，可能向量不存在"
                }, status=404)
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"更新向量失败: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            return web.json_response({
                "success": False,
                "error": f"更新向量失败: {str(e)}"
            }, status=500)
    
    async def search_vectors(self, request):
        """
        搜索向量接口
        POST /api/vectors/search
        
        请求体:
        {
            "query": "查询文本",
            "query_vector": [可选，直接提供查询向量],
            "top_k": 10,
            "user_id": "用户ID（可选）"
        }
        """
        try:
            data = await request.json()
            
            # 验证参数
            query = data.get("query")
            query_vector = data.get("query_vector")
            top_k = data.get("top_k", 10)
            user_id = data.get("user_id")
            
            if not query and not query_vector:
                return web.json_response({
                    "success": False,
                    "error": "必须提供query或query_vector参数"
                }, status=400)
            
            # 获取查询向量
            if query_vector:
                final_query_vector = query_vector
            else:
                final_query_vector = await self._text_to_vector(query)
            
            # 搜索向量
            results = self.vector_store.search_vectors(
                query_vector=final_query_vector,
                top_k=top_k,
                user_id=user_id
            )
            
            return web.json_response({
                "success": True,
                "data": {
                    "results": results,
                    "total": len(results)
                }
            })
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"搜索向量失败: {str(e)}")
            logger.bind(tag=TAG).error(f"详细错误: {traceback.format_exc()}")
            return web.json_response({
                "success": False,
                "error": f"搜索向量失败: {str(e)}"
            }, status=500)
    
    async def get_stats(self, request):
        """
        获取向量库统计信息
        GET /api/vectors/stats
        """
        try:
            stats = self.vector_store.get_collection_stats()
            
            return web.json_response({
                "success": True,
                "data": stats
            })
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取统计信息失败: {str(e)}")
            return web.json_response({
                "success": False,
                "error": f"获取统计信息失败: {str(e)}"
            }, status=500)

def setup_vector_routes(app, config):
    """设置向量管理路由"""
    vector_api = VectorManagementAPI(config)
    
    # 添加路由
    app.router.add_post('/api/vectors/add', vector_api.add_vector)
    app.router.add_delete('/api/vectors/delete', vector_api.delete_vector)
    app.router.add_put('/api/vectors/update', vector_api.update_vector)
    app.router.add_post('/api/vectors/search', vector_api.search_vectors)
    app.router.add_get('/api/vectors/stats', vector_api.get_stats)
    
    logger.bind(tag=TAG).info("向量管理API路由已设置")
