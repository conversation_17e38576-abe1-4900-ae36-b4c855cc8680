# MCP工具检索系统环境配置示例
# 请复制此文件为 .env 并填入实际的API密钥

# OpenAI API配置
LLM_API_KEY=sk-VZh4Djx1C6lFg8QsAtn75CgMsWZOmEllIjSh4KSDR7K1mdkb
OPENAI_API_KEY=https://yunwu.ai/v1

# 阿里云DashScope API配置 (用于阿里云embedding)
DASHSCOPE_API_KEY=sk-fcac337c29fe4d6f93bb9ff2ca2395d8

# 火山引擎API配置 (用于豆包embedding)
VOLCENGINE_API_KEY=935e14d7-245c-4adc-8cca-179ae0947829

# 可选配置
# 日志级别
LOG_LEVEL=INFO

# 缓存目录配置
VECTOR_CACHE_DIR=.vector_cache

# API服务器配置
API_HOST=0.0.0.0
API_PORT=8000

# 注意事项：
# 1. 请勿将包含真实API密钥的.env文件提交到版本控制系统
# 2. 确保.env文件在.gitignore中被忽略
# 3. 不同的embedding类型需要不同的API密钥