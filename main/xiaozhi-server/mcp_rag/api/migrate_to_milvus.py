"""数据迁移主程序，实现从现有缓存到Milvus的数据迁移。"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.performance_optimizer import PerformanceOptimizedMigrator
from src.core.providers.memory.milvus_vector_store import MilvusVectorStore
from src.core.api_client import ProjectToolsAPIClient


async def main():
    """主迁移程序。"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('migration.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    print("🚀 开始向量数据迁移到Milvus...")
    print("=" * 60)
    
    # 进度回调函数
    def progress_callback(progress_info):
        stage = progress_info.get("stage")
        
        if stage == "started":
            print(f"📊 总工具数: {progress_info['total']}")
            
        elif stage == "processing":
            batch = progress_info.get("batch", 0)
            total_batches = progress_info.get("total_batches", 0)
            processed = progress_info.get("processed", 0)
            total = progress_info.get("total", 0)
            embedding_time = progress_info.get("embedding_time", 0)
            insert_time = progress_info.get("insert_time", 0)
            
            percentage = (processed / total) * 100 if total > 0 else 0
            print(f"⏳ 批次 {batch}/{total_batches} | "
                  f"进度: {processed}/{total} ({percentage:.1f}%) | "
                  f"Embedding: {embedding_time:.2f}s | "
                  f"插入: {insert_time:.2f}s")
    
    try:
        # 创建迁移器
        migrator = PerformanceOptimizedMigrator(
            milvus_host="**********",
            milvus_port=19530,
            batch_size=70,  # 优化的批次大小
            max_concurrent_embeddings=10,  # 并发embedding数
            max_concurrent_inserts=3       # 并发插入数
        )
        
        # 执行迁移
        result = await migrator.migrate_data(progress_callback=progress_callback)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("✅ 迁移完成!")
        print("=" * 60)
        print(f"📈 总工具数: {result['total_tools']}")
        print(f"✅ 成功处理: {result['processed_tools']}")
        print(f"❌ 失败数量: {result['failed_tools']}")
        print(f"📊 成功率: {result['success_rate']:.1f}%")
        print(f"⏱️  总耗时: {result['total_time']:.2f}s")
        print(f"🚄 处理速度: {result['tools_per_second']:.2f} tools/s")
        print(f"🧠 平均Embedding时间: {result['avg_embedding_time']:.3f}s")
        print(f"💾 平均插入时间: {result['avg_insert_time']:.3f}s")
        
        # 性能分析
        analysis = migrator.get_performance_analysis()
        if "optimization_suggestions" in analysis and analysis["optimization_suggestions"]:
            print("\n💡 性能优化建议:")
            for suggestion in analysis["optimization_suggestions"]:
                print(f"   • {suggestion}")
        
        # 验证迁移结果
        print("\n🔍 验证迁移结果...")
        await verify_migration_result()
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        print(f"❌ 迁移失败: {e}")
        return 1
        
    return 0


async def verify_migration_result():
    """验证迁移结果。"""
    try:
        # 连接到Milvus
        milvus_store = MilvusVectorStore(
            host="localhost",
            port=19530,
            collection_name="tool_vectors"
        )
        await milvus_store.connect()
        
        # 获取统计信息
        stats = await milvus_store.get_collection_stats()
        print(f"📊 Milvus中的工具数量: {stats.get('total_tools', 0)}")
        
        # 测试搜索功能
        from src.embeddings.factory import EmbeddingsFactory
        config = {
            'model': 'text-embedding-v4',
            'dimensions': 1536,
            'instruct': '给定一个网络搜索查询，检索回答查询的相关段落',
            'max_batch_size': 10,
            'timeout': 30
        }
        api_key = "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
        embeddings_model = EmbeddingsFactory.create_embeddings("aliyun", config, api_key)
        
        # 测试查询
        test_query = "播放音乐"
        print(f"🔎 测试查询: '{test_query}'")
        
        query_vector = await embeddings_model.aembed_query(test_query)
        results = await milvus_store.search_similar_tools(query_vector, top_k=3)
        
        print(f"🎯 找到 {len(results)} 个相关工具:")
        for i, result in enumerate(results, 1):
            name = result.get("name", "未知")
            similarity = result.get("similarity", 0)
            print(f"   {i}. {name} (相似度: {similarity:.3f})")
            
        await milvus_store.disconnect()
        print("✅ 验证完成!")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")


async def benchmark_performance():
    """性能基准测试。"""
    print("\n🏁 开始性能基准测试...")
    
    from src.core.performance_optimizer import PerformanceBenchmark
    
    results = await PerformanceBenchmark.run_insertion_benchmark(
        tools_count=100,
        batch_sizes=[10, 50, 100],
        concurrent_levels=[5, 10, 15]
    )
    
    print("📊 基准测试结果:")
    for test_name, result in results.items():
        if "error" not in result:
            print(f"   {test_name}: {result['tools_per_second']:.2f} tools/s")
        else:
            print(f"   {test_name}: 错误 - {result['error']}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="向量数据迁移到Milvus")
    parser.add_argument("--benchmark", action="store_true", help="运行性能基准测试")
    parser.add_argument("--verify-only", action="store_true", help="仅验证现有数据")
    
    args = parser.parse_args()
    
    if args.benchmark:
        asyncio.run(benchmark_performance())
    elif args.verify_only:
        asyncio.run(verify_migration_result())
    else:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)

