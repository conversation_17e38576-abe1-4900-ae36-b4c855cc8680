#!/usr/bin/env python3
"""清除Milvus数据库数据的脚本。"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ..src.core.providers.memory.milvus_vector_store import MilvusVectorStore


def setup_logging():
    """设置日志配置。"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


async def clear_milvus_data():
    """清除Milvus数据库中的所有数据。"""
    print("🗑️  Milvus数据清除工具")
    print("=" * 50)
    
    # 确认操作
    print("⚠️  警告: 此操作将删除Milvus数据库中的所有向量数据!")
    print("⚠️  此操作不可恢复!")
    print()

    confirm = input("请输入 'yes' 或 'y' 确认删除所有数据: ").strip().lower()
    if confirm not in ['yes', 'y']:
        print("❌ 操作已取消")
        return False
    
    print()
    print("🔗 连接到Milvus数据库...")
    
    try:
        # 创建Milvus存储实例
        store = MilvusVectorStore(
            host="localhost",
            port=19530,
            collection_name="tool_vectors",
            dimension=1536
        )
        
        # 连接到数据库
        await store.connect()
        print("✅ 成功连接到Milvus数据库")
        
        # 获取清除前的统计信息
        print("\n📊 获取数据统计信息...")
        stats = await store.get_collection_stats()
        total_count = stats.get("total_tools", 0)
        collection_name = stats.get("collection_name", "unknown")
        
        print(f"📦 集合名称: {collection_name}")
        print(f"📈 当前数据量: {total_count} 条记录")
        
        if total_count == 0:
            print("ℹ️  数据库中没有数据需要清除")
            await store.disconnect()
            return True
        
        # 最后确认
        print(f"\n⚠️  即将删除 {total_count} 条记录，确定继续吗?")
        final_confirm = input("请输入 'delete' 或 'd' 确认: ").strip().lower()
        if final_confirm not in ['delete', 'd']:
            print("❌ 操作已取消")
            await store.disconnect()
            return False
        
        # 执行清除操作
        print("\n🗑️  正在清除数据...")
        success = await store.clear_all_data()
        
        if success:
            print("✅ 数据清除成功!")
            
            # 验证清除结果
            print("\n🔍 验证清除结果...")
            new_stats = await store.get_collection_stats()
            new_count = new_stats.get("total_tools", 0)
            print(f"📈 清除后数据量: {new_count} 条记录")
            
            if new_count == 0:
                print("✅ 所有数据已成功清除")
            else:
                print(f"⚠️  仍有 {new_count} 条记录未清除")
        else:
            print("❌ 数据清除失败")
            return False
        
        # 断开连接
        await store.disconnect()
        print("🔌 已断开数据库连接")
        
        return success
        
    except Exception as e:
        print(f"❌ 清除数据时发生错误: {e}")
        return False


async def main():
    """主函数。"""
    setup_logging()
    
    try:
        success = await clear_milvus_data()
        if success:
            print("\n🎉 数据清除操作完成!")
            print("💡 现在您可以重新导入数据了")
        else:
            print("\n💥 数据清除操作失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 发生未知错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
