#!/usr/bin/env python3
"""简单的Milvus数据清除脚本，直接使用pymilvus库。"""

import sys
from pymilvus import connections, utility, Collection


def clear_milvus_collections():
    """清除Milvus中的所有集合。"""
    print("🗑️  Milvus数据清除工具 (简化版)")
    print("=" * 50)
    
    # 确认操作
    print("⚠️  警告: 此操作将删除Milvus数据库中的所有集合和数据!")
    print("⚠️  此操作不可恢复!")
    print()
    
    confirm = input("请输入 'YES' 确认删除所有数据: ")
    if confirm != "YES":
        print("❌ 操作已取消")
        return False
    
    try:
        print("\n🔗 连接到Milvus数据库...")
        
        # 连接到Milvus
        connections.connect(
            alias="default",
            host="localhost",
            port=19530
        )
        print("✅ 成功连接到Milvus数据库")
        
        # 获取所有集合
        print("\n📋 获取所有集合列表...")
        collections = utility.list_collections()
        
        if not collections:
            print("ℹ️  数据库中没有集合")
            return True
        
        print(f"📦 找到 {len(collections)} 个集合:")
        for i, collection_name in enumerate(collections, 1):
            try:
                collection = Collection(collection_name)
                count = collection.num_entities
                print(f"  {i}. {collection_name} ({count} 条记录)")
            except Exception as e:
                print(f"  {i}. {collection_name} (无法获取记录数: {e})")
        
        # 最后确认
        print(f"\n⚠️  即将删除所有 {len(collections)} 个集合，确定继续吗?")
        final_confirm = input("请输入 'DELETE' 确认: ")
        if final_confirm != "DELETE":
            print("❌ 操作已取消")
            return False
        
        # 删除所有集合
        print("\n🗑️  正在删除集合...")
        deleted_count = 0
        
        for collection_name in collections:
            try:
                print(f"  删除集合: {collection_name}")
                utility.drop_collection(collection_name)
                deleted_count += 1
                print(f"  ✅ 已删除: {collection_name}")
            except Exception as e:
                print(f"  ❌ 删除失败 {collection_name}: {e}")
        
        print(f"\n✅ 成功删除 {deleted_count}/{len(collections)} 个集合")
        
        # 验证删除结果
        print("\n🔍 验证删除结果...")
        remaining_collections = utility.list_collections()
        if not remaining_collections:
            print("✅ 所有集合已成功删除")
        else:
            print(f"⚠️  仍有 {len(remaining_collections)} 个集合未删除:")
            for collection_name in remaining_collections:
                print(f"  - {collection_name}")
        
        return deleted_count > 0
        
    except Exception as e:
        print(f"❌ 清除数据时发生错误: {e}")
        return False
    finally:
        try:
            connections.disconnect("default")
            print("🔌 已断开数据库连接")
        except:
            pass


def clear_specific_collection(collection_name: str):
    """清除指定集合的数据。"""
    print(f"🗑️  清除集合: {collection_name}")
    print("=" * 50)
    
    try:
        print("🔗 连接到Milvus数据库...")
        
        # 连接到Milvus
        connections.connect(
            alias="default",
            host="localhost",
            port=19530
        )
        print("✅ 成功连接到Milvus数据库")
        
        # 检查集合是否存在
        if not utility.has_collection(collection_name):
            print(f"ℹ️  集合 '{collection_name}' 不存在")
            return True
        
        # 获取集合信息
        collection = Collection(collection_name)
        count = collection.num_entities
        print(f"📦 集合 '{collection_name}' 包含 {count} 条记录")
        
        if count == 0:
            print("ℹ️  集合中没有数据需要清除")
            return True
        
        # 确认删除
        confirm = input(f"确定要删除集合 '{collection_name}' 中的所有 {count} 条记录吗? (y/N): ")
        if confirm.lower() not in ['y', 'yes']:
            print("❌ 操作已取消")
            return False
        
        # 删除集合
        print(f"🗑️  正在删除集合 '{collection_name}'...")
        utility.drop_collection(collection_name)
        print(f"✅ 集合 '{collection_name}' 已成功删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 清除集合时发生错误: {e}")
        return False
    finally:
        try:
            connections.disconnect("default")
            print("🔌 已断开数据库连接")
        except:
            pass


def main():
    """主函数。"""
    if len(sys.argv) > 1:
        # 清除指定集合
        collection_name = sys.argv[1]
        success = clear_specific_collection(collection_name)
    else:
        # 清除所有集合
        success = clear_milvus_collections()
    
    if success:
        print("\n🎉 数据清除操作完成!")
        print("💡 现在您可以重新导入数据了")
    else:
        print("\n💥 数据清除操作失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
