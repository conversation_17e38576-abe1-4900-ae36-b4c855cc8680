#!/usr/bin/env python3
"""快速清除Milvus数据的脚本。"""

from pymilvus import connections, utility, Collection


def quick_clear():
    """快速清除Milvus数据。"""
    print("🗑️  快速清除Milvus数据")
    print("=" * 30)
    
    try:
        # 连接到Milvus
        print("🔗 连接Milvus...")
        connections.connect(
            alias="default",
            host="localhost",
            port=19530
        )
        print("✅ 连接成功")
        
        # 获取所有集合
        collections = utility.list_collections()
        print(f"📦 找到 {len(collections)} 个集合")
        
        if not collections:
            print("ℹ️  没有数据需要清除")
            return
        
        # 显示集合信息
        for collection_name in collections:
            try:
                collection = Collection(collection_name)
                count = collection.num_entities
                print(f"  - {collection_name}: {count} 条记录")
            except:
                print(f"  - {collection_name}: 无法获取记录数")
        
        # 确认删除
        confirm = input("\n确认删除所有数据? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 已取消")
            return
        
        # 删除所有集合
        print("\n🗑️  删除中...")
        for collection_name in collections:
            try:
                utility.drop_collection(collection_name)
                print(f"✅ 已删除: {collection_name}")
            except Exception as e:
                print(f"❌ 删除失败 {collection_name}: {e}")
        
        print("\n🎉 清除完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        try:
            connections.disconnect("default")
        except:
            pass


if __name__ == "__main__":
    quick_clear()
