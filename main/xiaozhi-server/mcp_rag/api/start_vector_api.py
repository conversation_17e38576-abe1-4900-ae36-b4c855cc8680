#!/usr/bin/env python3
"""向量管理API服务启动脚本。"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # mcp_rag目录
sys.path.insert(0, str(project_root))

import uvicorn
from fastapi import FastAPI
from src.core.api.vector_management import router


def create_app() -> FastAPI:
    """创建FastAPI应用。"""
    app = FastAPI(
        title="工具向量管理API",
        description="基于Milvus的工具向量存储和检索API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 注册路由
    app.include_router(router)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    return app


def main():
    """主函数。"""
    print("🚀 启动向量管理API服务...")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 ReDoc文档: http://localhost:8000/redoc")
    print("=" * 50)
    
    # 启动服务 - 使用import string支持reload
    uvicorn.run(
        "start_vector_api:create_app",
        factory=True,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()