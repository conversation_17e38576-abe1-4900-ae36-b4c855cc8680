# 用户权限管理API使用指南

## 概述

本文档介绍了增强后的Milvus向量管理API的用户权限功能。API现在支持基于用户ID的权限控制，实现了管理端数据和用户私有数据的分离。

## 核心功能

### 1. 用户权限模型

- **管理端数据**: `user_id` 为空或 `"admin"`，所有用户可见
- **用户私有数据**: `user_id` 为具体用户ID，只有该用户和管理员可见
- **权限控制**: 用户只能修改和删除自己的数据，管理员可以操作所有数据

### 2. 搜索权限逻辑

- **未传递user_id**: 只返回管理端公共数据
- **传递user_id**: 返回管理端数据 + 该用户的私有数据

## API接口说明

### 添加工具向量

```bash
POST /api/vector/add
```

**请求体示例**:
```json
{
  "name": "test_tool",
  "description": "Test tool description",
  "descriptionChinese": "测试工具描述",
  "user_id": "user123"  // 可选，为空表示管理端操作
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "工具向量添加成功",
  "data": {
    "record_id": "uuid",
    "tool_id": 1,
    "user_id": "user123"
  }
}
```

### 批量添加工具向量

```bash
POST /api/vector/batch_add
```

**请求体示例**:
```json
{
  "tools": [
    {
      "name": "tool1",
      "description": "Tool 1 description",
      "descriptionChinese": "工具1描述"
    },
    {
      "name": "tool2",
      "description": "Tool 2 description", 
      "descriptionChinese": "工具2描述"
    }
  ],
  "user_id": "user123"  // 可选，为空表示管理端操作
}
```

### 更新工具向量

```bash
PUT /api/vector/update/{tool_id}
```

**请求体示例**:
```json
{
  "description": "Updated description",
  "descriptionChinese": "更新后的描述",
  "user_id": "user123"  // 必须，用于权限验证
}
```

**权限验证**:
- 用户只能更新自己的工具
- 管理员可以更新所有工具
- 无权限时返回403错误

### 删除工具向量

```bash
DELETE /api/vector/delete/{tool_id}?user_id=user123
```

**权限验证**:
- 用户只能删除自己的工具
- 管理员可以删除所有工具
- 无权限时返回403错误

### 获取工具信息

```bash
GET /api/vector/get/{tool_id}?user_id=user123
```

**权限验证**:
- 用户只能查看自己的工具和管理端公共工具
- 管理员可以查看所有工具

### 搜索相似工具

```bash
POST /api/vector/search
```

**请求体示例**:
```json
{
  "query": "搜索关键词",
  "top_k": 10,
  "user_id": "user123"  // 可选，用于权限过滤
}
```

**搜索逻辑**:
- 未传递user_id: 只搜索管理端数据
- 传递user_id: 搜索管理端数据 + 用户私有数据

### 获取统计信息

```bash
GET /api/vector/stats?user_id=user123
```

**全局统计** (不传user_id):
```json
{
  "code": 0,
  "msg": "获取统计信息成功",
  "data": {
    "total_tools": 100,
    "collection_name": "tool_vectors",
    "dimension": 1536
  }
}
```

**用户统计** (传递user_id):
```json
{
  "code": 0,
  "msg": "获取统计信息成功",
  "data": {
    "total_tools": 100,
    "admin_tools": 80,
    "user_tools": 5,
    "accessible_tools": 85,
    "user_id": "user123",
    "collection_name": "tool_vectors",
    "dimension": 1536
  }
}
```

## 数据结构增强

### 工具数据验证

API现在会自动处理空值和默认值：

```json
{
  "ID": 0,                    // 默认为0
  "name": "",                 // 必填，不能为空
  "c_name": "",              // 默认为空字符串
  "description": "",          // 默认为空字符串
  "descriptionChinese": "",   // 默认为空字符串
  "fullName": "",            // 默认为空字符串
  "projectUUId": "",         // 默认为空字符串
  "projectId": 0,            // 默认为0
  "points": 0,               // 默认为0
  "is_single_call": 0,       // 默认为0
  "inputSchema": {},         // 默认为空对象
  "outputSchema": {},        // 默认为空对象
  "regex": ""                // 默认为空字符串
}
```

## 错误处理

### 权限错误
- **403**: 无权限操作（用户尝试操作其他用户的数据）
- **404**: 工具不存在

### 验证错误
- **400**: 必填字段缺失（如工具名称为空）
- **500**: 服务器内部错误

## 使用建议

1. **管理端操作**: 不传递user_id或传递null
2. **用户操作**: 始终传递具体的user_id
3. **搜索权限**: 根据需要传递user_id来控制搜索范围
4. **批量操作**: 所有工具将使用相同的user_id
5. **错误处理**: 检查返回的code字段判断操作结果

## 测试脚本

可以使用项目中的`test_user_permissions.py`脚本来测试用户权限功能。

```bash
python test_user_permissions.py
```

该脚本会测试：
- 管理端和用户数据的创建
- 不同用户的搜索权限
- 权限验证功能
- 统计信息查询
- 批量操作权限