"""输入管理器，实现防抖动输入处理和检索任务管理。"""

import asyncio
import logging
import time
from typing import Optional, Callable, Any, List
from dataclasses import dataclass
from enum import Enum
from collections import deque


class InputState(Enum):
    """输入状态枚举。"""
    IDLE = "idle"
    TYPING = "typing"
    PROCESSING = "processing"
    COMPLETED = "completed"


@dataclass
class InputEvent:
    """输入事件数据结构。"""
    text: str
    timestamp: float
    is_complete: bool = False
    session_id: Optional[str] = None
    similarity_score: float = 0.0


class InputManager:
    """输入管理器，处理用户输入的防抖动和任务管理。"""
    
    def __init__(self, debounce_delay: float = 1.0, min_input_length: int = 1, 
                 context_window_size: int = 10, similarity_threshold: float = 0.7):
        """初始化输入管理器。
        
        Args:
            debounce_delay: 防抖动延迟时间（秒）
            min_input_length: 最小输入长度
            context_window_size: 上下文窗口大小
            similarity_threshold: 语义相似度阈值
        """
        self.debounce_delay = debounce_delay
        self.min_input_length = min_input_length
        self.context_window_size = context_window_size
        self.similarity_threshold = similarity_threshold
        
        # 状态管理
        self.state = InputState.IDLE
        self.input_buffer = ""
        self.last_input_time = 0.0
        
        # 上下文管理
        self.context_window: deque = deque(maxlen=context_window_size)
        self.current_session_id: Optional[str] = None
        self.pending_context = ""
        
        # 任务管理
        self.current_task: Optional[asyncio.Task] = None
        self.processing_task: Optional[asyncio.Task] = None
        
        # 回调函数
        self.process_callback: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_inputs': 0,
            'debounced_inputs': 0,
            'processed_inputs': 0,
            'cancelled_tasks': 0,
            'context_merges': 0,
            'similarity_matches': 0
        }
        
        logging.info(f"📝 InputManager initialized with debounce_delay={debounce_delay}s, context_window={context_window_size}")
    
    async def handle_input(self, text: str, callback: Callable, is_complete: bool = False, session_id: Optional[str] = None) -> None:
        """处理用户输入。
        
        Args:
            text: 输入文本
            callback: 处理回调函数
            is_complete: 是否为完整输入
            session_id: 会话ID，用于上下文管理
        """
        current_time = asyncio.get_event_loop().time()
        self.stats['total_inputs'] += 1
        
        # 更新输入缓冲区
        self.input_buffer = text.strip()
        self.last_input_time = current_time
        self.process_callback = callback
        
        # 如果输入为空或太短，不处理
        if not self.input_buffer or len(self.input_buffer) < self.min_input_length:
            return
        
        # 检查是否需要上下文合并
        should_merge_context = await self._should_merge_with_context(text, session_id)
        
        if should_merge_context:
            # 合并上下文
            merged_text = self._merge_with_context(text)
            self.input_buffer = merged_text
            self.stats['context_merges'] += 1
            logging.info(f"🔗 Context merged: {text[:30]}... + previous context")
        
        # 如果是完整输入，立即处理
        if is_complete:
            await self._process_immediately()
            return
        
        # 更新状态
        if self.state == InputState.IDLE:
            self.state = InputState.TYPING
        
        # 取消之前的防抖动任务
        if self.current_task and not self.current_task.done():
            self.current_task.cancel()
            self.stats['cancelled_tasks'] += 1
            logging.debug("⏹️ Cancelled previous debounce task")
        
        # 创建新的防抖动任务
        self.current_task = asyncio.create_task(
            self._debounced_process(self.input_buffer, current_time)
        )
        
        logging.debug(f"⏱️ Started debounce task for input: {self.input_buffer[:30]}...")
    
    async def _debounced_process(self, original_text: str, start_time: float) -> None:
        """防抖动处理。"""
        try:
            # 等待防抖动延迟
            await asyncio.sleep(self.debounce_delay)
            
            # 检查是否有更新的输入
            if self.last_input_time > start_time:
                logging.debug("🔄 Input updated during debounce, skipping")
                return
            
            # 检查输入是否仍然有效
            if not self.input_buffer or self.input_buffer != original_text:
                logging.debug("🔄 Input changed during debounce, skipping")
                return
            
            # 执行处理
            await self._process_immediately()
            
        except asyncio.CancelledError:
            logging.debug("⏹️ Debounce task was cancelled")
        except Exception as e:
            logging.error(f"❌ Error in debounced process: {e}")
            self.state = InputState.IDLE
    
    async def _process_immediately(self) -> None:
        """立即处理输入。"""
        if not self.input_buffer or not self.process_callback:
            return
        
        # 检查是否已经在处理中
        if self.processing_task and not self.processing_task.done():
            logging.debug("🔄 Already processing, cancelling previous task")
            self.processing_task.cancel()
            self.stats['cancelled_tasks'] += 1
        
        # 更新状态
        self.state = InputState.PROCESSING
        input_text = self.input_buffer
        
        try:
            logging.info(f"🚀 Processing input: {input_text[:50]}...")
            
            # 添加到上下文窗口
            self._add_to_context_window(input_text)
            
            # 创建处理任务
            self.processing_task = asyncio.create_task(
                self._safe_process(input_text)
            )
            
            # 等待处理完成
            await self.processing_task
            
            # 更新统计
            self.stats['processed_inputs'] += 1
            self.state = InputState.COMPLETED
            
            logging.info(f"✅ Input processing completed")
            
        except asyncio.CancelledError:
            logging.debug("⏹️ Processing task was cancelled")
            self.state = InputState.IDLE
        except Exception as e:
            logging.error(f"❌ Error processing input: {e}")
            self.state = InputState.IDLE
        finally:
            # 清理状态
            await asyncio.sleep(0.1)  # 短暂延迟
            if self.state == InputState.COMPLETED:
                self.state = InputState.IDLE
                self.pending_context = ""  # 清除待处理上下文
    
    async def _safe_process(self, text: str) -> None:
        """安全地执行处理回调。"""
        try:
            if asyncio.iscoroutinefunction(self.process_callback):
                await self.process_callback(text)
            else:
                self.process_callback(text)
        except Exception as e:
            logging.error(f"❌ Error in process callback: {e}")
            raise
    
    def cancel_all_tasks(self) -> None:
        """取消所有正在进行的任务。"""
        cancelled_count = 0
        
        if self.current_task and not self.current_task.done():
            self.current_task.cancel()
            cancelled_count += 1
        
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            cancelled_count += 1
        
        if cancelled_count > 0:
            self.stats['cancelled_tasks'] += cancelled_count
            logging.info(f"⏹️ Cancelled {cancelled_count} tasks")
        
        self.state = InputState.IDLE
    
    def is_busy(self) -> bool:
        """检查是否正在处理输入。"""
        return self.state in [InputState.TYPING, InputState.PROCESSING]
    
    def get_stats(self) -> dict:
        """获取统计信息。"""
        return {
            **self.stats,
            'current_state': self.state.value,
            'debounce_rate': (
                self.stats['debounced_inputs'] / max(self.stats['total_inputs'], 1) * 100
            ),
            'processing_rate': (
                self.stats['processed_inputs'] / max(self.stats['total_inputs'], 1) * 100
            )
        }
    
    def set_debounce_delay(self, delay: float) -> None:
        """设置防抖动延迟时间。"""
        self.debounce_delay = max(0.1, delay)
        logging.info(f"📝 Debounce delay updated to {self.debounce_delay}s")
    
    def reset_stats(self) -> None:
        """重置统计信息。"""
        self.stats = {
            'total_inputs': 0,
            'debounced_inputs': 0,
            'processed_inputs': 0,
            'cancelled_tasks': 0,
            'context_merges': 0,
            'similarity_matches': 0
        }
        logging.info("📊 Input manager stats reset")
    
    async def _should_merge_with_context(self, text: str, session_id: Optional[str] = None) -> bool:
        """判断是否应该与上下文合并。"""
        # 如果没有历史上下文，不合并
        if not self.context_window:
            return False
        
        # 如果会话ID不同，不合并
        if session_id and self.current_session_id != session_id:
            self.current_session_id = session_id
            return False
        
        # 检查时间间隔（如果距离上次输入太久，不合并）
        time_since_last = time.time() - self.last_input_time
        if time_since_last > 30:  # 30秒超时
            return False
        
        # 简单的语义相似度检查（基于关键词重叠）
        recent_context = " ".join([event.text for event in list(self.context_window)[-3:]])
        similarity = self._calculate_simple_similarity(text, recent_context)
        
        if similarity > self.similarity_threshold:
            self.stats['similarity_matches'] += 1
            return True
        
        return False
    
    def _calculate_simple_similarity(self, text1: str, text2: str) -> float:
        """计算简单的文本相似度（基于词汇重叠）。"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的词汇重叠计算
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _merge_with_context(self, text: str) -> str:
        """与上下文合并。"""
        if not self.context_window:
            return text
        
        # 获取最近的上下文
        recent_context = " ".join([event.text for event in list(self.context_window)[-2:]])
        
        # 合并上下文
        merged_text = f"{recent_context} {text}".strip()
        
        return merged_text
    
    def _add_to_context_window(self, text: str) -> None:
        """添加到上下文窗口。"""
        event = InputEvent(
            text=text,
            timestamp=time.time(),
            session_id=self.current_session_id
        )
        self.context_window.append(event)
        
        logging.debug(f"📝 Added to context window: {text[:30]}...")
    
    def clear_context(self) -> None:
        """清除上下文窗口。"""
        self.context_window.clear()
        self.current_session_id = None
        self.pending_context = ""
        logging.info("🧹 Context window cleared")
    
    def get_context_summary(self) -> dict:
        """获取上下文摘要。"""
        return {
            'context_size': len(self.context_window),
            'current_session_id': self.current_session_id,
            'oldest_context': self.context_window[0].text[:50] + "..." if self.context_window else None,
            'newest_context': self.context_window[-1].text[:50] + "..." if self.context_window else None,
            'similarity_threshold': self.similarity_threshold
        }