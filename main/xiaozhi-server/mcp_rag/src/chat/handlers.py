"""聊天会话的命令处理器。"""

import asyncio
import logging
from typing import Optional

try:
    from ..common.similarity_config import SimilarityConfig
    SIMILARITY_CONFIG_AVAILABLE = True
except ImportError:
    SIMILARITY_CONFIG_AVAILABLE = False


class CommandHandler:
    """处理聊天会话中的各种命令。"""

    def __init__(self, chat_session) -> None:
        """使用聊天会话引用初始化。

        参数:
            chat_session: 聊天会话的引用。
        """
        self.chat_session = chat_session

    def handle_weights_command(self) -> None:
        """处理权重调整命令。"""
        if not SIMILARITY_CONFIG_AVAILABLE:
            print("❌ 相似度配置不可用")
            return

        print("\n⚖️  相似度权重配置")
        print("=" * 40)

        # 显示当前权重
        weights = SimilarityConfig.get_weights()
        print(f"当前权重:")
        print(f"  语义:  {weights['semantic']:.2f}")
        print(f"  关键词:  {weights['keyword']:.2f}")
        print(f"  名称:      {weights['name_match']:.2f}")

        # 显示可用预设
        print(f"\n可用预设:")
        presets = SimilarityConfig.get_presets()
        for name, preset in presets.items():
            print(f"  {name}: {preset['description']}")

        print(f"\n选项:")
        print(f"  1. 输入预设名称 (例如, 'semantic_focused')")
        print(f"  2. 输入 'custom' 设置自定义权重")
        print(f"  3. 按回车键保持当前权重")

        choice = input("\nYour choice: ").strip().lower()

        if not choice:
            print("Keeping current weights.")
            return

        if choice == "custom":
            try:
                print("\nEnter weights (must sum to 1.0):")
                semantic = float(input("Semantic weight (0.0-1.0): "))
                keyword = float(input("Keyword weight (0.0-1.0): "))
                name_match = float(input("Name match weight (0.0-1.0): "))

                SimilarityConfig.set_weights(semantic, keyword, name_match)
                print(f"✅ Custom weights applied successfully!")

            except (ValueError, Exception) as e:
                print(f"❌ Error setting custom weights: {e}")

        elif choice in presets:
            description = SimilarityConfig.apply_preset(choice)
            print(f"✅ Applied preset '{choice}': {description}")

        else:
            print(f"❌ Unknown option: {choice}")

        # Show updated weights
        weights = SimilarityConfig.get_weights()
        print(f"\nUpdated weights:")
        print(f"  Semantic:  {weights['semantic']:.2f}")
        print(f"  Keywords:  {weights['keyword']:.2f}")
        print(f"  Name:      {weights['name_match']:.2f}")

    def handle_multi_tool_command(self) -> None:
        """Handle the multi-tool query command."""
        if not hasattr(self.chat_session, 'multi_tool_mode') or not self.chat_session.multi_tool_mode:
            print("❌ Multi-tool query functionality not available.")
            return

        print("\n🔧 Multi-tool Query Management")
        print("=" * 40)

        print("Options:")
        print("  1. Test multi-tool query")
        print("  2. Toggle multi-tool mode")

        choice = input("\nChoose action (1-2): ").strip()

        if choice == "1":
            asyncio.create_task(self._test_multi_tool_query())
        elif choice == "2":
            self._toggle_multi_tool_mode()
        else:
            print("❌ Invalid choice")

    async def _test_multi_tool_query(self) -> None:
        """Test multi-tool query functionality."""
        print("\n🧪 Test Multi-tool Query")
        print("=" * 30)

        if not (hasattr(self.chat_session, 'multi_tool_retriever') and 
                self.chat_session.multi_tool_retriever):
            print("❌ Multi-tool retriever not initialized")
            return

        test_query = input("Enter test query: ").strip()
        if not test_query:
            print("❌ Query cannot be empty")
            return

        try:
            print(f"\n🔍 Testing query: '{test_query}'")
            start_time = asyncio.get_event_loop().time()

            results = await self.chat_session.multi_tool_retriever.retrieve_multi_tools(
                query=test_query,
                max_tools_per_task=self.chat_session.max_tools_per_task,
                total_max_tools=10
            )

            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time

            print(f"\n✅ Test completed (Time: {processing_time:.2f}s)")
            print(f"📊 Results count: {len(results.tool_matches)}")

            if results.tool_matches:
                print(f"\n🎯 Retrieved tools:")
                for i, tool_match in enumerate(results.tool_matches, 1):
                    print(f"\n{i}. {tool_match.tool_name}")
                    print(f"   Description: {tool_match.description}")
                    print(f"   Confidence: {tool_match.confidence:.3f}")
                    print(f"   Subtask ID: {tool_match.subtask_id}")
            else:
                print("❌ No relevant tools found")

        except Exception as e:
            print(f"❌ Test failed: {e}")

    def _toggle_multi_tool_mode(self) -> None:
        """Toggle multi-tool mode."""
        if not (hasattr(self.chat_session, 'multi_tool_retriever') and 
                self.chat_session.multi_tool_retriever):
            print("❌ Multi-tool retriever not initialized")
            return

        # Simple toggle
        self.chat_session.multi_tool_mode = not self.chat_session.multi_tool_mode
        status = "enabled" if self.chat_session.multi_tool_mode else "disabled"
        print(f"✅ Multi-tool mode {status}")

    def handle_cache_command(self, action: str = "stats") -> None:
        """Handle cache-related commands.

        Args:
            action: The cache action to perform (stats, clear, save).
        """
        if not hasattr(self.chat_session, 'tool_vector_manager'):
            print("❌ Tool vector manager not available")
            return

        if action == "clear":
            self.chat_session.tool_vector_manager.clear_cache()
            print("✅ Vector cache cleared!")
        elif action == "save":
            self.chat_session.tool_vector_manager.save_cache()
            print("✅ Vector cache saved!")
        elif action == "stats":
            stats = self.chat_session.tool_vector_manager.get_cache_stats()
            print("\n📊 Cache Statistics:")
            print(f"  Total vectors: {stats.get('total_vectors', 0)}")
            print(f"  Cache size: {stats.get('cache_size_mb', 0):.2f} MB")
            print(f"  Last updated: {stats.get('last_updated', 'Unknown')}")
            print(f"  Cache version: {stats.get('cache_version', 'Unknown')}")
        else:
            print(f"❌ Unknown cache action: {action}")

    def get_available_commands(self) -> list[str]:
        """Get list of available commands.

        Returns:
            List of available command names.
        """
        commands = ["'quit'/'exit'", "'clear_cache'", "'cache_stats'"]
        
        if SIMILARITY_CONFIG_AVAILABLE:
            commands.append("'weights'")
        
        if hasattr(self.chat_session, 'multi_tool_mode') and self.chat_session.multi_tool_mode:
            commands.append("'multi_tool'")
            
        return commands