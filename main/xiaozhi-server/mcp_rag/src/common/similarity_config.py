"""
混合相似度权重和参数的配置。
"""

class SimilarityConfig:
    """混合相似度计算的配置类。"""

    # 相似度权重（为了最佳效果，总和应为1.0）
    SEMANTIC_WEIGHT = 0.7      # 基于向量的语义相似度权重
    KEYWORD_WEIGHT = 0.15       # 关键词重叠（Jaccard相似度）权重
    NAME_MATCH_WEIGHT = 0.15   # 工具名称匹配权重

    # 关键词相似度参数
    MIN_WORD_LENGTH = 1        # 关键词匹配时考虑的最小单词长度（支持中文单字符）
    STOP_WORDS = {             # 关键词匹配时忽略的常用词（英文和中文）
        # 英文停用词
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'can', 'may', 'might', 'must', 'shall', 'this', 'that', 'these', 'those',
        # 中文停用词
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
        '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
        '自己', '这', '那', '它', '他', '她', '这个', '那个', '什么', '怎么', '为什么'
    }

    # 名称匹配参数
    EXACT_NAME_MATCH_SCORE = 1.0      # 精确名称匹配的分数
    PARTIAL_NAME_MATCH_SCORE = 0.5    # 部分名称匹配的分数

    # 性能参数
    USE_FLOAT32 = True         # 使用float32而不是float64以获得更好的性能
    NORMALIZE_VECTORS = True   # 预标准化向量以加快余弦相似度计算

    # 调试和日志
    ENABLE_DETAILED_LOGGING = False   # 启用详细的相似度计算日志
    SHOW_MATCH_BREAKDOWN = True       # 显示相似度组件的分解
    
    @classmethod
    def validate_weights(cls):
        """验证权重总和约为1.0。"""
        total = cls.SEMANTIC_WEIGHT + cls.KEYWORD_WEIGHT + cls.NAME_MATCH_WEIGHT
        if abs(total - 1.0) > 0.01:  # 允许小的浮点误差
            raise ValueError(f"相似度权重总和应为1.0，实际为{total}")
        return True

    @classmethod
    def get_weights(cls):
        """获取当前权重配置。"""
        cls.validate_weights()
        return {
            'semantic': cls.SEMANTIC_WEIGHT,
            'keyword': cls.KEYWORD_WEIGHT,
            'name_match': cls.NAME_MATCH_WEIGHT
        }

    @classmethod
    def set_weights(cls, semantic: float, keyword: float, name_match: float):
        """设置相似度计算的新权重。"""
        if abs(semantic + keyword + name_match - 1.0) > 0.01:
            raise ValueError("权重总和必须为1.0")

        cls.SEMANTIC_WEIGHT = semantic
        cls.KEYWORD_WEIGHT = keyword
        cls.NAME_MATCH_WEIGHT = name_match
    
    @classmethod
    def get_presets(cls):
        """获取不同用例的预定义权重预设。"""
        return {
            'semantic_focused': {
                'semantic': 0.8,
                'keyword': 0.15,
                'name_match': 0.05,
                'description': '优先考虑语义理解而非精确匹配'
            },
            'keyword_focused': {
                'semantic': 0.4,
                'keyword': 0.5,
                'name_match': 0.1,
                'description': '优先考虑精确关键词匹配'
            },
            'balanced': {
                'semantic': 0.6,
                'keyword': 0.3,
                'name_match': 0.1,
                'description': '平衡方法（默认）'
            },
            'name_focused': {
                'semantic': 0.5,
                'keyword': 0.2,
                'name_match': 0.3,
                'description': '给工具名称匹配更多权重'
            }
        }

    @classmethod
    def apply_preset(cls, preset_name: str):
        """应用预定义的权重预设。"""
        presets = cls.get_presets()
        if preset_name not in presets:
            available = ', '.join(presets.keys())
            raise ValueError(f"未知预设'{preset_name}'。可用预设：{available}")

        preset = presets[preset_name]
        cls.set_weights(preset['semantic'], preset['keyword'], preset['name_match'])
        return preset['description']

# 导入时验证配置
SimilarityConfig.validate_weights()
