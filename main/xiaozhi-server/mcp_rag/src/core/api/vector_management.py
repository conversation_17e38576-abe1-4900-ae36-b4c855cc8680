"""向量管理API接口，提供工具向量的增删改操作。"""

import logging
import asyncio
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.core.providers.memory.milvus_vector_store import MilvusVectorStore
from src.core.api_client import ProjectToolsAPIClient
from src.embeddings.factory import EmbeddingsFactory


# API模型定义
class ToolCreateRequest(BaseModel):
    """创建工具请求模型。"""
    ID: Optional[int] = Field(default=0, description="工具ID")
    name: str = Field(..., description="工具名称")
    c_name: Optional[str] = Field(default="", description="工具中文名称")
    description: Optional[str] = Field(default="", description="工具英文描述")
    descriptionChinese: Optional[str] = Field(default="", description="工具中文描述")
    fullName: Optional[str] = Field(default="", description="工具完整名称")
    projectUUId: Optional[str] = Field(default="", description="项目UUID")
    projectId: Optional[int] = Field(default=0, description="项目ID")
    points: Optional[int] = Field(default=0, description="工具积分")
    is_single_call: Optional[int] = Field(default=0, description="是否单次调用")
    inputSchema: Optional[Dict[str, Any]] = Field(default_factory=dict, description="输入Schema")
    outputSchema: Optional[Dict[str, Any]] = Field(default_factory=dict, description="输出Schema")
    regex: Optional[str] = Field(default="", description="正则表达式")
    user_id: Optional[str] = Field(default=None, description="用户ID，为空表示管理端操作")


class ToolUpdateRequest(BaseModel):
    """更新工具请求模型。"""
    name: Optional[str] = Field(default=None, description="工具名称")
    c_name: Optional[str] = Field(default=None, description="工具中文名称")
    description: Optional[str] = Field(default=None, description="工具英文描述")
    descriptionChinese: Optional[str] = Field(default=None, description="工具中文描述")
    fullName: Optional[str] = Field(default=None, description="工具完整名称")
    projectUUId: Optional[str] = Field(default=None, description="项目UUID")
    projectId: Optional[int] = Field(default=None, description="项目ID")
    points: Optional[int] = Field(default=None, description="工具积分")
    is_single_call: Optional[int] = Field(default=None, description="是否单次调用")
    inputSchema: Optional[Dict[str, Any]] = Field(default=None, description="输入Schema")
    outputSchema: Optional[Dict[str, Any]] = Field(default=None, description="输出Schema")
    regex: Optional[str] = Field(default=None, description="正则表达式")
    user_id: Optional[str] = Field(default=None, description="用户ID，为空表示管理端操作")


class ToolSearchRequest(BaseModel):
    """工具搜索请求模型。"""
    query: str = Field(..., description="搜索查询")
    top_k: int = Field(default=10, description="返回结果数量")
    filters: Optional[str] = Field(default=None, description="过滤条件")
    user_id: Optional[str] = Field(default=None, description="用户ID，用于权限控制")


class BatchToolCreateRequest(BaseModel):
    """批量工具创建请求模型。"""
    tools: List[ToolCreateRequest] = Field(..., description="工具列表")
    user_id: Optional[str] = Field(default=None, description="用户ID，为空表示管理端操作")


class ApiResponse(BaseModel):
    """API响应模型。"""
    code: int = Field(..., description="响应代码")
    msg: str = Field(..., description="响应消息")
    data: Any = Field(default=None, description="响应数据")


class VectorManagementAPI:
    """向量管理API类。"""
    
    def __init__(self):
        self.router = APIRouter(prefix="/api/vector", tags=["向量管理"])
        self.milvus_store: Optional[MilvusVectorStore] = None
        self.embeddings_model = None
        self.logger = logging.getLogger(__name__)
        
        # 注册路由
        self._register_routes()
        
    async def get_milvus_store(self) -> MilvusVectorStore:
        """获取Milvus存储实例（依赖注入）。"""
        if self.milvus_store is None:
            self.milvus_store = MilvusVectorStore(
                host="**********",
                port=19530,
                collection_name="tool_vectors",
                dimension=1536
            )
            await self.milvus_store.connect()
        return self.milvus_store
        
    async def get_embeddings_model(self):
        """获取嵌入模型实例。"""
        if self.embeddings_model is None:
            # 使用阿里云embedding模型
            config = {
                'model': 'text-embedding-v4',
                'dimensions': 1536,
                'instruct': '给定一个网络搜索查询，检索回答查询的相关段落',
                'max_batch_size': 10,
                'timeout': 30
            }
            api_key = "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"  # 需要配置阿里云API密钥
            self.embeddings_model = EmbeddingsFactory.create_embeddings("aliyun", config, api_key)
        return self.embeddings_model
        
    def _process_tool_data(self, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具数据，确保空值得到正确处理。"""
        # 处理空值和默认值
        processed_data = {
            "ID": tool_data.get("ID", 0) or 0,
            "name": tool_data.get("name", "") or "",
            "c_name": tool_data.get("c_name", "") or "",
            "description": tool_data.get("description", "") or "",
            "descriptionChinese": tool_data.get("descriptionChinese", "") or "",
            "fullName": tool_data.get("fullName", "") or "",
            "projectUUId": tool_data.get("projectUUId", "") or "",
            "projectId": tool_data.get("projectId", 0) or 0,
            "points": tool_data.get("points", 0) or 0,
            "is_single_call": tool_data.get("is_single_call", 0) or 0,
            "inputSchema": tool_data.get("inputSchema", {}) or {},
            "outputSchema": tool_data.get("outputSchema", {}) or {},
            "regex": tool_data.get("regex", "") or "",
        }

        # 验证必要字段
        if not processed_data["name"]:
            raise ValueError("工具名称不能为空")

        return processed_data

    async def search_similar_tools_method(
        self,
        search_request: ToolSearchRequest,
        store: Optional[MilvusVectorStore] = None,
        embeddings = None
    ) -> ApiResponse:
        """搜索相似工具的方法实现。

        Args:
            search_request: 搜索请求对象
            store: Milvus存储实例，如果为None则使用默认实例
            embeddings: 嵌入模型实例，如果为None则使用默认实例

        Returns:
            ApiResponse: 包含搜索结果的API响应

        Raises:
            Exception: 搜索过程中的任何异常
        """
        try:
            # 获取依赖实例
            if store is None:
                store = await self.get_milvus_store()
            if embeddings is None:
                embeddings = await self.get_embeddings_model()

            # 生成查询向量
            query_vector = await embeddings.aembed_query(search_request.query)

            # 处理过滤条件：如果filters为空字符串或None，则传递None
            filters = search_request.filters
            if filters == "" or filters is None:
                filters = None

            # 搜索相似工具，传递用户ID进行权限过滤
            results = await store.search_similar_tools(
                query_vector=query_vector,
                top_k=search_request.top_k,
                filters=filters,
                user_id=search_request.user_id
            )

            return ApiResponse(
                code=0,
                msg="搜索完成",
                data={
                    "query": search_request.query,
                    "results": results,
                    "count": len(results)
                }
            )

        except Exception as e:
            self.logger.error(f"搜索工具失败: {e}")
            raise Exception(f"搜索工具失败: {str(e)}")
        
    def _register_routes(self):
        """注册所有API路由。"""
        
        @self.router.post("/add", response_model=ApiResponse, summary="添加工具向量")
        async def add_tool_vector(
            tool_data: ToolCreateRequest,
            check_duplicate: bool = True,
            store: MilvusVectorStore = Depends(self.get_milvus_store),
            embeddings = Depends(self.get_embeddings_model)
        ):
            """添加工具向量。"""
            try:
                # 处理空值和默认值
                tool_dict = self._process_tool_data(tool_data.dict())

                # 生成向量
                description_text = f"{tool_dict['name']}: {tool_dict['descriptionChinese'] or tool_dict['description']}"
                vector = await embeddings.aembed_query(description_text)

                # 插入到Milvus，传递用户ID和重复检查标志
                result = await store.insert_tool(tool_dict, vector, tool_data.user_id or "admin", check_duplicate)

                if result["is_duplicate"]:
                    return ApiResponse(
                        code=0,
                        msg="发现重复向量，跳过添加",
                        data={
                            "record_id": result["record_id"],
                            "tool_id": tool_dict['ID'],
                            "user_id": tool_data.user_id or "admin",
                            "is_duplicate": True,
                            "existing_record_id": result["existing_record_id"]
                        }
                    )
                else:
                    return ApiResponse(
                        code=0,
                        msg="工具向量添加成功",
                        data={
                            "record_id": result["record_id"],
                            "tool_id": tool_dict['ID'],
                            "user_id": tool_data.user_id or "admin",
                            "is_duplicate": False
                        }
                    )

            except Exception as e:
                self.logger.error(f"添加工具向量失败: {e}")
                raise HTTPException(status_code=500, detail=f"添加工具向量失败: {str(e)}")
                
        @self.router.post("/batch_add", response_model=ApiResponse, summary="批量添加工具向量")
        async def batch_add_tool_vectors(
            request: BatchToolCreateRequest,
            check_duplicate: bool = True,
            store: MilvusVectorStore = Depends(self.get_milvus_store),
            embeddings = Depends(self.get_embeddings_model)
        ):
            """批量添加工具向量。"""
            try:
                # 处理空值和默认值
                tools_dict = [self._process_tool_data(tool.dict()) for tool in request.tools]

                # 批量生成向量
                descriptions = [
                    f"{tool_dict['name']}: {tool_dict['descriptionChinese'] or tool_dict['description']}"
                    for tool_dict in tools_dict
                ]

                vectors = []
                for desc in descriptions:
                    vector = await embeddings.aembed_query(desc)
                    vectors.append(vector)

                # 批量插入到Milvus，传递用户ID和重复检查标志
                result = await store.batch_insert_tools(tools_dict, vectors, request.user_id or "admin", check_duplicate)

                return ApiResponse(
                    code=0,
                    msg=f"批量处理 {len(request.tools)} 个工具向量：新增 {result['inserted_count']} 个，跳过重复 {result['duplicate_count']} 个",
                    data={
                        "record_ids": result["record_ids"],
                        "tool_ids": [tool_dict['ID'] for tool_dict in tools_dict],
                        "total_count": len(request.tools),
                        "inserted_count": result["inserted_count"],
                        "duplicate_count": result["duplicate_count"],
                        "duplicates": result["duplicates"],
                        "user_id": request.user_id or "admin"
                    }
                )

            except Exception as e:
                self.logger.error(f"批量添加工具向量失败: {e}")
                raise HTTPException(status_code=500, detail=f"批量添加工具向量失败: {str(e)}")
                
        @self.router.put("/update/{tool_id}", response_model=ApiResponse, summary="更新工具向量")
        async def update_tool_vector(
            tool_id: int,
            update_data: ToolUpdateRequest,
            regenerate_vector: bool = False,
            store: MilvusVectorStore = Depends(self.get_milvus_store),
            embeddings = Depends(self.get_embeddings_model)
        ):
            """更新工具向量。"""
            try:
                # 过滤掉None值
                update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
                user_id = update_data.user_id or "admin"
                
                vector = None
                if regenerate_vector and (update_data.name or update_data.description or update_data.descriptionChinese):
                    # 重新生成向量
                    existing_tool = await store.get_tool_by_id(tool_id)
                    if existing_tool:
                        name = update_data.name or existing_tool.get("name", "")
                        desc = update_data.descriptionChinese or update_data.description or existing_tool.get("description_chinese", "") or existing_tool.get("description", "")
                        description_text = f"{name}: {desc}"
                        vector = await embeddings.aembed_query(description_text)
                
                # 更新工具，传递用户ID进行权限验证
                success = await store.update_tool(tool_id, update_dict, vector, user_id)
                
                if success:
                    return ApiResponse(
                        code=0,
                        msg="工具向量更新成功",
                        data={"tool_id": tool_id, "regenerated_vector": regenerate_vector, "user_id": user_id}
                    )
                else:
                    return ApiResponse(
                        code=403,
                        msg="工具不存在或无权限操作",
                        data={"tool_id": tool_id, "user_id": user_id}
                    )
                    
            except Exception as e:
                self.logger.error(f"更新工具向量失败: {e}")
                raise HTTPException(status_code=500, detail=f"更新工具向量失败: {str(e)}")
                
        @self.router.delete("/delete/{tool_id}", response_model=ApiResponse, summary="删除工具向量")
        async def delete_tool_vector(
            tool_id: int,
            user_id: Optional[str] = None,
            store: MilvusVectorStore = Depends(self.get_milvus_store)
        ):
            """删除工具向量。"""
            try:
                # 传递用户ID进行权限验证
                success = await store.delete_tool(tool_id, user_id or "admin")
                
                if success:
                    return ApiResponse(
                        code=0,
                        msg="工具向量删除成功",
                        data={"tool_id": tool_id, "user_id": user_id or "admin"}
                    )
                else:
                    return ApiResponse(
                        code=403,
                        msg="工具不存在或无权限操作",
                        data={"tool_id": tool_id, "user_id": user_id or "admin"}
                    )
                    
            except Exception as e:
                self.logger.error(f"删除工具向量失败: {e}")
                raise HTTPException(status_code=500, detail=f"删除工具向量失败: {str(e)}")
                
        @self.router.get("/get/{tool_id}", response_model=ApiResponse, summary="获取工具信息")
        async def get_tool_vector(
            tool_id: int,
            user_id: Optional[str] = None,
            store: MilvusVectorStore = Depends(self.get_milvus_store)
        ):
            """获取工具信息。"""
            try:
                tool_data = await store.get_tool_by_id(tool_id)
                
                if tool_data:
                    # 权限检查：用户只能查看自己的数据或管理端公共数据
                    tool_user_id = tool_data.get("user_id", "admin")
                    request_user_id = user_id or "admin"
                    
                    if request_user_id != "admin" and tool_user_id != request_user_id and tool_user_id != "admin":
                        return ApiResponse(
                            code=403,
                            msg="无权限查看此工具",
                            data={"tool_id": tool_id, "user_id": request_user_id}
                        )
                    
                    # 移除向量字段以减少响应大小
                    if "vector" in tool_data:
                        tool_data["vector_dimension"] = len(tool_data["vector"])
                        del tool_data["vector"]
                        
                    return ApiResponse(
                        code=0,
                        msg="获取工具信息成功",
                        data=tool_data
                    )
                else:
                    return ApiResponse(
                        code=404,
                        msg="工具不存在",
                        data={"tool_id": tool_id}
                    )
                    
            except Exception as e:
                self.logger.error(f"获取工具信息失败: {e}")
                raise HTTPException(status_code=500, detail=f"获取工具信息失败: {str(e)}")
                
        @self.router.post("/search", response_model=ApiResponse, summary="搜索相似工具")
        async def search_similar_tools(
            search_request: ToolSearchRequest,
            store: MilvusVectorStore = Depends(self.get_milvus_store),
            embeddings = Depends(self.get_embeddings_model)
        ):
            """搜索相似工具。"""
            try:
                # 调用搜索方法
                return await self.search_similar_tools_method(search_request, store, embeddings)

            except Exception as e:
                self.logger.error(f"搜索工具失败: {e}")
                raise HTTPException(status_code=500, detail=f"搜索工具失败: {str(e)}")
                
        @self.router.get("/stats", response_model=ApiResponse, summary="获取向量库统计信息")
        async def get_vector_stats(
            user_id: Optional[str] = None,
            store: MilvusVectorStore = Depends(self.get_milvus_store)
        ):
            """获取向量库统计信息。"""
            try:
                stats = await store.get_collection_stats(user_id)
                
                return ApiResponse(
                    code=0,
                    msg="获取统计信息成功",
                    data=stats
                )
                
            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
                
        @self.router.post("/migrate_from_api", response_model=ApiResponse, summary="从API迁移数据")
        async def migrate_from_api(
            batch_size: int = 50,
            store: MilvusVectorStore = Depends(self.get_milvus_store),
            embeddings = Depends(self.get_embeddings_model)
        ):
            """从原始API迁移数据到Milvus。"""
            try:
                # 获取API数据
                api_client = ProjectToolsAPIClient()
                raw_data = await api_client.fetch_tools_data()

                self.logger.info(f"开始迁移 {len(raw_data)} 个工具")

                # 分批处理以优化性能
                migrated_count = 0
                for i in range(0, len(raw_data), batch_size):
                    batch_data = raw_data[i:i + batch_size]

                    # 生成向量
                    vectors = []
                    for tool in batch_data:
                        desc = tool.get("descriptionChinese", "") or tool.get("description", "")
                        description_text = f"{tool.get('name', '')}: {desc}"
                        vector = await embeddings.aembed_query(description_text)
                        vectors.append(vector)

                    # 批量插入，迁移时检查重复以避免重复迁移
                    result = await store.batch_insert_tools(batch_data, vectors, "admin", check_duplicate=True)
                    migrated_count += result["inserted_count"]

                    self.logger.info(f"已迁移 {migrated_count}/{len(raw_data)} 个工具")

                return ApiResponse(
                    code=0,
                    msg="数据迁移完成",
                    data={
                        "total_migrated": migrated_count,
                        "batch_size": batch_size
                    }
                )

            except Exception as e:
                self.logger.error(f"数据迁移失败: {e}")
                raise HTTPException(status_code=500, detail=f"数据迁移失败: {str(e)}")

        @self.router.delete("/clear_all", response_model=ApiResponse, summary="清除所有数据")
        async def clear_all_data(
            confirm: bool = False,
            store: MilvusVectorStore = Depends(self.get_milvus_store)
        ):
            """清除Milvus数据库中的所有数据。

            Args:
                confirm: 确认删除标志，必须为True才能执行删除操作
            """
            try:
                if not confirm:
                    return ApiResponse(
                        code=400,
                        msg="请设置confirm=true参数确认删除操作",
                        data={"warning": "此操作将删除所有向量数据，不可恢复"}
                    )

                # 获取删除前的统计信息
                stats_before = await store.get_collection_stats()
                total_before = stats_before.get("total_tools", 0)

                # 清除所有数据
                success = await store.clear_all_data()

                if success:
                    return ApiResponse(
                        code=0,
                        msg="所有数据已成功清除",
                        data={
                            "deleted_count": total_before,
                            "collection_name": store.collection_name,
                            "status": "cleared"
                        }
                    )
                else:
                    return ApiResponse(
                        code=500,
                        msg="清除数据失败",
                        data={"error": "操作未成功完成"}
                    )

            except Exception as e:
                self.logger.error(f"清除数据失败: {e}")
                raise HTTPException(status_code=500, detail=f"清除数据失败: {str(e)}")


# 创建API实例
vector_api = VectorManagementAPI()
router = vector_api.router