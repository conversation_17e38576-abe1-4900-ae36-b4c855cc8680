"""Milvus向量存储提供者，用于高性能向量存储和检索。"""

import logging
import uuid
from typing import Any, Dict, List, Optional

from pymilvus import (
    connections, 
    Collection, 
    CollectionSchema, 
    DataType, 
    FieldSchema, 
    MilvusException,
    utility
)


class MilvusVectorStore:
    """Milvus向量数据库存储提供者。"""

    def __init__(
        self, 
        host: str = "localhost", 
        port: int = 19530, 
        collection_name: str = "tool_vectors",
        dimension: int = 1536,  # 阿里云embedding维度
        username: str = "",
        password: str = ""
    ):
        """初始化Milvus连接。
        
        Args:
            host: Milvus服务器地址
            port: Milvus服务器端口
            collection_name: 集合名称
            dimension: 向量维度
            username: 用户名（可选）
            password: 密码（可选）
        """
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.dimension = dimension
        self.username = username
        self.password = password
        self.collection = None
        self.logger = logging.getLogger(__name__)
        
    async def connect(self) -> None:
        """连接到Milvus数据库。"""
        try:
            # 连接到Milvus
            connections.connect(
                alias="default",
                host=self.host,
                port=self.port,
                user=self.username,
                password=self.password
            )
            self.logger.info(f"已连接到Milvus: {self.host}:{self.port}")
            
            # 创建或获取集合
            await self._ensure_collection()
            
        except MilvusException as e:
            self.logger.error(f"连接Milvus失败: {e}")
            raise
            
    async def _ensure_collection(self) -> None:
        """确保集合存在，如果不存在则创建。"""
        try:
            if utility.has_collection(self.collection_name):
                self.collection = Collection(self.collection_name)
                self.logger.info(f"使用现有集合: {self.collection_name}")
            else:
                # 创建集合schema
                schema = self._create_schema()
                self.collection = Collection(self.collection_name, schema)
                
                # 创建索引
                await self._create_index()
                
                self.logger.info(f"创建新集合: {self.collection_name}")
                
            # 加载集合到内存
            self.collection.load()
            
        except MilvusException as e:
            self.logger.error(f"创建/加载集合失败: {e}")
            raise
            
    def _create_schema(self) -> CollectionSchema:
        """创建集合schema。"""
        fields = [
            # 主键字段 - 使用UUID
            FieldSchema(
                name="id", 
                dtype=DataType.VARCHAR, 
                is_primary=True, 
                max_length=100
            ),
            # 工具ID（API中的ID字段）
            FieldSchema(
                name="tool_id", 
                dtype=DataType.INT64, 
                description="工具的原始ID"
            ),
            # 工具名称
            FieldSchema(
                name="name", 
                dtype=DataType.VARCHAR, 
                max_length=200,
                description="工具名称"
            ),
            # 中文名称
            FieldSchema(
                name="c_name", 
                dtype=DataType.VARCHAR, 
                max_length=200,
                description="工具中文名称"
            ),
            # 英文描述
            FieldSchema(
                name="description", 
                dtype=DataType.VARCHAR, 
                max_length=5000,
                description="工具英文描述"
            ),
            # 中文描述
            FieldSchema(
                name="description_chinese", 
                dtype=DataType.VARCHAR, 
                max_length=5000,
                description="工具中文描述"
            ),
            # 完整名称（包含项目前缀）
            FieldSchema(
                name="full_name", 
                dtype=DataType.VARCHAR, 
                max_length=300,
                description="工具完整名称"
            ),
            # 项目UUID
            FieldSchema(
                name="project_uuid", 
                dtype=DataType.VARCHAR, 
                max_length=100,
                description="项目UUID"
            ),
            # 项目ID
            FieldSchema(
                name="project_id", 
                dtype=DataType.INT64,
                description="项目ID"
            ),
            # 积分
            FieldSchema(
                name="points", 
                dtype=DataType.INT64,
                description="工具积分"
            ),
            # 是否单次调用
            FieldSchema(
                name="is_single_call", 
                dtype=DataType.BOOL,
                description="是否单次调用"
            ),
            # 向量字段
            FieldSchema(
                name="vector", 
                dtype=DataType.FLOAT_VECTOR, 
                dim=self.dimension,
                description="工具向量表示"
            ),
            # 创建时间戳
            FieldSchema(
                name="created_at", 
                dtype=DataType.INT64,
                description="创建时间戳"
            ),
            # 更新时间戳
            FieldSchema(
                name="updated_at", 
                dtype=DataType.INT64,
                description="更新时间戳"
            ),
            # 用户ID字段 - 用于权限控制
            FieldSchema(
                name="user_id", 
                dtype=DataType.VARCHAR,
                max_length=100,
                description="用户ID，admin表示管理端数据"
            ),
            # 新增字段
            FieldSchema(
                name="input_schema", 
                dtype=DataType.VARCHAR,
                max_length=10000,
                description="输入参数结构"
            ),
            FieldSchema(
                name="output_schema", 
                dtype=DataType.VARCHAR,
                max_length=10000,
                description="输出参数结构"
            ),
            FieldSchema(
                name="regex", 
                dtype=DataType.VARCHAR,
                max_length=1000,
                description="正则表达式"
            )
        ]
        
        schema = CollectionSchema(
            fields=fields,
            description="工具向量存储集合",
            enable_dynamic_field=True  # 允许动态字段以便将来扩展
        )
        
        return schema
        
    async def _create_index(self) -> None:
        """为向量字段创建索引。"""
        try:
            # 创建向量索引 - 使用HNSW算法，适合高维向量检索
            index_params = {
                "metric_type": "COSINE",  # 使用余弦相似度
                "index_type": "HNSW",    # HNSW索引，性能较好
                "params": {
                    "M": 16,             # HNSW参数M，控制连接数
                    "efConstruction": 200 # 构建时的搜索参数
                }
            }
            
            self.collection.create_index(
                field_name="vector",
                index_params=index_params,
                timeout=120
            )
            
            # 为常用查询字段创建标量索引
            # 工具名称索引
            self.collection.create_index(
                field_name="name",
                index_params={"index_type": "TRIE"}
            )
            
            # 项目UUID索引
            self.collection.create_index(
                field_name="project_uuid", 
                index_params={"index_type": "TRIE"}
            )
            
            # 工具ID索引
            self.collection.create_index(
                field_name="tool_id",
                index_params={"index_type": "STL_SORT"}
            )
            
            # 用户ID索引 - 用于权限查询
            self.collection.create_index(
                field_name="user_id",
                index_params={"index_type": "TRIE"}
            )
            
            self.logger.info("成功创建索引")
            
        except MilvusException as e:
            self.logger.error(f"创建索引失败: {e}")
            raise

    async def _check_vector_exists(self, vector: List[float], user_id: str, similarity_threshold: float = 0.99) -> Optional[str]:
        """检查是否存在相同用户的相似向量。

        Args:
            vector: 要检查的向量
            user_id: 用户ID，只检查相同用户的向量
            similarity_threshold: 相似度阈值，默认0.99表示几乎相同

        Returns:
            如果存在相同用户的相似向量，返回其record_id；否则返回None
        """
        try:
            search_params = {
                "metric_type": "COSINE",
                "params": {"ef": 200}
            }

            # 搜索最相似的向量，同时过滤用户ID
            results = self.collection.search(
                data=[vector],
                anns_field="vector",
                param=search_params,
                limit=10,  # 获取更多结果以便过滤用户ID
                output_fields=["id", "user_id"],
                expr=f'user_id == "{user_id}"'  # 只搜索相同用户的向量
            )

            if results and len(results[0]) > 0:
                for hit in results[0]:
                    if hit.score >= similarity_threshold:
                        hit_user_id = hit.entity.get("user_id")
                        if hit_user_id == user_id:
                            self.logger.info(f"发现用户 {user_id} 的相似向量，相似度: {hit.score:.4f}")
                            return hit.entity.get("id")

            return None

        except MilvusException as e:
            self.logger.error(f"检查向量重复失败: {e}")
            return None

    async def insert_tool(self, tool_data: Dict[str, Any], vector: List[float], user_id: str = "admin", check_duplicate: bool = True) -> Dict[str, Any]:
        """插入单个工具数据。

        Args:
            tool_data: 工具数据字典
            vector: 工具向量
            user_id: 用户ID，默认为"admin"表示管理端数据
            check_duplicate: 是否检查重复向量，默认为True

        Returns:
            包含插入结果的字典，格式：{"record_id": str, "is_duplicate": bool, "existing_record_id": str}
        """
        import time
        import json

        # 检查是否存在重复向量（相同用户ID + 相同内容）
        if check_duplicate:
            existing_record_id = await self._check_vector_exists(vector, user_id)
            if existing_record_id:
                self.logger.info(f"跳过用户 {user_id} 的重复向量，工具: {tool_data.get('name', 'unknown')}")
                return {
                    "record_id": existing_record_id,
                    "is_duplicate": True,
                    "existing_record_id": existing_record_id
                }

        record_id = str(uuid.uuid4())
        current_time = int(time.time() * 1000)  # 毫秒时间戳

        # 处理inputSchema和outputSchema
        input_schema = tool_data.get("inputSchema", {})
        output_schema = tool_data.get("outputSchema", {})

        entity = {
            "id": record_id,
            "tool_id": tool_data.get("ID", 0),
            "name": tool_data.get("name", "") or "",
            "c_name": tool_data.get("c_name", "") or tool_data.get("cName", "") or "",
            "description": tool_data.get("description", "") or "",
            "description_chinese": tool_data.get("descriptionChinese", "") or "",
            "full_name": tool_data.get("fullName", "") or "",
            "project_uuid": tool_data.get("projectUUId", "") or "",
            "project_id": tool_data.get("projectId", 0) or 0,
            "points": tool_data.get("points", 0) or 0,
            "is_single_call": bool(tool_data.get("is_single_call", 0)),
            "vector": vector,
            "created_at": current_time,
            "updated_at": current_time,
            "user_id": user_id or "admin",
            "input_schema": json.dumps(input_schema, ensure_ascii=False) if input_schema else "",
            "output_schema": json.dumps(output_schema, ensure_ascii=False) if output_schema else "",
            "regex": tool_data.get("regex", "") or ""
        }

        try:
            result = self.collection.insert([entity])
            await self._flush()
            self.logger.debug(f"插入工具: {tool_data.get('name', 'unknown')}")
            return {
                "record_id": record_id,
                "is_duplicate": False,
                "existing_record_id": None
            }

        except MilvusException as e:
            self.logger.error(f"插入工具失败: {e}")
            raise
            
    async def batch_insert_tools(self, tools_data: List[Dict[str, Any]], vectors: List[List[float]], user_id: str = "admin", check_duplicate: bool = True) -> Dict[str, Any]:
        """批量插入工具数据。

        Args:
            tools_data: 工具数据列表
            vectors: 对应的向量列表
            user_id: 用户ID，默认为"admin"表示管理端数据
            check_duplicate: 是否检查重复向量，默认为True

        Returns:
            包含插入结果的字典，格式：{"record_ids": List[str], "duplicates": List[Dict], "inserted_count": int, "duplicate_count": int}
        """
        if len(tools_data) != len(vectors):
            raise ValueError("工具数据和向量数量不匹配")

        import time
        import json
        current_time = int(time.time() * 1000)

        entities = []
        record_ids = []
        duplicates = []

        for tool_data, vector in zip(tools_data, vectors):
            # 检查是否存在重复向量（相同用户ID + 相同内容）
            if check_duplicate:
                existing_record_id = await self._check_vector_exists(vector, user_id)
                if existing_record_id:
                    self.logger.info(f"跳过用户 {user_id} 的重复向量，工具: {tool_data.get('name', 'unknown')}")
                    duplicates.append({
                        "tool_name": tool_data.get('name', 'unknown'),
                        "existing_record_id": existing_record_id,
                        "tool_data": tool_data,
                        "user_id": user_id
                    })
                    record_ids.append(existing_record_id)
                    continue

            record_id = str(uuid.uuid4())
            record_ids.append(record_id)

            # 处理inputSchema和outputSchema
            input_schema = tool_data.get("inputSchema", {})
            output_schema = tool_data.get("outputSchema", {})

            entity = {
                "id": record_id,
                "tool_id": tool_data.get("ID", 0),
                "name": tool_data.get("name", "") or "",
                "c_name": tool_data.get("c_name", "") or tool_data.get("cName", "") or "",
                "description": tool_data.get("description", "") or "",
                "description_chinese": tool_data.get("descriptionChinese", "") or "",
                "full_name": tool_data.get("fullName", "") or "",
                "project_uuid": tool_data.get("projectUUId", "") or "",
                "project_id": tool_data.get("projectId", 0) or 0,
                "points": tool_data.get("points", 0) or 0,
                "is_single_call": bool(tool_data.get("is_single_call", 0)),
                "vector": vector,
                "created_at": current_time,
                "updated_at": current_time,
                "user_id": user_id or "admin",
                "input_schema": json.dumps(input_schema, ensure_ascii=False) if input_schema else "",
                "output_schema": json.dumps(output_schema, ensure_ascii=False) if output_schema else "",
                "regex": tool_data.get("regex", "") or ""
            }
            entities.append(entity)

        try:
            # 如果有新的实体需要插入
            if entities:
                # 转换为列式数据格式进行批量插入
                data = self._convert_to_column_format(entities)
                result = self.collection.insert(data)
                await self._flush()

                self.logger.info(f"批量插入 {len(entities)} 个工具，跳过 {len(duplicates)} 个重复")
            else:
                self.logger.info(f"所有 {len(duplicates)} 个工具都是重复的，跳过插入")

            return {
                "record_ids": record_ids,
                "duplicates": duplicates,
                "inserted_count": len(entities),
                "duplicate_count": len(duplicates)
            }

        except MilvusException as e:
            self.logger.error(f"批量插入工具失败: {e}")
            raise
            
    def _convert_to_column_format(self, entities: List[Dict[str, Any]]) -> List[List[Any]]:
        """将实体列表转换为列式格式。"""
        if not entities:
            return []
            
        # 获取字段名
        field_names = list(entities[0].keys())
        
        # 按列组织数据
        data = []
        for field_name in field_names:
            column_data = [entity[field_name] for entity in entities]
            data.append(column_data)
            
        return data
        
    async def search_similar_tools(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        filters: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似工具。
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            filters: 过滤条件
            user_id: 用户ID，用于权限控制
            
        Returns:
            相似工具列表
        """
        try:
            search_params = {
                "metric_type": "COSINE",
                "params": {"ef": 200}  # HNSW搜索参数
            }
            
            output_fields = [
                "tool_id", "name", "c_name", "description", 
                "description_chinese", "full_name", "project_uuid",
                "project_id", "points", "is_single_call", "user_id",
                "input_schema", "output_schema", "regex"
            ]
            
            # 构建权限过滤条件
            permission_filter = None
            if user_id is None:
                # 未传递user_id，只返回管理端数据（包括admin和空值）
                permission_filter = 'user_id == "admin" or user_id is null'
            else:
                # 传递了user_id，返回管理端数据 + 用户私有数据（包括admin和空值作为管理端）
                permission_filter = f'user_id == "admin" or user_id is null or user_id == "{user_id}"'
            
            # 合并过滤条件
            final_filter = permission_filter
            if filters:
                final_filter = f"({permission_filter}) and ({filters})"
            
            results = self.collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=final_filter,
                output_fields=output_fields
            )
            
            # 转换结果格式
            import json
            formatted_results = []
            for hit in results[0]:
                # 解析input_schema为JSON对象
                input_schema_str = hit.entity.get("input_schema", "")
                try:
                    parameters = json.loads(input_schema_str) if input_schema_str else {}
                except (json.JSONDecodeError, TypeError):
                    # 如果解析失败，提供默认的空对象
                    parameters = {}

                # 使用中文描述优先，如果为空则使用英文描述
                description_chinese = hit.entity.get("description_chinese", "")
                description_english = hit.entity.get("description", "")
                description = description_chinese if description_chinese else description_english

                tool_data = {
                    "name": hit.entity.get("name"),
                    "description": description,
                    "parameters": parameters,
                    "fullName": hit.entity.get("full_name"),
                    "similarity_score": hit.score
                }
                formatted_results.append(tool_data)
                
            return formatted_results
            
        except MilvusException as e:
            self.logger.error(f"搜索相似工具失败: {e}")
            raise
            
    async def check_tool_permission(self, tool_id: int, user_id: str) -> bool:
        """检查用户是否有权限操作指定工具。
        
        Args:
            tool_id: 工具ID
            user_id: 用户ID，"admin"表示管理员
            
        Returns:
            是否有权限
        """
        try:
            # 管理员有所有权限
            if user_id == "admin":
                return True
                
            # 查询工具的所有者
            expr = f"tool_id == {tool_id}"
            results = self.collection.query(
                expr=expr,
                output_fields=["user_id"]
            )
            
            if not results:
                return False  # 工具不存在
                
            tool_user_id = results[0].get("user_id")
            
            # 管理端数据（admin或空值）可以被管理员操作
            if tool_user_id in ["admin", "", None]:
                return user_id == "admin"
            
            # 用户只能操作自己的数据
            return tool_user_id == user_id
            
        except MilvusException as e:
            self.logger.error(f"权限检查失败: {e}")
            return False
            
    async def update_tool(self, tool_id: int, updated_data: Dict[str, Any], vector: Optional[List[float]] = None, user_id: str = "admin") -> bool:
        """更新工具数据。
        
        Args:
            tool_id: 工具ID
            updated_data: 更新的数据
            vector: 可选的新向量
            user_id: 用户ID，用于权限验证
            
        Returns:
            是否更新成功
        """
        try:
            # 权限检查
            if not await self.check_tool_permission(tool_id, user_id):
                self.logger.warning(f"用户 {user_id} 无权限更新工具 {tool_id}")
                return False
                
            import time
            current_time = int(time.time() * 1000)
            
            # 首先查找现有记录
            existing = await self.get_tool_by_id(tool_id)
            if not existing:
                return False
                
            # 删除旧记录
            await self.delete_tool(tool_id, user_id)
            
            # 插入更新后的记录
            merged_data = {**existing, **updated_data}
            if vector is None:
                # 如果没有提供新向量，保持原向量
                vector = existing.get("vector", [])

            # 更新时不检查重复，因为这是替换操作
            result = await self.insert_tool(merged_data, vector, user_id, check_duplicate=False)
            
            self.logger.info(f"更新工具: {tool_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新工具失败: {e}")
            return False
            
    async def delete_tool(self, tool_id: int, user_id: str = "admin") -> bool:
        """删除工具。
        
        Args:
            tool_id: 工具ID
            user_id: 用户ID，用于权限验证
            
        Returns:
            是否删除成功
        """
        try:
            # 权限检查
            if not await self.check_tool_permission(tool_id, user_id):
                self.logger.warning(f"用户 {user_id} 无权限删除工具 {tool_id}")
                return False
                
            expr = f"tool_id == {tool_id}"
            result = self.collection.delete(expr)
            await self._flush()
            
            self.logger.info(f"删除工具: {tool_id}")
            return True
            
        except MilvusException as e:
            self.logger.error(f"删除工具失败: {e}")
            return False
            
    async def get_tool_by_id(self, tool_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取工具。
        
        Args:
            tool_id: 工具ID
            
        Returns:
            工具数据或None
        """
        try:
            expr = f"tool_id == {tool_id}"
            results = self.collection.query(
                expr=expr,
                output_fields=["*"]
            )
            
            if results:
                return results[0]
            return None
            
        except MilvusException as e:
            self.logger.error(f"获取工具失败: {e}")
            return None
            
    async def get_collection_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取集合统计信息。
        
        Args:
            user_id: 用户ID，为None时返回全局统计，否则返回用户相关统计
        """
        try:
            total_stats = self.collection.num_entities
            
            if user_id is None:
                # 全局统计
                return {
                    "total_tools": total_stats,
                    "collection_name": self.collection_name,
                    "dimension": self.dimension
                }
            else:
                # 用户相关统计
                # 管理端数据统计（包括admin和空值）
                admin_expr = 'user_id == "admin" or user_id is null'
                admin_results = self.collection.query(expr=admin_expr, output_fields=["tool_id"])
                admin_count = len(admin_results)
                
                # 用户私有数据统计
                user_expr = f'user_id == "{user_id}"'
                user_results = self.collection.query(expr=user_expr, output_fields=["tool_id"])
                user_count = len(user_results)
                
                return {
                    "total_tools": total_stats,
                    "admin_tools": admin_count,
                    "user_tools": user_count,
                    "accessible_tools": admin_count + user_count,
                    "user_id": user_id,
                    "collection_name": self.collection_name,
                    "dimension": self.dimension
                }
                
        except MilvusException as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
            
    async def _flush(self) -> None:
        """刷新数据到磁盘。"""
        try:
            self.collection.flush()
        except MilvusException as e:
            self.logger.warning(f"刷新数据失败: {e}")
            
    async def clear_all_data(self) -> bool:
        """清除集合中的所有数据。

        Returns:
            是否清除成功
        """
        try:
            # 方法1: 删除集合中的所有数据（使用空的过滤条件删除所有记录）
            # 先获取所有记录的数量
            total_count = self.collection.num_entities
            self.logger.info(f"准备清除 {total_count} 条记录")

            if total_count > 0:
                # 删除所有数据 - 使用一个总是为真的表达式
                expr = "tool_id >= 0"  # 所有记录的tool_id都大于等于0
                result = self.collection.delete(expr)
                await self._flush()

                self.logger.info(f"成功清除所有数据，共删除 {total_count} 条记录")
            else:
                self.logger.info("集合中没有数据需要清除")

            return True

        except MilvusException as e:
            self.logger.error(f"清除数据失败: {e}")
            # 如果上面的方法失败，尝试删除并重建集合
            try:
                self.logger.info("尝试删除并重建集合...")
                return await self._recreate_collection()
            except Exception as recreate_error:
                self.logger.error(f"重建集合也失败: {recreate_error}")
                return False
        except Exception as e:
            self.logger.error(f"清除数据时发生未知错误: {e}")
            return False

    async def _recreate_collection(self) -> bool:
        """删除并重建集合。

        Returns:
            是否重建成功
        """
        try:
            # 释放集合
            if self.collection:
                self.collection.release()

            # 删除集合
            if utility.has_collection(self.collection_name):
                utility.drop_collection(self.collection_name)
                self.logger.info(f"已删除集合: {self.collection_name}")

            # 重新创建集合
            await self._ensure_collection()
            self.logger.info(f"已重建集合: {self.collection_name}")

            return True

        except Exception as e:
            self.logger.error(f"重建集合失败: {e}")
            return False

    async def disconnect(self) -> None:
        """断开连接。"""
        try:
            if self.collection:
                self.collection.release()
            connections.disconnect("default")
            self.logger.info("已断开Milvus连接")
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")