"""基于API的服务器，直接提供工具而无需MCP服务器进程。"""

import asyncio
import logging
from typing import Any, Dict, List

from .tool import Tool


class APIServer:
    """从API数据提供工具而无需实际MCP服务器进程的服务器。"""

    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        self.name: str = name
        self.config: Dict[str, Any] = config
        self.tools: List[Tool] = []
        self._initialized: bool = False

    async def initialize(self) -> None:
        """使用工具数据初始化API服务器。"""
        try:
            # 如果这是基于API的服务器，从配置创建工具
            if self.config.get("type") == "api":
                tool_data = {
                    "name": self.config.get("name", "unknown"),
                    "description": self.config.get("description", ""),
                    "inputSchema": self.config.get("parameters", {}),
                    "fullName": self.config.get("fullName", "")
                }

                tool = Tool(
                    name=tool_data["name"],
                    description=tool_data["description"],
                    input_schema=tool_data["inputSchema"],
                    full_name=tool_data["fullName"]
                )
                self.tools = [tool]
                self._initialized = True
                logging.info(f"API服务器 {self.name} 初始化成功")
            else:
                raise ValueError(f"服务器 {self.name} 不是API类型的服务器")

        except Exception as e:
            logging.error(f"初始化API服务器 {self.name} 时出错: {e}")
            raise

    async def list_tools(self) -> List[Tool]:
        """列出API服务器中可用的工具。

        返回:
            可用工具的列表。

        异常:
            RuntimeError: 如果服务器未初始化。
        """
        if not self._initialized:
            raise RuntimeError(f"API服务器 {self.name} 未初始化")

        return self.tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """在API服务器上执行工具。

        对于基于API的工具，这是一个模拟，返回工具描述和参数，
        因为我们没有实际的工具执行端点。

        参数:
            tool_name: 要执行的工具名称。
            arguments: 工具的参数。
            retries: 重试次数（API工具未使用）。
            delay: 重试间隔时间（秒）（API工具未使用）。

        返回:
            工具执行结果（模拟）。

        异常:
            RuntimeError: 如果服务器未初始化。
            ValueError: 如果未找到工具。
        """
        if not self._initialized:
            raise RuntimeError(f"API服务器 {self.name} 未初始化")

        # 查找工具
        tool = None
        for t in self.tools:
            if t.name == tool_name:
                tool = t
                break

        if not tool:
            raise ValueError(f"在服务器 {self.name} 中未找到工具 {tool_name}")

        # 对于API工具，我们返回模拟结果
        logging.info(f"在API服务器 {self.name} 上模拟执行 {tool_name}")

        result = {
            "tool_name": tool_name,
            "server_name": self.name,
            "description": tool.description,
            "arguments": arguments,
            "status": "simulated",
            "message": f"工具 {tool_name} 将使用参数执行: {arguments}"
        }

        logging.info(f"API工具 {tool_name} 执行成功（模拟）")
        return result

    async def cleanup(self) -> None:
        """清理API服务器资源。"""
        try:
            self.tools = []
            self._initialized = False
            logging.info(f"API服务器 {self.name} 清理成功")
        except Exception as e:
            logging.error(f"清理API服务器 {self.name} 时出错: {e}")

    def is_initialized(self) -> bool:
        """检查API服务器是否已初始化。

        返回:
            如果已初始化返回True，否则返回False。
        """
        return self._initialized

    def __repr__(self) -> str:
        """API服务器的字符串表示。"""
        status = "已初始化" if self.is_initialized() else "未初始化"
        return f"APIServer(name='{self.name}', status='{status}')"