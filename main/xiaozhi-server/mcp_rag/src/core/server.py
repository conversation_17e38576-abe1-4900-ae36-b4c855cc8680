"""MCP连接的服务器管理模块。"""

import asyncio
import logging
import os
import shutil
from contextlib import AsyncExitStack
from typing import Any, Dict, List

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from .tool import Tool


class Server:
    """管理MCP服务器连接和工具执行。"""

    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        self.name: str = name
        self.config: Dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()

    async def initialize(self) -> None:
        """初始化服务器连接。"""
        command = (
            shutil.which("npx")
            if self.config["command"] == "npx"
            else self.config["command"]
        )
        if command is None:
            raise ValueError("命令必须是有效的字符串，不能为None。")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]}
            if self.config.get("env")
            else None,
        )
        try:
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read, write = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
            logging.info(f"服务器 {self.name} 初始化成功")
        except Exception as e:
            logging.error(f"初始化服务器 {self.name} 时出错: {e}")
            await self.cleanup()
            raise

    async def list_tools(self) -> List[Tool]:
        """列出服务器中可用的工具。

        返回:
            可用工具的列表。

        异常:
            RuntimeError: 如果服务器未初始化。
        """
        if not self.session:
            raise RuntimeError(f"服务器 {self.name} 未初始化")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(
                    Tool(tool.name, tool.description, tool.inputSchema)
                    for tool in item[1]
                )

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """在服务器上执行工具，带有重试逻辑。

        参数:
            tool_name: 要执行的工具名称。
            arguments: 工具的参数。
            retries: 重试次数。
            delay: 重试间隔时间（秒）。

        返回:
            工具执行结果。

        异常:
            RuntimeError: 如果服务器未初始化。
            Exception: 如果工具执行在所有重试后仍然失败。
        """
        if not self.session:
            raise RuntimeError(f"服务器 {self.name} 未初始化")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"在服务器 {self.name} 上执行 {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)
                logging.info(f"工具 {tool_name} 执行成功")
                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    f"执行工具 {tool_name} 时出错: {e}. "
                    f"第 {attempt} 次尝试，共 {retries} 次。"
                )
                if attempt < retries:
                    logging.info(f"{delay} 秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    logging.error(f"工具 {tool_name} 达到最大重试次数。执行失败。")
                    raise

    async def cleanup(self) -> None:
        """清理服务器资源。"""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
                self.stdio_context = None
                logging.info(f"服务器 {self.name} 清理成功")
            except Exception as e:
                logging.error(f"清理服务器 {self.name} 时出错: {e}")

    def is_initialized(self) -> bool:
        """检查服务器是否已初始化。

        返回:
            如果已初始化返回True，否则返回False。
        """
        return self.session is not None

    def __repr__(self) -> str:
        """服务器的字符串表示。"""
        status = "已初始化" if self.is_initialized() else "未初始化"
        return f"Server(name='{self.name}', status='{status}')"