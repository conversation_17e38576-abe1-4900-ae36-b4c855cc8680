"""从远程服务器获取项目工具数据的API客户端。"""

import json
import httpx
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path


class ProjectToolsAPIClient:
    """用于从远程API获取项目工具数据的客户端。"""

    def __init__(self, api_url: str = "https://www.rapido.chat/api/projectTools/getAllProjectToolsList"):
        """初始化API客户端。

        参数:
            api_url: API端点URL
        """
        self.api_url = api_url
        self.logger = logging.getLogger(__name__)

    async def fetch_tools_data(self) -> List[Dict[str, Any]]:
        """从API获取工具数据。

        返回:
            工具数据字典列表

        异常:
            httpx.RequestError: 如果HTTP请求失败
            Exception: 如果API响应无效
        """
        try:
            async with httpx.AsyncClient(timeout=30) as client:
                self.logger.info(f"从 {self.api_url} 获取工具数据")
                response = await client.post(self.api_url)
                response.raise_for_status()

                data = response.json()

                # 检查响应是否具有预期的结构
                if not isinstance(data, dict) or "code" not in data or "data" not in data:
                    raise ValueError("无效的API响应格式")

                if data["code"] != 0:
                    raise ValueError(f"API返回错误代码 {data['code']}: {data.get('msg', '未知错误')}")

                tools_data = data["data"]
                if not isinstance(tools_data, list):
                    raise ValueError("API数据字段不是列表")

                self.logger.info(f"成功获取 {len(tools_data)} 个工具")
                return tools_data

        except httpx.RequestError as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"获取工具数据失败: {e}")
            raise

    def transform_to_tool_format(self, tools_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将API响应数据转换为所需的工具格式。

        参数:
            tools_data: 来自API的原始工具数据

        返回:
            转换后的工具字典列表
        """
        transformed_tools = []

        for tool in tools_data:
            try:
                # 提取必需字段并设置回退值
                name = tool.get("name", "unknown_tool")
                description = tool.get("descriptionChinese", tool.get("description", ""))
                input_schema = tool.get("inputSchema", {})

                # 创建转换后的工具对象
                transformed_tool = {
                    "name": name,
                    "description": description,
                    "parameters": input_schema,
                    "fullName": tool.get("fullName", "")
                }

                transformed_tools.append(transformed_tool)

            except Exception as e:
                self.logger.warning(f"转换工具 {tool.get('name', 'unknown')} 失败: {e}")
                continue

        self.logger.info(f"成功转换 {len(transformed_tools)} 个工具")
        return transformed_tools

    def generate_mock_server_config(self, tools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成模拟服务器配置，模仿原始servers_config.json格式。

        这用于在使用API数据的同时保持与现有系统的兼容性。

        参数:
            tools_data: 转换后的工具数据

        返回:
            模拟服务器配置字典
        """
        mock_config = {
            "mcpServers": {}
        }

        for tool in tools_data:
            server_name = tool.get("name", "unknown_tool")

            # 创建模拟服务器配置
            mock_config["mcpServers"][server_name] = {
                "name": server_name,
                "type": "api",  # 标记为基于API的
                "description": tool.get("description", ""),
                "parameters": tool.get("parameters", {}),
                "fullName": tool.get("fullName", ""),
                "disabledTools": []
            }

        return mock_config

    async def get_server_config(self) -> Dict[str, Any]:
        """通过从API获取数据并转换来获取服务器配置。

        返回:
            与现有系统兼容的服务器配置字典
        """
        try:
            # 从API获取原始数据
            raw_tools_data = await self.fetch_tools_data()

            # 转换为工具格式
            transformed_tools = self.transform_to_tool_format(raw_tools_data)

            # 生成模拟服务器配置
            server_config = self.generate_mock_server_config(transformed_tools)

            return server_config

        except Exception as e:
            self.logger.error(f"获取服务器配置失败: {e}")
            raise

    async def get_tools_list(self) -> List[Dict[str, Any]]:
        """获取转换后的工具列表。

        返回:
            所需格式的转换后工具列表
        """
        try:
            # 从API获取原始数据
            raw_tools_data = await self.fetch_tools_data()

            # 转换为工具格式
            transformed_tools = self.transform_to_tool_format(raw_tools_data)

            return transformed_tools

        except Exception as e:
            self.logger.error(f"获取工具列表失败: {e}")
            raise