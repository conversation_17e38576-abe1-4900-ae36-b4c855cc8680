"""用于集成Milvus向量存储的适配器，替换现有的向量缓存系统。"""

import logging
from typing import Any, Dict, List, Optional

from .providers.memory.milvus_vector_store import MilvusVectorStore
from ..cache.tool_vector_manager import ToolVectorManager
from ..cache.vector_cache import VectorCache


class MilvusVectorAdapter:
    """Milvus向量存储适配器，提供与现有系统兼容的接口。"""
    
    def __init__(
        self,
        milvus_host: str = "localhost",
        milvus_port: int = 19530,
        collection_name: str = "tool_vectors",
        dimension: int = 1536,
        fallback_to_cache: bool = True
    ):
        """初始化适配器。
        
        Args:
            milvus_host: Milvus服务器地址
            milvus_port: Milvus服务器端口  
            collection_name: 集合名称
            dimension: 向量维度
            fallback_to_cache: 是否在Milvus失败时回退到本地缓存
        """
        self.milvus_store = MilvusVectorStore(
            host=milvus_host,
            port=milvus_port,
            collection_name=collection_name,
            dimension=dimension
        )
        
        self.fallback_to_cache = fallback_to_cache
        self.fallback_cache = None
        self.logger = logging.getLogger(__name__)
        self.is_connected = False
        
    async def initialize(self):
        """初始化连接。"""
        try:
            await self.milvus_store.connect()
            self.is_connected = True
            self.logger.info("Milvus连接成功")
            
        except Exception as e:
            self.logger.warning(f"Milvus连接失败: {e}")
            self.is_connected = False
            
            if self.fallback_to_cache:
                self.fallback_cache = VectorCache()
                self.logger.info("使用本地缓存作为后备")
            else:
                raise
                
    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str, 
                      full_name: str = "") -> None:
        """添加工具到向量存储。"""
        
        if self.is_connected:
            try:
                # 转换为Milvus格式
                tool_data = {
                    "ID": int(tool_id) if tool_id.isdigit() else hash(tool_id) % (2**31),
                    "name": tool_name,
                    "description": tool_description,
                    "descriptionChinese": tool_description,  # 如果没有中文描述，使用英文
                    "fullName": full_name,
                    "projectUUId": "",
                    "projectId": 0,
                    "points": 0,
                    "is_single_call": 1
                }
                
                # 生成向量 - 需要嵌入模型
                # 这里假设有全局的嵌入模型实例
                from ..embeddings.factory import EmbeddingsFactory
                factory = EmbeddingsFactory()
                embeddings_model = factory.create_embeddings(provider="aliyun")
                
                description_text = f"{tool_name}: {tool_description}"
                vector = await embeddings_model.aembed_query(description_text)
                
                await self.milvus_store.insert_tool(tool_data, vector)
                return
                
            except Exception as e:
                self.logger.warning(f"Milvus添加工具失败: {e}")
                if not self.fallback_to_cache:
                    raise
                    
        # 回退到本地缓存
        if self.fallback_cache:
            # 这里需要适配原有的缓存接口
            pass  # 原有的缓存逻辑
            
    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似工具。"""
        
        if self.is_connected:
            try:
                # 生成查询向量
                from ..embeddings.factory import EmbeddingsFactory
                factory = EmbeddingsFactory()
                embeddings_model = factory.create_embeddings(provider="aliyun")
                
                query_vector = await embeddings_model.aembed_query(query)
                
                # 搜索
                results = await self.milvus_store.search_similar_tools(
                    query_vector=query_vector,
                    top_k=top_k
                )
                
                # 转换为兼容格式
                formatted_results = []
                for result in results:
                    formatted_result = {
                        "tool_id": str(result.get("tool_id", "")),
                        "similarity": result.get("similarity", 0.0),
                        "tool_data": {
                            "name": result.get("name", ""),
                            "description": result.get("description_chinese", "") or result.get("description", ""),
                            "full_description": f"{result.get('name', '')}: {result.get('description_chinese', '') or result.get('description', '')}",
                            "fullName": result.get("full_name", "")
                        },
                        "match_details": {
                            "semantic_score": result.get("similarity", 0.0),
                            "keyword_score": 0.0,  # Milvus主要基于语义搜索
                            "name_match_score": 0.0
                        },
                        "selection_method": "milvus_semantic"
                    }
                    formatted_results.append(formatted_result)
                    
                return formatted_results
                
            except Exception as e:
                self.logger.warning(f"Milvus搜索失败: {e}")
                if not self.fallback_to_cache:
                    raise
                    
        # 回退到原有逻辑
        if self.fallback_cache:
            # 这里需要实现原有的搜索逻辑
            return []
            
        return []
        
    def get_tool_count(self) -> int:
        """获取工具数量。"""
        if self.is_connected:
            try:
                import asyncio
                stats = asyncio.run(self.milvus_store.get_collection_stats())
                return stats.get("total_tools", 0)
            except Exception as e:
                self.logger.warning(f"获取Milvus统计失败: {e}")
                
        return 0
        
    async def update_tool(self, tool_id: str, updated_data: Dict[str, Any]) -> bool:
        """更新工具。"""
        if self.is_connected:
            try:
                # 转换tool_id为整数
                numeric_id = int(tool_id) if tool_id.isdigit() else hash(tool_id) % (2**31)
                return await self.milvus_store.update_tool(numeric_id, updated_data)
            except Exception as e:
                self.logger.warning(f"Milvus更新工具失败: {e}")
                
        return False
        
    async def delete_tool(self, tool_id: str) -> bool:
        """删除工具。"""
        if self.is_connected:
            try:
                # 转换tool_id为整数
                numeric_id = int(tool_id) if tool_id.isdigit() else hash(tool_id) % (2**31)
                return await self.milvus_store.delete_tool(numeric_id)
            except Exception as e:
                self.logger.warning(f"Milvus删除工具失败: {e}")
                
        return False
        
    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息。"""
        stats = {"adapter_type": "milvus", "connected": self.is_connected}
        
        if self.is_connected:
            try:
                milvus_stats = await self.milvus_store.get_collection_stats()
                stats.update(milvus_stats)
            except Exception as e:
                stats["error"] = str(e)
                
        return stats
        
    async def disconnect(self):
        """断开连接。"""
        if self.is_connected:
            await self.milvus_store.disconnect()
            self.is_connected = False


class CachedVectorStoreWithMilvus:
    """集成Milvus的缓存向量存储，替换原有的CachedVectorStore。"""
    
    def __init__(self, tool_vector_manager: ToolVectorManager, use_milvus: bool = True):
        """初始化。
        
        Args:
            tool_vector_manager: 工具向量管理器
            use_milvus: 是否使用Milvus存储
        """
        self.tool_vector_manager = tool_vector_manager
        self.use_milvus = use_milvus
        
        if use_milvus:
            self.milvus_adapter = MilvusVectorAdapter()
        else:
            self.milvus_adapter = None
            
        # 保持与原有接口的兼容性
        self.tools_data: Dict[str, Dict[str, Any]] = {}
        self.tool_vectors: Dict[str, List[float]] = {}
        self.query_cache = {}
        
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化存储。"""
        if self.milvus_adapter:
            await self.milvus_adapter.initialize()
            
    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str, 
                      full_name: str = "") -> None:
        """添加工具。"""
        
        if self.milvus_adapter and self.milvus_adapter.is_connected:
            # 使用Milvus
            await self.milvus_adapter.add_tool(tool_id, tool_name, tool_description, full_name)
        else:
            # 使用原有逻辑
            vector = await self.tool_vector_manager.get_tool_vector(tool_name, tool_description)
            
            self.tools_data[tool_id] = {
                "name": tool_name,
                "description": tool_description,
                "full_description": f"{tool_name}: {tool_description}",
                "fullName": full_name
            }
            self.tool_vectors[tool_id] = vector
            
    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似工具。"""
        
        if self.milvus_adapter and self.milvus_adapter.is_connected:
            # 使用Milvus搜索
            return await self.milvus_adapter.search_similar_tools(query, top_k)
        else:
            # 使用原有搜索逻辑
            # 这里需要实现原有的搜索算法
            return []
            
    def get_tool_count(self) -> int:
        """获取工具数量。"""
        if self.milvus_adapter and self.milvus_adapter.is_connected:
            return self.milvus_adapter.get_tool_count()
        else:
            return len(self.tools_data)
            
    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息。"""
        if self.milvus_adapter:
            return await self.milvus_adapter.get_stats()
        else:
            return {
                "adapter_type": "local_cache",
                "total_tools": len(self.tools_data)
            }