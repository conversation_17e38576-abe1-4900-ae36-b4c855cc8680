"""工具表示和格式化模块。"""

from typing import Any, Dict


class Tool:
    """表示一个工具及其属性和格式化功能。"""

    def __init__(
        self, name: str, description: str, input_schema: Dict[str, Any], full_name: str = ""
    ) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: Dict[str, Any] = input_schema
        self.full_name: str = full_name

    def format_for_llm(self) -> str:
        """为LLM格式化工具信息。

        返回:
            描述工具的格式化字符串。
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', '无描述')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (必需)"
                args_desc.append(arg_desc)

        return f"""
工具: {self.name}
描述: {self.description}
参数:
{chr(10).join(args_desc)}
"""

    def to_dict(self) -> Dict[str, Any]:
        """将工具转换为字典表示。

        返回:
            工具数据的字典形式。
        """
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema,
            "fullName": self.full_name
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Tool':
        """从字典创建Tool实例。

        参数:
            data: 字典形式的工具数据。

        返回:
            Tool实例。
        """
        return cls(
            name=data["name"],
            description=data["description"],
            input_schema=data["input_schema"],
            full_name=data.get("fullName", "")
        )