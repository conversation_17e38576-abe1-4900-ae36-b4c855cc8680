"""MCP工具检索系统的配置管理模块。"""

import json
import os
from typing import Any, Dict, Optional
from pathlib import Path
from dotenv import load_dotenv
from .api_client import ProjectToolsAPIClient


class Configuration:
    """管理MCP客户端的配置和环境变量。"""

    def __init__(self) -> None:
        """使用环境变量初始化配置。"""
        self.load_env()
        self._load_api_keys()
        self._load_embedding_configs()

    @staticmethod
    def load_env() -> None:
        """从.env文件加载环境变量。"""
        load_dotenv()

    def _load_api_keys(self) -> None:
        """仅从环境变量加载API密钥 - 无硬编码默认值。"""
        self.api_keys = {
            'aliyun': "sk-fcac337c29fe4d6f93bb9ff2ca2395d8",
            'volcengine': "935e14d7-245c-4adc-8cca-179ae0947829",
            'openai': "https://yunwu.ai/v1",
            'llm':"sk-VZh4Djx1C6lFg8QsAtn75CgMsWZOmEllIjSh4KSDR7K1mdkb"
        }

    def _load_embedding_configs(self) -> None:
        """加载嵌入配置。"""
        self.embedding_configs = {
            'aliyun': {
                'model': 'text-embedding-v4',
                'dimensions': 1024,
                'instruct': '给定一个网络搜索查询，检索回答查询的相关段落',
                'max_batch_size': 10,
                'timeout': 30
            },
            'volcengine': {
                'model': 'doubao-embedding-large-text-250515',
                'dimensions': 2048,
                'max_batch_size': 50,
                'timeout': 30
            },
            'openai': {
                'model': 'text-embedding-3-large',
                'dimensions': 3072,
                'timeout': 30
            }
        }

    @staticmethod
    def load_config(file_path: str) -> Dict[str, Any]:
        """从JSON文件加载服务器配置。

        参数:
            file_path: JSON配置文件的路径。

        返回:
            包含服务器配置的字典。

        异常:
            FileNotFoundError: 如果配置文件不存在。
            JSONDecodeError: 如果配置文件是无效的JSON。
        """
        with open(file_path, "r") as f:
            return json.load(f)

    async def load_config_from_api(self) -> Dict[str, Any]:
        """从API加载服务器配置。

        返回:
            包含服务器配置的字典。

        异常:
            Exception: 如果API请求失败。
        """
        api_client = ProjectToolsAPIClient()
        return await api_client.get_server_config()

    async def get_tools_from_api(self) -> Dict[str, Any]:
        """从API获取所需格式的工具列表。

        返回:
            包含工具列表的字典。

        异常:
            Exception: 如果API请求失败。
        """
        api_client = ProjectToolsAPIClient()
        tools_list = await api_client.get_tools_list()
        return {"tools": tools_list}

    def get_api_key(self, provider: str) -> str:
        """获取特定提供者的API密钥。

        参数:
            provider: 提供者名称（aliyun, volcengine, openai, llm）。

        返回:
            API密钥字符串。

        异常:
            ValueError: 如果未找到API密钥。
        """
        if provider not in self.api_keys:
            raise ValueError(f"未知提供者: {provider}")

        api_key = self.api_keys[provider]
        if not api_key:
            env_var_map = {
                'aliyun': 'DASHSCOPE_API_KEY',
                'volcengine': 'VOLCENGINE_API_KEY',
                'openai': 'OPENAI_API_KEY',
                'llm': 'LLM_API_KEY'
            }
            env_var = env_var_map.get(provider, f"{provider.upper()}_API_KEY")
            raise ValueError(
                f"未找到 {provider} 的API密钥。"
                f"请设置 {env_var} 环境变量。"
            )

        return api_key

    def get_embedding_config(self, provider: str) -> Dict[str, Any]:
        """获取特定提供者的嵌入配置。

        参数:
            provider: 提供者名称（aliyun, volcengine, openai）。

        返回:
            嵌入配置字典。

        异常:
            ValueError: 如果提供者不受支持。
        """
        if provider not in self.embedding_configs:
            raise ValueError(f"未知嵌入提供者: {provider}")

        return self.embedding_configs[provider].copy()

    def get_cache_dir(self, provider: str) -> str:
        """获取特定提供者的缓存目录。

        参数:
            provider: 提供者名称。

        返回:
            缓存目录路径。
        """
        cache_dirs = {
            'aliyun': '.vector_cache_aliyun',
            'volcengine': '.vector_cache_volcengine',
            'openai': '.vector_cache_openai'
        }
        return cache_dirs.get(provider, '.vector_cache')

    def validate_config(self) -> bool:
        """验证当前配置。

        返回:
            如果配置有效返回True，否则返回False。
        """
        try:
            # 检查是否至少有一个API密钥可用
            available_keys = [key for key in self.api_keys.values() if key]
            if not available_keys:
                return False

            # 检查嵌入配置是否有效
            for provider, config in self.embedding_configs.items():
                if not config.get('model'):
                    return False
                if not isinstance(config.get('dimensions'), int):
                    return False

            return True
        except Exception:
            return False

    def get_available_providers(self) -> list[str]:
        """获取具有有效API密钥的可用提供者列表。

        返回:
            提供者名称列表。
        """
        return [provider for provider, key in self.api_keys.items() if key]