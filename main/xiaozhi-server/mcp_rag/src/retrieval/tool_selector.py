"""MCP工具检索的自定义工具选择器。"""

import asyncio
import json
import logging
import httpx
from typing import Any, Dict, List, Optional

from .cached_vector_store import CachedVectorStore

# 导入重排序功能
try:
    from ..aliyun.aliyun_enhanced_retrieval import AliyunTextReranker
    RERANK_AVAILABLE = True
except ImportError:
    RERANK_AVAILABLE = False


class CustomToolSelector:
    """使用缓存向量进行工具选择的自定义工具选择器。"""

    def __init__(self, cached_store: CachedVectorStore, tool_registry: Dict[str, Any],
                 text_reranker: Optional[Any] = None) -> None:
        """使用缓存存储和工具注册表初始化。

        参数:
            cached_store: 缓存向量存储实例。
            tool_registry: 工具ID到工具实例的注册表映射。
            text_reranker: 可选的文本重排序器，用于改善结果。
        """
        self.cached_store = cached_store
        self.tool_registry = tool_registry
        self.text_reranker = text_reranker
        self.rerank_enabled = text_reranker is not None and RERANK_AVAILABLE

        if self.rerank_enabled:
            logging.info("🚀 CustomToolSelector 已初始化，支持重排序")
        else:
            logging.info("📝 CustomToolSelector 已初始化，不支持重排序")

    async def select_and_execute_tools(self, user_query: str) -> str:
        """基于用户查询选择和执行工具。

        参数:
            user_query: 用户的查询字符串

        返回:
            包含工具选择结果的格式化响应
        """
        try:
            # 搜索相似工具
            similar_tools = await self.cached_store.search_similar_tools(user_query, top_k=10)

            if not similar_tools:
                return "❌ 未找到相关工具"

            # 如果可用，应用重排序
            if self.rerank_enabled and similar_tools:
                try:
                    logging.info(f"🚀 对 {len(similar_tools)} 个工具应用重排序")

                    # 为重排序准备文档
                    documents = [
                        f"{tool_info['tool_data']['name']}: {tool_info['tool_data']['description']}"
                        for tool_info in similar_tools
                    ]

                    # 调用重排序
                    rerank_results = await self.text_reranker.rerank_documents(
                        user_query, documents, top_n=len(documents)
                    )

                    # 用重排序分数更新相似度分数
                    for rerank_result in rerank_results:
                        if rerank_result.index < len(similar_tools):
                            original_similarity = similar_tools[rerank_result.index]["similarity"]
                            similar_tools[rerank_result.index]["similarity"] = rerank_result.relevance_score
                            similar_tools[rerank_result.index]["match_details"]["rerank_score"] = rerank_result.relevance_score
                            similar_tools[rerank_result.index]["match_details"]["original_similarity"] = original_similarity
                            similar_tools[rerank_result.index]["reranked"] = True

                    # 按新的相似度分数重新排序
                    similar_tools.sort(key=lambda x: x["similarity"], reverse=True)
                    logging.info(f"🚀 重排序完成")

                except Exception as e:
                    logging.warning(f"重排序失败，使用原始结果: {e}")

            # 格式化包含工具信息的响应
            response_parts = []
            rerank_indicator = "🚀" if self.rerank_enabled else ""
            response_parts.append(f"🔍{rerank_indicator} 为查询 '{user_query}' 找到 {len(similar_tools)} 个相关工具:\n")

            for i, tool_info in enumerate(similar_tools, 1):
                tool_data = tool_info["tool_data"]
                similarity = tool_info["similarity"]
                match_details = tool_info.get("match_details", {})
                is_reranked = tool_info.get("reranked", False)

                # 将相似度分数格式化为百分比
                similarity_pct = similarity * 100

                # 添加重排序指示器
                rerank_emoji = "🚀" if is_reranked else ""
                response_parts.append(f"{i}. {rerank_emoji}**{tool_data['name']}** (相似度: {similarity_pct:.1f}%)")
                response_parts.append(f"   📝 描述: {tool_data['description']}")

                # 如果可用，添加匹配详情
                if match_details:
                    if is_reranked and "original_similarity" in match_details:
                        original_sim = match_details.get('original_similarity', 0) * 100
                        rerank_score = match_details.get('rerank_score', 0) * 100
                        response_parts.append(
                            f"   📊 重排序: 原始={original_sim:.1f}% → 重排序={rerank_score:.1f}%"
                        )
                    else:
                        semantic = match_details.get('semantic_score', 0) * 100
                        keyword = match_details.get('keyword_score', 0) * 100
                        name_match = match_details.get('name_match_score', 0) * 100
                        response_parts.append(
                            f"   📊 详细匹配: 语义={semantic:.1f}% | 关键词={keyword:.1f}% | 名称={name_match:.1f}%"
                        )

                response_parts.append("")  # 空行用于间距

            return "\n".join(response_parts)

        except Exception as e:
            logging.error(f"工具选择过程中出错: {e}")
            return f"❌ 工具选择过程中出错: {str(e)}"

    async def select_tools(self, query: str, max_tools: int = 5,
                          method: str = "hybrid") -> List[Any]:
        """基于查询使用缓存向量选择相关工具。

        参数:
            query: 搜索查询。
            max_tools: 要选择的最大工具数量。
            method: 选择方法（"hybrid", "semantic_only"）。

        返回:
            选中的工具对象列表。
        """
        # 搜索相似工具
        if method == "semantic_only":
            similar_tools = await self.cached_store.search_by_semantic_only(query, max_tools)
        else:
            similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # 返回实际的工具对象
        selected_tools = []
        for tool_info in similar_tools:
            tool_id = tool_info["tool_id"]
            if tool_id in self.tool_registry:
                selected_tools.append(self.tool_registry[tool_id])
            else:
                logging.warning(f"工具 {tool_id} 在注册表中未找到")

        logging.info(f"使用 {method} 方法选择了 {len(selected_tools)} 个工具")
        return selected_tools

    async def get_tool_similarities(self, query: str, max_tools: int = 10) -> List[Dict[str, Any]]:
        """获取工具的详细相似度信息。

        参数:
            query: 搜索查询。
            max_tools: 要分析的最大工具数量。

        返回:
            包含详细相似度信息的工具列表。
        """
        return await self.cached_store.search_similar_tools(query, max_tools)

    async def get_tools_json_format(self, query: str, max_tools: int = 10,
                                   original_tools_map: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """获取API响应所需JSON格式的工具。

        参数:
            query: 搜索查询。
            max_tools: 要返回的最大工具数量。
            original_tools_map: 从tool_id到原始工具对象的映射。

        返回:
            包含名称、描述、参数和相似度分数的所需JSON格式的工具列表。
        """
        # 搜索相似工具
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # 将工具格式化为所需的JSON结构
        formatted_tools = []
        for tool_info in similar_tools:
            tool_data = tool_info["tool_data"]
            tool_id = tool_info["tool_id"]
            similarity = tool_info["similarity"]
            match_details = tool_info.get("match_details", {})

            # 只返回 similarity_score > 0.3 的工具
            if similarity <= 0.3:
                continue

            # 如果可用，从原始工具获取参数
            parameters = {}
            if original_tools_map and tool_id in original_tools_map:
                original_tool = original_tools_map[tool_id]
                parameters = getattr(original_tool, 'input_schema', {})

            # 创建包含分数的所需格式
            formatted_tool = {
                "name": tool_data["name"],
                "description": tool_data["description"],
                "parameters": parameters,
                "fullName": tool_data.get("fullName", ""),
                "similarity_score": round(similarity, 4),
                "match_details": {
                    "semantic_score": round(match_details.get('semantic_score', 0), 4),
                    "keyword_score": round(match_details.get('keyword_score', 0), 4),
                    "name_match_score": round(match_details.get('name_match_score', 0), 4)
                }
            }

            formatted_tools.append(formatted_tool)

        return formatted_tools

    def get_available_tools_count(self) -> int:
        """获取可用工具的数量。

        返回:
            注册表中的工具数量。
        """
        return len(self.tool_registry)

    def get_cached_tools_count(self) -> int:
        """获取缓存工具的数量。

        返回:
            缓存存储中的工具数量。
        """
        return self.cached_store.get_tool_count()

    async def search_tools_by_name(self, name_pattern: str) -> List[Dict[str, Any]]:
        """按名称模式搜索工具。

        参数:
            name_pattern: 要在工具名称中匹配的模式。

        返回:
            匹配的工具列表。
        """
        all_tools = self.cached_store.list_tools()
        matching_tools = []

        name_pattern_lower = name_pattern.lower()
        for tool in all_tools:
            if name_pattern_lower in tool["name"].lower():
                matching_tools.append(tool)

        return matching_tools

    def validate_setup(self) -> Dict[str, Any]:
        """验证工具选择器设置。

        返回:
            包含验证结果的字典。
        """
        registry_count = self.get_available_tools_count()
        cached_count = self.get_cached_tools_count()

        validation_result = {
            "status": "ok" if registry_count > 0 and cached_count > 0 else "error",
            "registry_tools": registry_count,
            "cached_tools": cached_count,
            "tools_match": registry_count == cached_count,
            "issues": []
        }

        if registry_count == 0:
            validation_result["issues"].append("注册表中没有工具")

        if cached_count == 0:
            validation_result["issues"].append("缓存中没有工具")

        if registry_count != cached_count:
            validation_result["issues"].append(
                f"注册表（{registry_count}）和缓存（{cached_count}）之间不匹配"
            )

        return validation_result

