"""查询预处理器，支持多句子分析和检索优化。"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class QuerySegment:
    """查询片段数据结构。"""
    text: str
    segment_type: str  # 'sentence', 'phrase', 'keyword'
    importance: float  # 0.0 to 1.0
    keywords: List[str]
    
    
class QueryProcessor:
    """查询预处理器，负责分析和优化用户查询。"""
    
    def __init__(self):
        """初始化查询处理器。"""
        self.min_sentence_length = 5
        self.max_segments = 10
        
    def process_query(self, query: str) -> Dict[str, Any]:
        """处理查询，返回分析结果。
        
        Args:
            query: 用户输入的查询字符串
            
        Returns:
            包含分析结果的字典
        """
        # 清理查询
        cleaned_query = self._clean_query(query)
        
        # 判断查询类型
        query_type = self._determine_query_type(cleaned_query)
        
        # 根据查询类型选择处理策略
        if query_type == 'multi_sentence':
            return self._process_multi_sentence_query(cleaned_query)
        elif query_type == 'complex_phrase':
            return self._process_complex_phrase_query(cleaned_query)
        else:
            return self._process_simple_query(cleaned_query)
    
    def _clean_query(self, query: str) -> str:
        """清理查询文本。"""
        # 去除多余空格
        query = re.sub(r'\s+', ' ', query.strip())
        
        # 统一标点符号
        query = query.replace('，', ',').replace('。', '.').replace('；', ';')
        
        return query
    
    def _determine_query_type(self, query: str) -> str:
        """确定查询类型。"""
        # 统计句子分隔符数量
        sentence_delimiters = [',', '，', '。', '.', ';', '；', '和', '以及', '还有']
        delimiter_count = sum(1 for delimiter in sentence_delimiters if delimiter in query)
        
        # 查询长度
        query_length = len(query)
        
        if delimiter_count >= 2 or query_length > 50:
            return 'multi_sentence'
        elif delimiter_count >= 1 and query_length > 20:
            return 'complex_phrase'
        else:
            return 'simple'
    
    def _process_multi_sentence_query(self, query: str) -> Dict[str, Any]:
        """处理多句子查询。"""
        segments = self._split_into_segments(query)
        
        # 为每个片段分配重要性权重
        weighted_segments = self._assign_importance_weights(segments)
        
        return {
            'query_type': 'multi_sentence',
            'original_query': query,
            'segments': weighted_segments,
            'processing_strategy': 'multi_retrieval',
            'should_combine_results': True
        }
    
    def _process_complex_phrase_query(self, query: str) -> Dict[str, Any]:
        """处理复杂短语查询。"""
        # 提取关键短语
        key_phrases = self._extract_key_phrases(query)
        
        segments = [
            QuerySegment(
                text=query,
                segment_type='phrase',
                importance=0.8,
                keywords=key_phrases
            )
        ]
        
        # 如果有多个关键短语，也创建单独的片段
        if len(key_phrases) > 1:
            for phrase in key_phrases:
                segments.append(
                    QuerySegment(
                        text=phrase,
                        segment_type='keyword',
                        importance=0.6,
                        keywords=[phrase]
                    )
                )
        
        return {
            'query_type': 'complex_phrase',
            'original_query': query,
            'segments': segments,
            'processing_strategy': 'hybrid_retrieval',
            'should_combine_results': True
        }
    
    def _process_simple_query(self, query: str) -> Dict[str, Any]:
        """处理简单查询。"""
        keywords = self._extract_keywords_simple(query)
        
        segments = [
            QuerySegment(
                text=query,
                segment_type='sentence',
                importance=1.0,
                keywords=keywords
            )
        ]
        
        return {
            'query_type': 'simple',
            'original_query': query,
            'segments': segments,
            'processing_strategy': 'single_retrieval',
            'should_combine_results': False
        }
    
    def _split_into_segments(self, query: str) -> List[QuerySegment]:
        """将查询分割为语义片段。"""
        segments = []
        
        # 按标点符号分割
        delimiters = [',', '，', '。', '.', ';', '；']
        
        # 创建分割模式
        pattern = '|'.join(re.escape(delimiter) for delimiter in delimiters)
        parts = re.split(f'({pattern})', query)
        
        current_segment = ""
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
                
            if part in delimiters:
                if current_segment and len(current_segment) >= self.min_sentence_length:
                    keywords = self._extract_keywords_simple(current_segment)
                    segments.append(
                        QuerySegment(
                            text=current_segment,
                            segment_type='sentence',
                            importance=1.0,
                            keywords=keywords
                        )
                    )
                current_segment = ""
            else:
                current_segment += part
        
        # 处理最后一个片段
        if current_segment and len(current_segment) >= self.min_sentence_length:
            keywords = self._extract_keywords_simple(current_segment)
            segments.append(
                QuerySegment(
                    text=current_segment,
                    segment_type='sentence',
                    importance=1.0,
                    keywords=keywords
                )
            )
        
        # 如果没有找到合适的分割，尝试按连接词分割
        if not segments:
            segments = self._split_by_conjunctions(query)
        
        return segments
    
    def _split_by_conjunctions(self, query: str) -> List[QuerySegment]:
        """按连接词分割查询。"""
        conjunctions = ['和', '以及', '还有', '与', '或', '及']
        
        for conjunction in conjunctions:
            if conjunction in query:
                parts = query.split(conjunction)
                segments = []
                
                for part in parts:
                    part = part.strip()
                    if part and len(part) >= self.min_sentence_length:
                        keywords = self._extract_keywords_simple(part)
                        segments.append(
                            QuerySegment(
                                text=part,
                                segment_type='phrase',
                                importance=1.0,
                                keywords=keywords
                            )
                        )
                
                if segments:
                    return segments
        
        # 如果都没有找到，返回原始查询作为单个片段
        keywords = self._extract_keywords_simple(query)
        return [
            QuerySegment(
                text=query,
                segment_type='sentence',
                importance=1.0,
                keywords=keywords
            )
        ]
    
    def _assign_importance_weights(self, segments: List[QuerySegment]) -> List[QuerySegment]:
        """为片段分配重要性权重。"""
        if not segments:
            return segments
        
        # 基于长度和关键词数量分配权重
        for segment in segments:
            # 基础权重
            base_weight = 0.8
            
            # 长度权重 (较长的片段可能更重要)
            length_weight = min(len(segment.text) / 50, 0.3)
            
            # 关键词权重 (更多关键词可能更重要)
            keyword_weight = min(len(segment.keywords) / 5, 0.2)
            
            # 类型权重
            type_weight = 0.1 if segment.segment_type == 'sentence' else 0.05
            
            segment.importance = min(base_weight + length_weight + keyword_weight + type_weight, 1.0)
        
        return segments
    
    def _extract_key_phrases(self, query: str) -> List[str]:
        """提取关键短语。"""
        key_phrases = []
        
        # 常见的技术术语模式
        tech_patterns = [
            r'[\u4e00-\u9fff]*浏览器[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*计算[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*搜索[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*数据[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*网页[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*网址[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*窗口[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*标签[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*系统[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*功能[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*表达式[\u4e00-\u9fff]*',
            r'[\u4e00-\u9fff]*站点[\u4e00-\u9fff]*',
        ]
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, query)
            key_phrases.extend([match.strip() for match in matches if match.strip()])
        
        # 去重并返回
        return list(set(key_phrases))
    
    def _extract_keywords_simple(self, text: str) -> List[str]:
        """简单关键词提取。"""
        # 使用正则表达式提取中文词语和英文单词
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,}', text)
        english_words = re.findall(r'[a-zA-Z]{2,}', text)
        
        # 合并并过滤
        keywords = chinese_words + english_words
        
        # 过滤常见停用词
        stop_words = {'的', '了', '是', '在', '有', '和', '或', '与', '为', '等', '该', '这', '那', '一个', '可以', '如果', '然后'}
        keywords = [word for word in keywords if word not in stop_words and len(word) >= 2]
        
        return keywords
    
    def get_search_queries(self, processed_query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据处理结果生成搜索查询。"""
        search_queries = []
        
        strategy = processed_query.get('processing_strategy', 'single_retrieval')
        segments = processed_query.get('segments', [])
        
        if strategy == 'multi_retrieval':
            # 为每个片段创建搜索查询
            for segment in segments:
                search_queries.append({
                    'query': segment.text,
                    'weight': segment.importance,
                    'type': segment.segment_type,
                    'keywords': segment.keywords
                })
            
            # 也添加完整查询
            search_queries.append({
                'query': processed_query['original_query'],
                'weight': 0.7,
                'type': 'full_query',
                'keywords': []
            })
            
        elif strategy == 'hybrid_retrieval':
            # 主查询
            search_queries.append({
                'query': processed_query['original_query'],
                'weight': 1.0,
                'type': 'main_query',
                'keywords': []
            })
            
            # 关键词查询
            for segment in segments:
                if segment.segment_type == 'keyword':
                    search_queries.append({
                        'query': segment.text,
                        'weight': segment.importance,
                        'type': 'keyword_query',
                        'keywords': segment.keywords
                    })
        
        else:  # single_retrieval
            search_queries.append({
                'query': processed_query['original_query'],
                'weight': 1.0,
                'type': 'single_query',
                'keywords': segments[0].keywords if segments else []
            })
        
        return search_queries
    
    def should_combine_results(self, processed_query: Dict[str, Any]) -> bool:
        """判断是否需要合并结果。"""
        return processed_query.get('should_combine_results', False)
    
    def get_query_summary(self, processed_query: Dict[str, Any]) -> str:
        """获取查询处理摘要。"""
        query_type = processed_query.get('query_type', 'unknown')
        segments_count = len(processed_query.get('segments', []))
        strategy = processed_query.get('processing_strategy', 'unknown')
        
        return f"查询类型: {query_type}, 片段数: {segments_count}, 处理策略: {strategy}"