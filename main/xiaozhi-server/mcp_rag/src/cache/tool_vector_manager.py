"""生成和缓存工具嵌入的工具向量管理模块。"""

import logging
from typing import List

from .vector_cache import VectorCache


class ToolVectorManager:
    """管理工具向量生成和缓存。"""

    def __init__(self, embeddings_model, vector_cache: VectorCache) -> None:
        """使用嵌入模型和缓存初始化。

        参数:
            embeddings_model: 用于向量生成的嵌入模型。
            vector_cache: 用于存储向量的缓存实例。
        """
        self.embeddings_model = embeddings_model
        self.vector_cache = vector_cache
        self._embedding_cache = {}

    async def get_tool_vector(self, tool_name: str, tool_description: str) -> List[float]:
        """获取工具的向量，如果可用则使用缓存。

        参数:
            tool_name: 工具名称。
            tool_description: 工具描述。

        返回:
            工具的向量表示。

        异常:
            Exception: 如果向量生成失败。
        """
        # 首先检查缓存
        cached_vector = self.vector_cache.get_vector(tool_name, tool_description)
        if cached_vector is not None:
            return cached_vector

        # 生成新向量
        logging.info(f"为工具生成向量: {tool_name}")
        description_text = f"{tool_name}: {tool_description}"

        try:
            # 使用嵌入模型生成向量
            vector = await self.embeddings_model.aembed_query(description_text)

            # 缓存结果
            self.vector_cache.set_vector(tool_name, tool_description, vector)

            return vector
        except Exception as e:
            logging.error(f"为工具 {tool_name} 生成向量失败: {e}")
            raise

    async def batch_generate_vectors(self, tools_data: List[tuple]) -> dict:
        """批量为多个工具生成向量。

        参数:
            tools_data: 包含 (tool_name, tool_description) 的元组列表。

        返回:
            将工具名称映射到向量的字典。
        """
        results = {}
        failed = []

        for tool_name, tool_description in tools_data:
            try:
                vector = await self.get_tool_vector(tool_name, tool_description)
                results[tool_name] = vector
            except Exception as e:
                logging.error(f"为 {tool_name} 生成向量失败: {e}")
                failed.append(tool_name)

        if failed:
            logging.warning(f"为 {len(failed)} 个工具生成向量失败: {failed}")

        return results

    def save_cache(self) -> None:
        """将向量缓存保存到磁盘。"""
        self.vector_cache.save()

    def clear_cache(self) -> None:
        """清除向量缓存。"""
        self.vector_cache.clear()
        self._embedding_cache.clear()

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息。

        返回:
            包含缓存统计信息的字典。
        """
        return self.vector_cache.get_cache_stats()