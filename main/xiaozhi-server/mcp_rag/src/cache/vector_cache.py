"""工具向量持久化存储的向量缓存管理模块。"""

import asyncio
import hashlib
import json
import logging
import pickle
from pathlib import Path
from typing import Any, Dict, List, Optional


class VectorCache:
    """管理工具向量的持久化存储以避免重复计算。"""

    def __init__(self, cache_dir: str = ".vector_cache") -> None:
        """使用指定目录初始化向量缓存。

        参数:
            cache_dir: 缓存存储的目录路径。
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "tool_vectors.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._load_cache()

    def _load_cache(self) -> None:
        """从磁盘加载现有缓存。"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logging.info(f"已加载 {len(self._cache)} 个缓存的工具向量")
            else:
                logging.info("未找到现有向量缓存，重新开始")
        except Exception as e:
            logging.warning(f"加载向量缓存失败: {e}，重新开始")
            self._cache = {}

    def _save_cache(self) -> None:
        """将当前缓存保存到磁盘。"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache, f)

            # 保存元数据
            metadata = {
                "last_updated": asyncio.get_event_loop().time(),
                "tool_count": len(self._cache),
                "cache_version": "1.0"
            }
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logging.info(f"已保存 {len(self._cache)} 个工具向量到缓存")
        except Exception as e:
            logging.error(f"保存向量缓存失败: {e}")

    def _get_tool_hash(self, tool_name: str, tool_description: str) -> str:
        """生成工具标识的哈希值。

        参数:
            tool_name: 工具名称。
            tool_description: 工具描述。

        返回:
            工具内容的MD5哈希值。
        """
        content = f"{tool_name}:{tool_description}"
        return hashlib.md5(content.encode()).hexdigest()

    def get_vector(self, tool_name: str, tool_description: str) -> Optional[List[float]]:
        """获取工具的缓存向量。

        参数:
            tool_name: 工具名称。
            tool_description: 工具描述。

        返回:
            如果可用则返回缓存向量，否则返回None。
        """
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        if tool_hash in self._cache:
            logging.debug(f"工具缓存命中: {tool_name}")
            return self._cache[tool_hash]["vector"]
        return None

    def set_vector(self, tool_name: str, tool_description: str, vector: List[float]) -> None:
        """为工具缓存向量。

        参数:
            tool_name: 工具名称。
            tool_description: 工具描述。
            vector: 要缓存的向量。
        """
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        self._cache[tool_hash] = {
            "tool_name": tool_name,
            "tool_description": tool_description,
            "vector": vector,
            "cached_at": asyncio.get_event_loop().time()
        }
        logging.debug(f"已缓存工具向量: {tool_name}")

    def save(self) -> None:
        """保存缓存的公共方法。"""
        self._save_cache()

    def clear(self) -> None:
        """清除所有缓存的向量。"""
        self._cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        logging.info("向量缓存已清除")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息。

        返回:
            包含缓存统计信息的字典。
        """
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    metadata = json.load(f)
            else:
                metadata = {}

            return {
                "total_vectors": len(self._cache),
                "cache_size_mb": self.cache_file.stat().st_size / (1024 * 1024) if self.cache_file.exists() else 0,
                "last_updated": metadata.get("last_updated"),
                "cache_version": metadata.get("cache_version", "unknown")
            }
        except Exception as e:
            logging.error(f"获取缓存统计信息时出错: {e}")
            return {"error": str(e)}