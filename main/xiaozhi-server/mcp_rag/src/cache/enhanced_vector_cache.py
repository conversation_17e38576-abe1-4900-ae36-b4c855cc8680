"""
增强的向量缓存系统，支持多级缓存和性能监控
Enhanced Vector Cache with Multi-level Caching and Performance Monitoring
"""

import time
import json
import hashlib
import asyncio
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pickle

logger = logging.getLogger(__name__)


class EnhancedVectorCache:
    """增强的向量缓存，提供内存和磁盘两级缓存"""
    
    def __init__(self, cache_dir: str = ".vector_cache_enhanced", max_memory_items: int = 1000):
        """
        初始化增强向量缓存
        
        Args:
            cache_dir: 磁盘缓存目录
            max_memory_items: 内存缓存最大条目数
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_memory_items = max_memory_items
        
        # 内存缓存
        self.memory_cache: Dict[str, Tuple[List[float], float]] = {}  # {query: (vector, timestamp)}
        self.cache_order: List[str] = []  # LRU队列
        
        # 性能统计
        self.stats = {
            "memory_hits": 0,
            "disk_hits": 0,
            "misses": 0,
            "total_queries": 0,
            "api_call_time": 0.0,
            "cache_save_time": 0.0
        }
        
        # 预加载常用查询
        self.preload_common_queries()
        
    def preload_common_queries(self):
        """预加载常用查询的向量"""
        common_queries = [
            "播放音乐",
            "打开灯光",
            "关闭灯光", 
            "设置定时器",
            "查询天气",
            "现在几点",
            "调节音量",
            "暂停播放",
            "继续播放",
            "下一首歌"
        ]
        
        # 从磁盘加载预计算的向量
        loaded_count = 0
        for query in common_queries:
            cache_key = self._get_cache_key(query)
            disk_path = self.cache_dir / f"{cache_key}.pkl"
            if disk_path.exists():
                try:
                    with open(disk_path, 'rb') as f:
                        vector = pickle.load(f)
                    self._add_to_memory_cache(query, vector)
                    loaded_count += 1
                except Exception as e:
                    logger.warning(f"Failed to preload vector for '{query}': {e}")
                    
        logger.info(f"Preloaded {loaded_count} common query vectors")
        
    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
        
    def _add_to_memory_cache(self, query: str, vector: List[float]):
        """添加到内存缓存，使用LRU策略"""
        # 如果已存在，先移除旧的
        if query in self.memory_cache:
            self.cache_order.remove(query)
            
        # 添加到末尾
        self.memory_cache[query] = (vector, time.time())
        self.cache_order.append(query)
        
        # 检查容量
        while len(self.cache_order) > self.max_memory_items:
            oldest = self.cache_order.pop(0)
            del self.memory_cache[oldest]
            
    async def get_vector(self, query: str, compute_func=None) -> Optional[List[float]]:
        """
        获取查询向量，优先从缓存获取
        
        Args:
            query: 查询文本
            compute_func: 计算向量的异步函数
            
        Returns:
            向量或None
        """
        self.stats["total_queries"] += 1
        
        # 1. 检查内存缓存
        if query in self.memory_cache:
            self.stats["memory_hits"] += 1
            vector, _ = self.memory_cache[query]
            # 更新LRU顺序
            self.cache_order.remove(query)
            self.cache_order.append(query)
            logger.debug(f"Memory cache hit for query: {query[:30]}...")
            return vector
            
        # 2. 检查磁盘缓存
        cache_key = self._get_cache_key(query)
        disk_path = self.cache_dir / f"{cache_key}.pkl"
        
        if disk_path.exists():
            try:
                with open(disk_path, 'rb') as f:
                    vector = pickle.load(f)
                self.stats["disk_hits"] += 1
                self._add_to_memory_cache(query, vector)
                logger.debug(f"Disk cache hit for query: {query[:30]}...")
                return vector
            except Exception as e:
                logger.warning(f"Failed to load from disk cache: {e}")
                
        # 3. 缓存未命中，需要计算
        self.stats["misses"] += 1
        
        if compute_func is None:
            return None
            
        # 计算向量
        start_time = time.time()
        try:
            vector = await compute_func(query)
            self.stats["api_call_time"] += time.time() - start_time
            
            # 保存到缓存
            await self.save_vector(query, vector)
            return vector
            
        except Exception as e:
            logger.error(f"Failed to compute vector: {e}")
            return None
            
    async def save_vector(self, query: str, vector: List[float]):
        """保存向量到缓存"""
        start_time = time.time()
        
        # 1. 保存到内存
        self._add_to_memory_cache(query, vector)
        
        # 2. 异步保存到磁盘
        cache_key = self._get_cache_key(query)
        disk_path = self.cache_dir / f"{cache_key}.pkl"
        
        try:
            # 使用线程池避免阻塞
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._save_to_disk, disk_path, vector)
            
        except Exception as e:
            logger.warning(f"Failed to save to disk cache: {e}")
            
        self.stats["cache_save_time"] += time.time() - start_time
        
    def _save_to_disk(self, path: Path, vector: List[float]):
        """保存到磁盘（同步操作）"""
        with open(path, 'wb') as f:
            pickle.dump(vector, f)
            
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total = self.stats["total_queries"]
        if total == 0:
            return self.stats
            
        return {
            **self.stats,
            "memory_hit_rate": self.stats["memory_hits"] / total,
            "disk_hit_rate": self.stats["disk_hits"] / total,
            "miss_rate": self.stats["misses"] / total,
            "avg_api_call_time": self.stats["api_call_time"] / max(self.stats["misses"], 1),
            "memory_cache_size": len(self.memory_cache),
            "disk_cache_files": len(list(self.cache_dir.glob("*.pkl")))
        }
        
    def clear_memory_cache(self):
        """清空内存缓存"""
        self.memory_cache.clear()
        self.cache_order.clear()
        logger.info("Memory cache cleared")
        
    def clear_disk_cache(self):
        """清空磁盘缓存"""
        for file in self.cache_dir.glob("*.pkl"):
            try:
                file.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete cache file {file}: {e}")
        logger.info("Disk cache cleared")
        
    def print_stats(self):
        """打印缓存统计信息"""
        stats = self.get_stats()
        print("\n📊 向量缓存统计:")
        print(f"   总查询数: {stats['total_queries']}")
        print(f"   内存命中: {stats['memory_hits']} ({stats.get('memory_hit_rate', 0):.1%})")
        print(f"   磁盘命中: {stats['disk_hits']} ({stats.get('disk_hit_rate', 0):.1%})")
        print(f"   缓存未命中: {stats['misses']} ({stats.get('miss_rate', 0):.1%})")
        print(f"   平均API调用时间: {stats.get('avg_api_call_time', 0):.3f}s")
        print(f"   内存缓存大小: {stats.get('memory_cache_size', 0)}")
        print(f"   磁盘缓存文件: {stats.get('disk_cache_files', 0)}")