"""创建嵌入实例的嵌入工厂模块。"""

import logging
from typing import Any, Dict

from langchain.embeddings import init_embeddings

# 导入自定义嵌入提供者
try:
    from ..aliyun.aliyun_embeddings import create_aliyun_embeddings
    ALIYUN_AVAILABLE = True
except ImportError:
    logging.warning("阿里云嵌入不可用")
    ALIYUN_AVAILABLE = False

try:
    from ..doubao.volcengine_embeddings import create_volcengine_embeddings
    VOLCENGINE_AVAILABLE = True
except ImportError:
    logging.warning("火山引擎嵌入不可用")
    VOLCENGINE_AVAILABLE = False


class EmbeddingsFactory:
    """用于创建嵌入实例的工厂类。"""

    @staticmethod
    def create_embeddings(provider: str, config: Dict[str, Any], api_key: str):
        """为指定提供者创建嵌入实例。

        参数:
            provider: 嵌入提供者名称（aliyun, volcengine, openai）。
            config: 提供者的配置字典。
            api_key: 提供者的API密钥。

        返回:
            嵌入实例。

        异常:
            ValueError: 如果提供者不受支持或不可用。
            Exception: 如果嵌入创建失败。
        """
        try:
            if provider == "aliyun":
                return EmbeddingsFactory._create_aliyun_embeddings(config, api_key)
            elif provider == "volcengine":
                return EmbeddingsFactory._create_volcengine_embeddings(config, api_key)
            elif provider == "openai":
                return EmbeddingsFactory._create_openai_embeddings(config, api_key)
            else:
                raise ValueError(f"不支持的嵌入提供者: {provider}")
        except Exception as e:
            logging.error(f"创建 {provider} 嵌入失败: {e}")
            raise

    @staticmethod
    def _create_aliyun_embeddings(config: Dict[str, Any], api_key: str):
        """创建阿里云嵌入实例。"""
        if not ALIYUN_AVAILABLE:
            raise ValueError("阿里云嵌入不可用")

        logging.info(f"🔥 使用模型 {config.get('model')} 创建阿里云嵌入")
        return create_aliyun_embeddings(
            api_key=api_key,
            model=config.get('model', 'text-embedding-v4'),
            dimensions=config.get('dimensions', 1024),
            instruct=config.get('instruct',
                '给定一个网络搜索查询，检索回答查询的相关段落'),
            max_batch_size=config.get('max_batch_size', 10),
            timeout=config.get('timeout', 30)
        )

    @staticmethod
    def _create_volcengine_embeddings(config: Dict[str, Any], api_key: str):
        """创建火山引擎嵌入实例。"""
        if not VOLCENGINE_AVAILABLE:
            raise ValueError("火山引擎嵌入不可用")

        logging.info(f"🔄 使用模型 {config.get('model')} 创建火山引擎嵌入")
        return create_volcengine_embeddings(
            api_key=api_key,
            model=config.get('model', 'doubao-embedding-large-text-250515'),
            dimensions=config.get('dimensions', 2048),
            max_batch_size=config.get('max_batch_size', 50),
            timeout=config.get('timeout', 30)
        )

    @staticmethod
    def _create_openai_embeddings(config: Dict[str, Any], api_key: str):
        """创建OpenAI嵌入实例。"""
        model = config.get('model', 'text-embedding-3-large')
        logging.info(f"🔄 使用模型 {model} 创建OpenAI嵌入")

        # 使用LangChain的init_embeddings创建OpenAI嵌入
        return init_embeddings(f"openai:{model}")

    @staticmethod
    def get_supported_providers() -> list[str]:
        """获取支持的嵌入提供者列表。

        返回:
            支持的提供者名称列表。
        """
        providers = ["openai"]  # OpenAI通过LangChain始终可用

        if ALIYUN_AVAILABLE:
            providers.append("aliyun")

        if VOLCENGINE_AVAILABLE:
            providers.append("volcengine")

        return providers

    @staticmethod
    def create_with_fallback(preferred_provider: str, config: Dict[str, Any],
                           api_key: str, fallback_providers: list[str] = None):
        """创建带有回退支持的嵌入。

        参数:
            preferred_provider: 首选的嵌入提供者。
            config: 配置字典。
            api_key: API密钥（将为回退使用配置中的适当密钥）。
            fallback_providers: 要尝试的回退提供者列表。

        返回:
            嵌入实例和实际使用的提供者。

        异常:
            Exception: 如果所有提供者都失败。
        """
        if fallback_providers is None:
            fallback_providers = ["openai"]  # 默认回退

        providers_to_try = [preferred_provider] + fallback_providers

        for provider in providers_to_try:
            try:
                embeddings = EmbeddingsFactory.create_embeddings(provider, config, api_key)
                logging.info(f"✅ 成功创建 {provider} 嵌入")
                return embeddings, provider
            except Exception as e:
                logging.warning(f"创建 {provider} 嵌入失败: {e}")
                continue

        raise Exception(f"使用任何提供者创建嵌入都失败: {providers_to_try}")