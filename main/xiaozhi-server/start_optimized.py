#!/usr/bin/env python3
"""
优化版本的小智服务器启动脚本
Optimized XiaoZhi Server Startup Script

主要优化:
1. 连接预热和池化
2. 并行处理管道
3. TTS连接优化
4. 性能监控增强
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from ruamel.yaml import YAML

# 添加项目路径到系统路径
sys.path.insert(0, str(Path(__file__).parent))

from config.logger import setup_logging
from core.utils.connection_warmup import ConnectionWarmupManager
from core.utils.performance_monitor import global_monitor
from core.handle.parallel_pipeline import pipeline_processor
from core.utils.optimized_connection_handler import optimized_handler
from core.utils.voice_pipeline_timer import voice_timer_manager

# 导入原始app
import app

logger = setup_logging()


class OptimizedServer:
    """优化版本的服务器"""
    
    def __init__(self):
        self.warmup_manager = ConnectionWarmupManager()
        self.is_running = False
        self.performance_config = self._load_performance_config()
        
    def _load_performance_config(self) -> dict:
        """加载性能优化配置"""
        try:
            config_path = Path(__file__).parent / "config" / "performance_optimization.yml"
            if config_path.exists():
                yaml = YAML(typ='safe')
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.load(f)
                    logger.info("✅ 性能优化配置加载成功")
                    return config
            else:
                logger.warning("⚠️ 性能优化配置文件不存在，使用默认配置")
                return {}
        except Exception as e:
            logger.error(f"❌ 加载性能优化配置失败: {e}")
            return {}
        
    async def start(self):
        """启动优化服务器"""
        logger.info("🚀 启动优化版小智服务器...")
        
        # 1. 启动性能监控
        logger.info("📊 启动性能监控...")
        await self._setup_performance_monitoring()
        
        # 2. 配置连接预热
        logger.info("🔥 配置连接预热...")
        await self._setup_warmup()
        
        # 3. 启用并行处理优化
        logger.info("⚡ 启用并行处理优化...")
        await self._setup_parallel_processing()
        
        # 4. 启动原始服务器
        logger.info("✅ 启动主服务...")
        self.is_running = True
        
        # 使用原始的app.main()
        await app.main()
        
    async def _setup_performance_monitoring(self):
        """设置性能监控"""
        try:
            monitoring_config = self.performance_config.get("monitoring", {})
            
            # 设置慢查询阈值
            slow_threshold = monitoring_config.get("slow_query_threshold", 1.0)
            global_monitor.slow_query_threshold = slow_threshold
            
            # 启用详细日志
            if monitoring_config.get("enable_detailed_performance_logging", True):
                global_monitor.enable_detailed_logging = True
            
            logger.info(f"✅ 性能监控配置完成，慢查询阈值: {slow_threshold}s")
            
        except Exception as e:
            logger.error(f"❌ 性能监控设置失败: {e}")
        
    async def _setup_warmup(self):
        """设置连接预热"""
        # 从配置中读取服务信息
        from config.config_loader import load_config
        config = load_config()
        
        # 构建预热服务配置
        warmup_services = {}
        
        # ASR预热配置
        if "ASR" in config:
            selected_asr = config.get("selected_module", {}).get("ASR")
            if selected_asr and selected_asr in config["ASR"]:
                asr_config = config["ASR"][selected_asr]
                if asr_config.get("type") == "doubao_stream":
                    warmup_services["asr"] = {
                        "url": "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel",
                        "headers": {
                            "X-Api-App-Key": str(asr_config.get("appid", "")),
                            "X-Api-Access-Key": asr_config.get("access_token", ""),
                            "X-Api-Resource-Id": "volc.bigasr.sauc.duration",
                            "X-Api-Connect-Id": str(__import__('uuid').uuid4())
                        }
                    }
                    
        # TTS预热配置
        if "TTS" in config:
            selected_tts = config.get("selected_module", {}).get("TTS")
            if selected_tts and selected_tts in config["TTS"]:
                tts_config = config["TTS"][selected_tts]
                if tts_config.get("type") == "huoshan_double_stream":
                    warmup_services["tts"] = {
                        "url": tts_config.get("ws_url", "wss://openspeech.bytedance.com/api/v3/tts/bidirection"),
                        "headers": {
                            "X-Api-App-Key": str(tts_config.get("appid", "")),
                            "X-Api-Access-Key": tts_config.get("access_token", ""),
                            "X-Api-Resource-Id": tts_config.get("resource_id", "volc.service_type.10029"),
                            "X-Api-Connect-Id": str(__import__('uuid').uuid4())
                        }
                    }
                    
        # LLM预热配置
        if "LLM" in config:
            selected_llm = config.get("selected_module", {}).get("LLM")
            if selected_llm and selected_llm in config["LLM"]:
                llm_config = config["LLM"][selected_llm]
                if llm_config.get("type") == "openai":
                    warmup_services["llm"] = {
                        "url": llm_config.get("base_url", "") + "/chat/completions",
                        "headers": {
                            "Authorization": f"Bearer {llm_config.get('api_key', '')}",
                            "Content-Type": "application/json"
                        },
                        "model": llm_config.get("model_name", "")
                    }
                    
        # 向量服务预热配置
        warmup_services["vector"] = {
            "url": "https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings",
            "headers": {
                "Authorization": "Bearer sk-fcac337c29fe4d6f93bb9ff2ca2395d8",
                "Content-Type": "application/json"
            },
            "model": "text-embedding-v4"
        }
        
        # 启动预热
        if warmup_services:
            await self.warmup_manager.start(warmup_services)
            logger.info(f"✅ 已启动 {len(warmup_services)} 个服务的连接预热")
            
    async def _setup_parallel_processing(self):
        """设置并行处理优化"""
        try:
            pipeline_config = self.performance_config.get("pipeline", {})
            
            # 启用TTS预热
            if pipeline_config.get("enable_tts_warmup", True):
                logger.info("✅ TTS预热已启用")
            
            # 启用并行内存查询
            if pipeline_config.get("enable_parallel_memory", True):
                logger.info("✅ 并行内存查询已启用")
            
            # 启用并行向量检索
            if pipeline_config.get("enable_parallel_vector", True):
                logger.info("✅ 并行向量检索已启用")
            
            # 设置超时配置
            timeouts = pipeline_config.get("timeouts", {})
            logger.info(f"✅ 管道超时配置: {timeouts}")
            
        except Exception as e:
            logger.error(f"❌ 并行处理设置失败: {e}")
            
    async def stop(self):
        """停止服务器"""
        logger.info("🛑 停止优化服务器...")
        self.is_running = False
        
        # 停止预热
        await self.warmup_manager.stop()
        
        # 打印性能报告
        logger.info("📊 生成性能报告...")
        global_monitor.print_summary()
        global_monitor.save_report()
        
        # 打印并行处理器统计
        pipeline_stats = pipeline_processor.get_performance_stats()
        logger.info(f"📈 并行处理器统计: {pipeline_stats}")
        
        # 打印优化处理器统计
        optimized_handler.print_performance_summary()
        
        # 打印语音管道统计
        logger.info("🎙️ 生成语音管道统计...")
        voice_timer_manager.print_global_stats()
        
        # 打印连接预热统计
        self.warmup_manager.print_stats()
        
        logger.info("✅ 服务器已停止")
        

def signal_handler(sig, frame):
    """信号处理器"""
    logger.info(f"\n收到信号 {sig}，准备停止服务器...")
    sys.exit(0)


async def main():
    """主函数"""
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动服务器
    server = OptimizedServer()
    
    try:
        await server.start()
    except KeyboardInterrupt:
        logger.info("收到键盘中断...")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
    finally:
        await server.stop()


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动异步主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序已退出")