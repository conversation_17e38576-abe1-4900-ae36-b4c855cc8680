from mcp_rag.main_simple import query_tools_json
import asyncio

async def call_vector_search_service(query: str) :
    """
    实际调用向量检索服务，返回OpenAI function call格式的工具列表。
    适配实际接口：http://192.168.100.50:8000/query，POST，返回data.result
    """
    data = await query_tools_json(query, max_tools=5)
    print("========-----------========-----------========-----------")
    print(data)
    print("========-----------========-----------========-----------")
    return data

if __name__ == '__main__':
    asyncio.run(call_vector_search_service(query="打开微信"))