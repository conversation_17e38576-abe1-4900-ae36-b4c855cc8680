user  root;
worker_processes  4;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  300;
    client_header_timeout 180s;
    client_body_timeout 180s;
    client_max_body_size 1024M;

    gzip on;
    gzip_buffers 32 4K;
    gzip_comp_level 6;
    gzip_min_length 100;
    gzip_types application/javascript text/css text/xml image/jpeg image/gif image/png;
    gzip_disable "MSIE [1-6]\.";
    gzip_vary on;

    server {
        # 无域名访问，就用localhost
        server_name localhost;
        # 80端口
        listen  8002;

        # 转发到编译后到web目录
        location / {
			root /usr/share/nginx/html;
			try_files $uri $uri/ /index.html;
		}

        # 转发到manager-api
        location /xiaozhi/ {
            proxy_pass http://127.0.0.1:8003;
            proxy_set_header   Host   $host;
            proxy_cookie_path /manager/ /;
            proxy_set_header   Referer $http_referer;
            proxy_set_header   Cookie $http_cookie;

            proxy_connect_timeout 15;
            proxy_send_timeout 15;
            proxy_read_timeout 15;

            proxy_set_header   X-Real-IP  $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}